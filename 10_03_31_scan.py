#!/usr/bin/env python3
"""
UDS 31服务例程控制扫描工具
1. 执行UDS 10 01 (默认会话)
2. 执行UDS 10 03 (扩展会话)
3. Fuzz UDS 31服务的常用子服务
4. 记录正响应的子服务和数据
"""

import socket
import struct
import time
import sys
import os
from datetime import datetime

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class UDS31Scanner:
    """UDS 31服务扫描器"""
    
    def __init__(self, request_id, response_id, interface='can0'):
        self.request_id = int(request_id, 16) if isinstance(request_id, str) else request_id
        self.response_id = int(response_id, 16) if isinstance(response_id, str) else response_id
        self.interface = interface
        
        self.socket = None
        self.log_file = None
        
        # 存储发现的31服务
        self.successful_routines = {}  # {routine_data: response_data}
        self.failed_routines = []
        
        # UDS服务常量
        self.UDS_SERVICE_10 = 0x10  # 诊断会话控制
        self.UDS_SERVICE_31 = 0x31  # 例程控制
        self.UDS_POSITIVE_RESPONSE = 0x40  # 正响应偏移
        
        # UDS 31子服务
        self.UDS_31_START_ROUTINE = 0x01    # 启动例程
        self.UDS_31_STOP_ROUTINE = 0x02     # 停止例程
        self.UDS_31_REQUEST_RESULT = 0x03   # 请求例程结果
        
        # 常用例程标识符 (Routine Identifier)
        self.common_routine_ids = [
            # 标准例程ID
            0x0001, 0x0002, 0x0003, 0x0004, 0x0005,
            0x0010, 0x0011, 0x0012, 0x0013, 0x0014,
            0x0020, 0x0021, 0x0022, 0x0023, 0x0024,
            0x0100, 0x0101, 0x0102, 0x0103, 0x0104,
            0x0200, 0x0201, 0x0202, 0x0203, 0x0204,
            # 厂商特定例程ID
            0x1000, 0x1001, 0x1002, 0x1003, 0x1004,
            0x2000, 0x2001, 0x2002, 0x2003, 0x2004,
            0x3000, 0x3001, 0x3002, 0x3003, 0x3004,
            # 常见测试例程
            0xF010, 0xF011, 0xF012, 0xF013, 0xF014,
            0xF020, 0xF021, 0xF022, 0xF023, 0xF024,
            0xF100, 0xF101, 0xF102, 0xF103, 0xF104,
            0xF200, 0xF201, 0xF202, 0xF203, 0xF204,
            # BMS相关例程
            0xFF00, 0xFF01, 0xFF02, 0xFF03, 0xFF04,
            0xFF10, 0xFF11, 0xFF12, 0xFF13, 0xFF14,
        ]
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置接收超时
            self.socket.settimeout(1.0)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_31_scan_{self.request_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS 31服务例程控制扫描 - {datetime.now()}\n")
            self.log_file.write(f"请求CAN ID: 0x{self.request_id:03X}\n")
            self.log_file.write(f"响应CAN ID: 0x{self.response_id:03X}\n")
            self.log_file.write(f"测试例程数量: {len(self.common_routine_ids)}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=1.0):
        """接收CAN帧（只接收来自指定响应ID的帧）"""
        try:
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.1)  # 短超时用于循环检查

            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)

                    # 记录所有接收到的帧（用于调试）
                    self.log_message(f"接收到帧: {frame}", False)

                    # 只处理来自指定响应ID的帧
                    if frame.can_id == self.response_id:
                        self.socket.settimeout(original_timeout)
                        self.log_message(f"接收目标响应: {frame}", False)
                        return frame
                    else:
                        # 记录但忽略其他ID的帧
                        self.log_message(f"忽略非目标ID帧: 0x{frame.can_id:03X} (期望0x{self.response_id:03X})", False)

                except socket.timeout:
                    # 短超时是正常的，继续等待
                    continue
                except Exception as e:
                    self.socket.settimeout(original_timeout)
                    self.log_message(f"接收失败: {e}", False)
                    return None

            # 超时
            self.socket.settimeout(original_timeout)
            self.log_message(f"等待响应ID 0x{self.response_id:03X} 超时", False)
            return None

        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
            return None
    
    def send_uds_request(self, service_data):
        """发送UDS请求并等待响应"""
        # 填充到8字节
        padded_data = service_data + b'\x55' * (8 - len(service_data))
        request_frame = CANFrame(self.request_id, padded_data)
        
        if not self.send_can_frame(request_frame):
            return None
        
        # 等待响应
        return self.receive_can_frame(2.0)
    
    def execute_uds_10_01(self):
        """执行UDS 10 01 (默认会话)"""
        self.log_message("执行UDS 10 01 (默认会话)...")
        response = self.send_uds_request(b'\x02\x10\x01')
        
        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 01 成功")
            return True
        else:
            self.log_message("❌ UDS 10 01 失败")
            return False
    
    def execute_uds_10_03(self):
        """执行UDS 10 03 (扩展会话)"""
        self.log_message("执行UDS 10 03 (扩展会话)...")
        response = self.send_uds_request(b'\x02\x10\x03')
        
        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 03 成功")
            return True
        else:
            self.log_message("❌ UDS 10 03 失败")
            return False
    
    def test_routine(self, subfunction, routine_id):
        """测试单个例程"""
        # 构造UDS 31请求
        request = bytes([
            0x04,  # 长度
            self.UDS_SERVICE_31,  # 服务ID
            subfunction,  # 子功能
            (routine_id >> 8) & 0xFF,  # 例程ID高字节
            routine_id & 0xFF  # 例程ID低字节
        ])
        
        routine_desc = {
            self.UDS_31_START_ROUTINE: "启动例程",
            self.UDS_31_STOP_ROUTINE: "停止例程", 
            self.UDS_31_REQUEST_RESULT: "请求结果"
        }.get(subfunction, f"子功能0x{subfunction:02X}")
        
        self.log_message(f"测试例程 0x{routine_id:04X} - {routine_desc}...", False)
        response = self.send_uds_request(request)
        
        if response is None:
            self.log_message(f"例程 0x{routine_id:04X}: 无响应", False)
            self.failed_routines.append((subfunction, routine_id))
            return False
        
        # 检查是否为正响应
        if len(response.data) >= 2 and response.data[1] == (self.UDS_SERVICE_31 + self.UDS_POSITIVE_RESPONSE):
            data = response.data[2:]  # 响应数据
            routine_key = f"{subfunction:02X}_{routine_id:04X}"
            self.log_message(f"✅ 例程 0x{routine_id:04X} ({routine_desc}): {' '.join(f'{b:02X}' for b in data)}")
            self.successful_routines[routine_key] = {
                'subfunction': subfunction,
                'routine_id': routine_id,
                'description': routine_desc,
                'request': request,
                'response': data
            }
            return True
        
        # 检查是否为负响应
        elif len(response.data) >= 3 and response.data[1] == 0x7F and response.data[2] == self.UDS_SERVICE_31:
            nrc = response.data[3] if len(response.data) > 3 else 0x00
            self.log_message(f"❌ 例程 0x{routine_id:04X}: 负响应 NRC=0x{nrc:02X}", False)
            self.failed_routines.append((subfunction, routine_id))
            return False
        
        else:
            self.log_message(f"❌ 例程 0x{routine_id:04X}: 无效响应格式", False)
            self.failed_routines.append((subfunction, routine_id))
            return False
    
    def scan_routines(self):
        """扫描例程"""
        print(f"\n开始UDS 31服务例程扫描...")
        print(f"测试例程数量: {len(self.common_routine_ids)}")
        print("=" * 50)
        
        total_tests = len(self.common_routine_ids) * 3  # 3个子功能
        tested = 0
        
        for routine_id in self.common_routine_ids:
            # 测试三个主要子功能
            for subfunction in [self.UDS_31_START_ROUTINE, self.UDS_31_STOP_ROUTINE, self.UDS_31_REQUEST_RESULT]:
                tested += 1
                progress = (tested / total_tests) * 100
                
                # 每50个测试显示一次进度
                if tested % 50 == 0 or tested == 1:
                    print(f"\r扫描进度: {progress:5.1f}% (例程 0x{routine_id:04X})", end='', flush=True)
                
                success = self.test_routine(subfunction, routine_id)
                
                if success:
                    print()  # 换行显示成功的例程
                
                # 短暂延时避免总线拥塞
                time.sleep(0.1)
        
        print()  # 最终换行
        print(f"\n扫描完成: 发现 {len(self.successful_routines)} 个有效例程")
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 60)
        print("UDS 31服务例程扫描结果摘要")
        print("=" * 60)
        
        total_tested = len(self.successful_routines) + len(self.failed_routines)
        success_count = len(self.successful_routines)
        
        print(f"总测试例程数: {total_tested}")
        print(f"成功例程数:   {success_count}")
        print(f"失败例程数:   {len(self.failed_routines)}")
        print(f"成功率:       {success_count/total_tested*100:.2f}%" if total_tested > 0 else "成功率: 0%")
        
        if self.successful_routines:
            print(f"\n✅ 发现有效例程 ({len(self.successful_routines)}个):")
            for routine_key, routine_info in sorted(self.successful_routines.items()):
                subfunction = routine_info['subfunction']
                routine_id = routine_info['routine_id']
                description = routine_info['description']
                response_data = routine_info['response']
                
                response_str = ' '.join(f'{b:02X}' for b in response_data)
                print(f"  例程 0x{routine_id:04X} (子功能 0x{subfunction:02X} - {description}): {response_str}")
                
                # 记录到日志
                self.log_message(f"有效例程: 0x{routine_id:04X} 子功能0x{subfunction:02X} = {response_str}", False)
        else:
            print("\n❌ 未发现有效例程")
            self.log_message("未发现有效例程", False)
        
        # 记录统计信息到日志
        self.log_message(f"\n例程扫描统计:", False)
        self.log_message(f"总测试例程数: {total_tested}", False)
        self.log_message(f"成功例程数: {success_count}", False)
        self.log_message(f"失败例程数: {len(self.failed_routines)}", False)
        self.log_message(f"成功率: {success_count/total_tested*100:.2f}%" if total_tested > 0 else "成功率: 0%", False)
        
        return len(self.successful_routines) > 0
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行完整扫描"""
        print("UDS 31服务例程控制扫描工具")
        print("=" * 28)
        print(f"请求CAN ID: 0x{self.request_id:03X}")
        print(f"响应CAN ID: 0x{self.response_id:03X}")
        print(f"CAN接口: {self.interface}")
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行UDS 10 01
            if not self.execute_uds_10_01():
                return False
            
            time.sleep(0.5)
            
            # 执行UDS 10 03
            if not self.execute_uds_10_03():
                return False
            
            time.sleep(0.5)
            
            # 扫描例程
            self.scan_routines()
            
            # 分析结果
            return self.print_summary()
            
        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False
        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def parse_hex_value(value_str, name):
    """解析十六进制值"""
    try:
        if value_str.startswith('0x') or value_str.startswith('0X'):
            return int(value_str, 16)
        else:
            return int(value_str, 16)
    except ValueError:
        raise ValueError(f"无效的{name}: {value_str}")

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python3 10_03_31_scan.py <请求CAN_ID> <响应CAN_ID> [接口]")
        print("示例: python3 10_03_31_scan.py 0x715 0x795")
        print("      python3 10_03_31_scan.py 715 795 can0")
        print("说明:")
        print("  请求CAN_ID: 必需，发送UDS请求的CAN ID")
        print("  响应CAN_ID: 必需，接收UDS响应的CAN ID")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)
    
    try:
        # 解析参数
        request_id = parse_hex_value(sys.argv[1], "请求CAN ID")
        response_id = parse_hex_value(sys.argv[2], "响应CAN ID")
        
        # 验证参数范围
        if request_id < 0 or request_id > 0x7FF:
            print(f"❌ 请求CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if response_id < 0 or response_id > 0x7FF:
            print(f"❌ 响应CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        # 获取接口名称
        interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'
        
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    scanner = UDS31Scanner(request_id, response_id, interface)
    
    try:
        success = scanner.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
