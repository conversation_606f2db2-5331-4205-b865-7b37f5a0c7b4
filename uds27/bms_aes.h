#ifndef BMS_AES_H
#define BMS_AES_H

#include <stdint.h>
#include "aes/aes.h"

/*
 * BMS专用AES算法
 * 
 * 这个BMS系统使用标准AES-128 ECB加密，然后对最后4个字节
 * 应用特定的XOR后处理。
 * 
 * 后处理模式:
 * - 字节12: XOR 0xF8
 * - 字节13: XOR 0x6A
 * - 字节14: XOR 0x50
 * - 字节15: XOR 0xB0
 */

// BMS专用的XOR后处理模式
static const uint8_t BMS_XOR_PATTERN[4] = {0xF8, 0x6A, 0x50, 0xB0};

/**
 * BMS专用AES加密函数
 * 
 * @param seed 输入的16字节seed
 * @param key 输出的16字节key
 * @param aes_key AES密钥 (16字节)
 */
static inline void bms_aes_encrypt(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 标准AES ECB加密
    AES_ECB_encrypt(&ctx, key);
    
    // BMS专用后处理: 对最后4个字节应用XOR
    for (int i = 0; i < 4; i++) {
        key[12 + i] ^= BMS_XOR_PATTERN[i];
    }
}

/**
 * 验证BMS AES算法是否正确
 * 
 * @param seed 测试用的seed
 * @param expected_key 期望的key
 * @param aes_key AES密钥
 * @return 1 if 匹配, 0 if 不匹配
 */
static inline int bms_aes_verify(const uint8_t *seed, const uint8_t *expected_key, const uint8_t *aes_key) {
    uint8_t calculated_key[16];
    bms_aes_encrypt(seed, calculated_key, aes_key);
    return memcmp(calculated_key, expected_key, 16) == 0;
}

/**
 * 打印BMS AES计算过程
 */
static inline void bms_aes_debug(const uint8_t *seed, const uint8_t *aes_key, int verbose) {
    if (!verbose) return;
    
    struct AES_ctx ctx;
    uint8_t temp[16];
    
    printf("BMS AES计算过程:\n");
    printf("===============\n");
    
    printf("输入Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", seed[i]);
    }
    printf("\n");
    
    printf("AES密钥:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_key[i]);
    }
    printf("\n");
    
    // 标准AES加密
    AES_init_ctx(&ctx, aes_key);
    memcpy(temp, seed, 16);
    AES_ECB_encrypt(&ctx, temp);
    
    printf("AES结果:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", temp[i]);
    }
    printf("\n");
    
    // 应用BMS后处理
    for (int i = 0; i < 4; i++) {
        temp[12 + i] ^= BMS_XOR_PATTERN[i];
    }
    
    printf("后处理:   ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", temp[i]);
    }
    printf("\n");
    
    printf("XOR模式:  ");
    for (int i = 0; i < 12; i++) {
        printf("-- ");
    }
    for (int i = 0; i < 4; i++) {
        printf("%02X ", BMS_XOR_PATTERN[i]);
    }
    printf("\n\n");
}

#endif // BMS_AES_H
