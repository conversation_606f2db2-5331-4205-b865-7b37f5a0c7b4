#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"

// 从实际通信记录中提取的seed和key
static const uint8_t actual_seed[16] = {
    0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
    0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
};

static const uint8_t actual_key[16] = {
    0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
    0xC9, 0x40, 0x45, 0x05, 0x5A, 0x24, 0xEB, 0xBA
};

// 常见的AES密钥模式
static const uint8_t test_keys[][16] = {
    // NIST标准测试向量
    {0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
     0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c},
    
    // 全零密钥
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
     0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    
    // 全1密钥
    {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
     0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF},
    
    // 简单递增
    {0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
     0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10},
    
    // 常见BMS密钥模式1
    {0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
     0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00},
    
    // 常见BMS密钥模式2
    {0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A,
     0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5},
    
    // 可能的厂商密钥1 (基于seed的某种变换)
    {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
     0x0F, 0xED, 0xCB, 0xA9, 0x87, 0x65, 0x43, 0x21},
    
    // 可能的厂商密钥2
    {0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00, 0x11,
     0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99},
    
    // 基于实际seed的简单变换尝试
    {0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
     0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA},
    
    // seed的反转
    {0xEA, 0x3F, 0xA9, 0x82, 0x40, 0x5E, 0xD1, 0x93,
     0xF6, 0x43, 0xA9, 0x69, 0x96, 0xB2, 0x21, 0xD3}
};

void print_hex(const char* label, const uint8_t* data, int len) {
    printf("%s: ", label);
    for (int i = 0; i < len; i++) {
        printf("%02X ", data[i]);
        if (i == 7) printf(" ");
    }
    printf("\n");
}

int test_aes_key(const uint8_t* test_key, const char* key_name) {
    struct AES_ctx ctx;
    uint8_t calculated_key[16];
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, test_key);
    
    // 复制seed到缓冲区
    memcpy(calculated_key, actual_seed, 16);
    
    // 使用AES ECB模式加密
    AES_ECB_encrypt(&ctx, calculated_key);
    
    // 比较结果
    if (memcmp(calculated_key, actual_key, 16) == 0) {
        printf("✓ 找到正确的AES密钥: %s\n", key_name);
        print_hex("AES密钥", test_key, 16);
        print_hex("输入Seed", actual_seed, 16);
        print_hex("计算Key", calculated_key, 16);
        print_hex("实际Key", actual_key, 16);
        return 1;
    } else {
        printf("✗ 测试密钥: %s\n", key_name);
        print_hex("AES密钥", test_key, 16);
        print_hex("计算Key", calculated_key, 16);
        printf("\n");
        return 0;
    }
}

// 尝试简单的XOR变换
void try_xor_variants() {
    printf("尝试XOR变换...\n");
    
    uint8_t xor_key[16];
    
    // 尝试seed XOR actual_key
    for (int i = 0; i < 16; i++) {
        xor_key[i] = actual_seed[i] ^ actual_key[i];
    }
    
    if (test_aes_key(xor_key, "Seed XOR Key")) {
        return;
    }
    
    // 尝试一些常见的XOR模式
    uint8_t xor_patterns[][16] = {
        {0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
         0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55},
        {0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
         0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA},
        {0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5,
         0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5}
    };
    
    for (int p = 0; p < 3; p++) {
        for (int i = 0; i < 16; i++) {
            xor_key[i] = actual_seed[i] ^ xor_patterns[p][i];
        }
        
        char pattern_name[64];
        sprintf(pattern_name, "Seed XOR Pattern %d", p + 1);
        if (test_aes_key(xor_key, pattern_name)) {
            return;
        }
    }
}

// 尝试基于实际数据的密钥推导
void try_derived_keys() {
    printf("尝试基于实际数据的密钥推导...\n");
    
    uint8_t derived_key[16];
    
    // 方法1: 使用key作为AES密钥
    if (test_aes_key(actual_key, "实际Key作为AES密钥")) {
        return;
    }
    
    // 方法2: key的反转
    for (int i = 0; i < 16; i++) {
        derived_key[i] = actual_key[15 - i];
    }
    if (test_aes_key(derived_key, "Key反转")) {
        return;
    }
    
    // 方法3: 基于通信中的固定值
    // 注意到通信中有很多0x55填充
    memset(derived_key, 0x55, 16);
    if (test_aes_key(derived_key, "0x55填充密钥")) {
        return;
    }
}

int main() {
    printf("UDS 27服务 - AES密钥查找工具\n");
    printf("============================\n\n");
    
    printf("分析实际通信记录:\n");
    print_hex("实际Seed", actual_seed, 16);
    print_hex("实际Key ", actual_key, 16);
    printf("\n");
    
    printf("测试预定义的AES密钥...\n");
    printf("========================\n");
    
    const char* key_names[] = {
        "NIST标准测试向量",
        "全零密钥",
        "全1密钥", 
        "简单递增密钥",
        "BMS模式1",
        "BMS模式2",
        "厂商密钥1",
        "厂商密钥2",
        "Seed作为密钥",
        "Seed反转"
    };
    
    int num_keys = sizeof(test_keys) / sizeof(test_keys[0]);
    int found = 0;
    
    for (int i = 0; i < num_keys; i++) {
        if (test_aes_key(test_keys[i], key_names[i])) {
            found = 1;
            break;
        }
    }
    
    if (!found) {
        printf("\n预定义密钥都不匹配，尝试其他方法...\n");
        printf("=====================================\n");
        
        try_xor_variants();
        try_derived_keys();
    }
    
    if (!found) {
        printf("\n未找到匹配的AES密钥。\n");
        printf("可能需要:\n");
        printf("1. 更复杂的密钥推导算法\n");
        printf("2. 不是标准AES算法\n");
        printf("3. 使用了其他加密算法\n");
        printf("4. 密钥是动态生成的\n");
    }
    
    return 0;
}
