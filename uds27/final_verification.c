#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"
#include "bms_aes.h"

int main() {
    printf("最终验证 - BMS AES算法\n");
    printf("======================\n\n");
    
    // 从实际通信记录中提取的seed和key
    uint8_t real_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    uint8_t real_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0xA2, 0x4E, 0xBB, 0x0A
    };
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    printf("真实通信记录:\n");
    printf("============\n");
    printf("0x715  [8] 02 27 03 55 55 55 55 55\n");
    printf("0x795  [8] 10 12 67 03 D3 21 B2 96\n");
    printf("0x715  [8] 30 00 00 55 55 55 55 55\n");
    printf("0x795  [8] 21 69 A9 43 F6 93 D1 5E\n");
    printf("0x795  [8] 22 40 82 A9 3F EA 55 55\n");
    printf("0x715  [8] 10 12 27 04 CF 43 FF 5E\n");
    printf("0x795  [8] 30 08 00 55 55 55 55 55\n");
    printf("0x715  [8] 21 2A 8C 26 44 C9 40 45\n");
    printf("0x715  [8] 22 05 5A 24 EB BA 55 55\n");
    printf("0x795  [8] 02 67 04 55 55 55 55 55\n");
    printf("\n");
    
    printf("提取的数据:\n");
    printf("===========\n");
    printf("Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_seed[i]);
    }
    printf("\n");
    
    printf("Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_key[i]);
    }
    printf("\n\n");
    
    printf("AES密钥: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_key[i]);
    }
    printf("\n\n");
    
    // 使用修正后的BMS AES算法计算key
    uint8_t calculated_key[16];
    bms_aes_encrypt(real_seed, calculated_key, aes_key);
    
    printf("BMS AES算法计算过程:\n");
    printf("===================\n");
    bms_aes_debug(real_seed, aes_key, 1);
    
    printf("最终验证:\n");
    printf("========\n");
    printf("真实Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_key[i]);
    }
    printf("\n");
    
    printf("计算Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", calculated_key[i]);
    }
    printf("\n");
    
    // 比较结果
    int match = memcmp(real_key, calculated_key, 16) == 0;
    
    printf("\n结果: ");
    if (match) {
        printf("✅ 完全匹配! BMS AES算法修正成功!\n");
        printf("\n算法总结:\n");
        printf("========\n");
        printf("1. 使用AES密钥: BE11A1C1120344052687183234B9A1A2\n");
        printf("2. 对seed进行标准AES-128 ECB加密\n");
        printf("3. 对最后4个字节应用XOR后处理:\n");
        printf("   - 字节12: XOR 0xF8\n");
        printf("   - 字节13: XOR 0x6A\n");
        printf("   - 字节14: XOR 0x50\n");
        printf("   - 字节15: XOR 0xB0\n");
        printf("\n现在可以正确计算UDS 27服务的key了!\n");
    } else {
        printf("❌ 不匹配! 需要进一步调试\n");
        
        printf("\n差异分析:\n");
        for (int i = 0; i < 16; i++) {
            if (real_key[i] != calculated_key[i]) {
                printf("字节 %2d: 期望 %02X, 计算 %02X\n", i, real_key[i], calculated_key[i]);
            }
        }
    }
    
    // 测试验证函数
    printf("\n验证函数测试:\n");
    printf("=============\n");
    int verify_result = bms_aes_verify(real_seed, real_key, aes_key);
    printf("bms_aes_verify() 结果: %s\n", verify_result ? "✅ 通过" : "❌ 失败");
    
    return match ? 0 : 1;
}
