#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <ifaddrs.h>
#include "aes/aes.h"
#include "bms_aes.h"

#define CAN_INTERFACE "can0"
#define UDS_REQUEST_ID_1 0x715  // 物理地址 (主要)
#define UDS_REQUEST_ID_2 0x7DF  // 功能地址 (备用)
#define UDS_RESPONSE_ID 0x795
#define UDS_SERVICE_27 0x27

// 真实BMS系统的AES密钥 (从实际通信记录获得)
static const uint8_t sim_aes_key[16] = {
    0xBE, 0x11, 0xA1, 0xC1, 0x12, 0x03, 0x44, 0x05,
    0x26, 0x87, 0x18, 0x32, 0x34, 0xB9, 0xA1, 0xA2
};

// 当前的seed
static uint8_t current_seed[16];
static int seed_valid = 0;

typedef struct {
    int socket;
    struct sockaddr_can addr;
} can_context_t;

// 初始化CAN接口
int init_can(can_context_t *ctx, const char *interface) {
    struct ifreq ifr;
    
    ctx->socket = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (ctx->socket < 0) {
        perror("socket");
        return -1;
    }
    
    strcpy(ifr.ifr_name, interface);
    if (ioctl(ctx->socket, SIOCGIFINDEX, &ifr) < 0) {
        perror("ioctl SIOCGIFINDEX");
        close(ctx->socket);
        return -1;
    }
    
    ctx->addr.can_family = AF_CAN;
    ctx->addr.can_ifindex = ifr.ifr_ifindex;
    
    if (bind(ctx->socket, (struct sockaddr *)&ctx->addr, sizeof(ctx->addr)) < 0) {
        perror("bind");
        close(ctx->socket);
        return -1;
    }
    
    return 0;
}

// 发送CAN帧
int send_can_frame(can_context_t *ctx, uint32_t id, uint8_t *data, uint8_t len) {
    struct can_frame frame;
    
    frame.can_id = id;
    frame.can_dlc = len;
    memcpy(frame.data, data, len);
    
    if (write(ctx->socket, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        perror("write");
        return -1;
    }
    
    printf("发送: can0  %03X   [%d] ", id, len);
    for (int i = 0; i < len; i++) {
        printf(" %02X", data[i]);
    }
    printf("\n");
    
    return 0;
}

// 接收CAN帧
int receive_can_frame(can_context_t *ctx, struct can_frame *frame, int timeout_ms) {
    fd_set readfds;
    struct timeval timeout;
    
    FD_ZERO(&readfds);
    FD_SET(ctx->socket, &readfds);
    
    timeout.tv_sec = timeout_ms / 1000;
    timeout.tv_usec = (timeout_ms % 1000) * 1000;
    
    int ret = select(ctx->socket + 1, &readfds, NULL, NULL, &timeout);
    if (ret <= 0) {
        return -1; // 超时或错误
    }
    
    if (read(ctx->socket, frame, sizeof(struct can_frame)) < 0) {
        perror("read");
        return -1;
    }
    
    printf("接收: can0  %03X   [%d] ", frame->can_id, frame->can_dlc);
    for (int i = 0; i < frame->can_dlc; i++) {
        printf(" %02X", frame->data[i]);
    }
    printf("\n");
    
    return 0;
}

// 生成随机seed
void generate_seed(uint8_t *seed) {
    for (int i = 0; i < 16; i++) {
        seed[i] = rand() & 0xFF;
    }
}

// 计算正确的key (使用BMS专用算法)
void calculate_correct_key(const uint8_t *seed, uint8_t *key) {
    bms_aes_encrypt(seed, key, sim_aes_key);
}

// 处理UDS 27 03请求 (请求seed)
void handle_request_seed(can_context_t *ctx) {
    // 生成新的seed
    generate_seed(current_seed);
    seed_valid = 1;
    
    printf("生成新的seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", current_seed[i]);
    }
    printf("\n");
    
    // 发送多帧响应
    uint8_t first_frame[8] = {0x10, 0x12, 0x67, 0x03};
    memcpy(&first_frame[4], current_seed, 4);
    send_can_frame(ctx, UDS_RESPONSE_ID, first_frame, 8);
    
    // 等待流控制帧
    struct can_frame flow_control;
    if (receive_can_frame(ctx, &flow_control, 1000) >= 0) {
        if (flow_control.data[0] == 0x30) {
            // 发送第二帧
            uint8_t second_frame[8] = {0x21};
            memcpy(&second_frame[1], &current_seed[4], 7);
            send_can_frame(ctx, UDS_RESPONSE_ID, second_frame, 8);
            
            // 发送第三帧
            uint8_t third_frame[8] = {0x22};
            memcpy(&third_frame[1], &current_seed[11], 5);
            // 填充剩余字节
            third_frame[6] = 0x55;
            third_frame[7] = 0x55;
            send_can_frame(ctx, UDS_RESPONSE_ID, third_frame, 8);
        }
    }
}

// 处理UDS 27 04请求 (发送key)
void handle_send_key(can_context_t *ctx, const uint8_t *received_key) {
    if (!seed_valid) {
        // 发送错误响应 - 请求序列错误
        uint8_t error_response[4] = {0x03, 0x7F, 0x27, 0x24};
        send_can_frame(ctx, UDS_RESPONSE_ID, error_response, 4);
        return;
    }
    
    // 计算正确的key
    uint8_t correct_key[16];
    calculate_correct_key(current_seed, correct_key);
    
    printf("期望的key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", correct_key[i]);
    }
    printf("\n");
    
    printf("接收的key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", received_key[i]);
    }
    printf("\n");
    
    // 比较key
    if (memcmp(received_key, correct_key, 16) == 0) {
        // Key正确 - 发送正面响应
        uint8_t positive_response[3] = {0x02, 0x67, 0x04};
        send_can_frame(ctx, UDS_RESPONSE_ID, positive_response, 3);
        printf("Key验证成功!\n");
        seed_valid = 0; // 重置seed状态
    } else {
        // Key错误 - 发送错误响应
        uint8_t error_response[4] = {0x03, 0x7F, 0x27, 0x35};
        send_can_frame(ctx, UDS_RESPONSE_ID, error_response, 4);
        printf("Key验证失败!\n");
    }
}

// 处理多帧key接收
int receive_multiframe_key(can_context_t *ctx, uint8_t *key) {
    struct can_frame frame;
    int received_bytes = 0;
    int total_length = 0;
    
    // 接收第一帧
    if (receive_can_frame(ctx, &frame, 1000) < 0) {
        return -1;
    }
    
    if (frame.data[0] == 0x10) {
        total_length = frame.data[1];
        if (total_length != 0x12) { // 应该是18字节 (2字节服务ID + 16字节key)
            return -1;
        }
        
        // 复制第一帧的数据 (跳过前4个字节: 10 12 27 04)
        memcpy(key, &frame.data[5], 3);
        received_bytes = 3;
        
        // 发送流控制帧
        uint8_t flow_control[3] = {0x30, 0x08, 0x00};
        send_can_frame(ctx, UDS_RESPONSE_ID, flow_control, 3);
        
        // 接收后续帧
        while (received_bytes < 16) {
            if (receive_can_frame(ctx, &frame, 1000) < 0) {
                return -1;
            }
            
            if ((frame.data[0] & 0xF0) == 0x20) {
                int copy_bytes = (16 - received_bytes > frame.can_dlc - 1) ? 
                                frame.can_dlc - 1 : 16 - received_bytes;
                memcpy(&key[received_bytes], &frame.data[1], copy_bytes);
                received_bytes += copy_bytes;
            }
        }
        
        return 0;
    }
    
    return -1;
}

int main() {
    can_context_t can_ctx;
    struct can_frame frame;
    uint8_t received_key[16];
    
    printf("UDS 27服务 CAN模拟器\n");
    printf("===================\n");
    
    // 初始化随机数生成器
    srand(time(NULL));
    
    // 初始化CAN接口
    if (init_can(&can_ctx, CAN_INTERFACE) < 0) {
        printf("初始化CAN接口失败\n");
        return -1;
    }
    
    printf("CAN模拟器启动，监听地址: 0x%03X, 0x%03X\n", UDS_REQUEST_ID_1, UDS_REQUEST_ID_2);
    printf("响应地址: 0x%03X\n", UDS_RESPONSE_ID);
    printf("按Ctrl+C退出\n\n");
    
    while (1) {
        // 接收CAN帧
        if (receive_can_frame(&can_ctx, &frame, 100) >= 0) {
            // 检查是否是UDS请求
            if ((frame.can_id == UDS_REQUEST_ID_1 || frame.can_id == UDS_REQUEST_ID_2) &&
                frame.can_dlc >= 3 && frame.data[1] == UDS_SERVICE_27) {
                
                if (frame.data[2] == 0x03) {
                    // 请求seed
                    printf("收到请求seed命令\n");
                    handle_request_seed(&can_ctx);
                } else if (frame.data[2] == 0x04) {
                    // 发送key
                    printf("收到发送key命令\n");
                    if (receive_multiframe_key(&can_ctx, received_key) == 0) {
                        handle_send_key(&can_ctx, received_key);
                    }
                }
            }
        }
    }
    
    close(can_ctx.socket);
    return 0;
}
