#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"

int main() {
    printf("验证真实BMS通信数据\n");
    printf("===================\n\n");
    
    // 从实际通信记录中提取的seed和key
    uint8_t real_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    uint8_t real_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0xA2, 0x4E, 0xBB, 0xA
    };
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    printf("真实通信记录数据:\n");
    printf("================\n");
    printf("Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_seed[i]);
    }
    printf("\n");
    
    printf("Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_key[i]);
    }
    printf("\n\n");
    
    printf("AES密钥: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_key[i]);
    }
    printf("\n\n");
    
    // 使用AES算法计算key
    uint8_t calculated_key[16];
    struct AES_ctx ctx;
    
    AES_init_ctx(&ctx, aes_key);
    memcpy(calculated_key, real_seed, 16);
    AES_ECB_encrypt(&ctx, calculated_key);
    
    printf("计算结果:\n");
    printf("========\n");
    printf("计算的Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", calculated_key[i]);
    }
    printf("\n");
    
    // 比较结果
    int match = memcmp(real_key, calculated_key, 16) == 0;
    
    printf("\n验证结果:\n");
    printf("========\n");
    if (match) {
        printf("✅ 成功! 计算的key与真实key完全匹配!\n");
        printf("AES密钥配置正确: BE11A1C1120344052687183234B9A1A2\n");
    } else {
        printf("❌ 失败! 计算的key与真实key不匹配\n");
        printf("需要检查AES密钥或算法实现\n");
        
        printf("\n差异分析:\n");
        for (int i = 0; i < 16; i++) {
            if (real_key[i] != calculated_key[i]) {
                printf("字节 %2d: 期望 %02X, 计算 %02X\n", i, real_key[i], calculated_key[i]);
            }
        }
    }
    
    printf("\n通信记录分析:\n");
    printf("============\n");
    printf("请求seed:  0x715 [8] 02 27 03 55 55 55 55 55\n");
    printf("响应seed:  多帧传输, seed = D321B29669A943F693D15E4082A93FEA\n");
    printf("发送key:   多帧传输, key = CF43FF5E2A8C2644C9404505A24EBBA\n");
    printf("响应成功:  0x795 [8] 02 67 04 55 55 55 55 55\n");
    
    return match ? 0 : 1;
}
