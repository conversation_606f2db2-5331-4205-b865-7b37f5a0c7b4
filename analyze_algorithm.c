#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"

// 尝试不同的后处理算法
void try_xor_pattern(uint8_t *data, const char *pattern_name, uint8_t xor_val) {
    printf("尝试 %s (XOR 0x%02X):\n", pattern_name, xor_val);
    for (int i = 12; i < 16; i++) {
        data[i] ^= xor_val;
    }
    printf("结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", data[i]);
    }
    printf("\n\n");
}

void try_byte_swap(uint8_t *data, const char *pattern_name) {
    printf("尝试 %s:\n", pattern_name);
    // 交换最后4个字节的顺序
    uint8_t temp;
    temp = data[12]; data[12] = data[15]; data[15] = temp;
    temp = data[13]; data[13] = data[14]; data[14] = temp;
    
    printf("结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", data[i]);
    }
    printf("\n\n");
}

void try_bit_inversion(uint8_t *data, const char *pattern_name) {
    printf("尝试 %s:\n", pattern_name);
    for (int i = 12; i < 16; i++) {
        data[i] = ~data[i];
    }
    printf("结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", data[i]);
    }
    printf("\n\n");
}

int main() {
    printf("BMS AES算法分析\n");
    printf("===============\n\n");
    
    // 真实数据
    uint8_t real_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    uint8_t real_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0xA2, 0x4E, 0xBB, 0x0A
    };
    
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    printf("分析数据:\n");
    printf("========\n");
    printf("Seed:     ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_seed[i]);
    }
    printf("\n");
    
    printf("真实Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_key[i]);
    }
    printf("\n");
    
    printf("AES密钥:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_key[i]);
    }
    printf("\n\n");
    
    // 标准AES计算
    uint8_t aes_result[16];
    struct AES_ctx ctx;
    AES_init_ctx(&ctx, aes_key);
    memcpy(aes_result, real_seed, 16);
    AES_ECB_encrypt(&ctx, aes_result);
    
    printf("标准AES结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_result[i]);
    }
    printf("\n\n");
    
    printf("差异分析:\n");
    printf("========\n");
    printf("字节位置 | 真实Key | AES结果 | 差异(XOR)\n");
    printf("---------|---------|---------|----------\n");
    for (int i = 0; i < 16; i++) {
        uint8_t diff = real_key[i] ^ aes_result[i];
        printf("   %2d    |   %02X    |   %02X    |    %02X\n", 
               i, real_key[i], aes_result[i], diff);
    }
    printf("\n");
    
    // 分析最后4个字节的模式
    printf("最后4字节分析:\n");
    printf("=============\n");
    printf("真实: %02X %02X %02X %02X\n", 
           real_key[12], real_key[13], real_key[14], real_key[15]);
    printf("AES:  %02X %02X %02X %02X\n", 
           aes_result[12], aes_result[13], aes_result[14], aes_result[15]);
    
    // 检查是否是简单的XOR模式
    uint8_t xor_pattern[4];
    for (int i = 0; i < 4; i++) {
        xor_pattern[i] = real_key[12+i] ^ aes_result[12+i];
    }
    printf("XOR:  %02X %02X %02X %02X\n", 
           xor_pattern[0], xor_pattern[1], xor_pattern[2], xor_pattern[3]);
    
    // 检查是否有固定的XOR值
    int same_xor = 1;
    for (int i = 1; i < 4; i++) {
        if (xor_pattern[i] != xor_pattern[0]) {
            same_xor = 0;
            break;
        }
    }
    
    if (same_xor) {
        printf("\n发现固定XOR模式: 0x%02X\n", xor_pattern[0]);
    } else {
        printf("\n未发现固定XOR模式\n");
    }
    
    // 尝试不同的变换
    printf("\n尝试不同的后处理算法:\n");
    printf("====================\n");
    
    // 尝试1: 字节反转
    uint8_t test1[16];
    memcpy(test1, aes_result, 16);
    try_bit_inversion(test1, "位反转最后4字节");
    
    // 尝试2: 字节交换
    uint8_t test2[16];
    memcpy(test2, aes_result, 16);
    try_byte_swap(test2, "字节顺序交换");
    
    // 尝试3: 特定XOR
    uint8_t test3[16];
    memcpy(test3, aes_result, 16);
    try_xor_pattern(test3, "XOR 0xF8", 0xF8);
    
    // 检查是否匹配
    printf("匹配检查:\n");
    printf("========\n");
    if (memcmp(real_key, test1, 16) == 0) {
        printf("✅ 位反转算法匹配!\n");
    } else if (memcmp(real_key, test2, 16) == 0) {
        printf("✅ 字节交换算法匹配!\n");
    } else if (memcmp(real_key, test3, 16) == 0) {
        printf("✅ XOR算法匹配!\n");
    } else {
        printf("❌ 未找到匹配的算法\n");
        printf("可能需要更复杂的变换或不同的AES密钥\n");
    }
    
    return 0;
}
