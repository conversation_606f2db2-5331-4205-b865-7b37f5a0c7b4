#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"

// calc_key的计算方式
void calculate_key_for_uds27(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);
}

// uds_27_advanced的计算方式
void calculate_key_with_aes(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);
}

int main() {
    printf("比较两种计算方式\n");
    printf("================\n\n");
    
    // 测试seed
    uint8_t test_seed[16] = {
        0x86, 0xBA, 0x46, 0x4A, 0x7C, 0xE0, 0x28, 0x14,
        0xD4, 0x02, 0x82, 0x90, 0xA3, 0xCA, 0xF5, 0x22
    };
    
    printf("测试Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", test_seed[i]);
    }
    printf("\n\n");
    
    // 测试不同的密钥类型
    printf("测试不同密钥类型:\n");
    printf("================\n");
    
    for (int key_type = 0; key_type <= 5; key_type++) {
        const uint8_t *aes_key = get_aes_key_by_type((aes_key_type_t)key_type);
        
        uint8_t key1[16], key2[16];
        
        // 使用两种方式计算
        calculate_key_for_uds27(test_seed, key1, aes_key);
        calculate_key_with_aes(test_seed, key2, aes_key);
        
        printf("密钥类型 %d (%s):\n", key_type, get_aes_key_description((aes_key_type_t)key_type));
        printf("  AES密钥: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X", aes_key[i]);
        }
        printf("\n");
        
        printf("  calc_key:     ");
        for (int i = 0; i < 16; i++) {
            printf("%02X", key1[i]);
        }
        printf("\n");
        
        printf("  uds_advanced: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X", key2[i]);
        }
        printf("\n");
        
        int match = memcmp(key1, key2, 16) == 0;
        printf("  结果: %s\n\n", match ? "✅ 相同" : "❌ 不同");
        
        if (!match) {
            printf("  差异分析:\n");
            for (int i = 0; i < 16; i++) {
                if (key1[i] != key2[i]) {
                    printf("    字节%2d: %02X vs %02X\n", i, key1[i], key2[i]);
                }
            }
            printf("\n");
        }
    }
    
    return 0;
}
