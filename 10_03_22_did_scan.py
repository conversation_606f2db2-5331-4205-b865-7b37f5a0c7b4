#!/usr/bin/env python3
"""
UDS 22服务DID扫描工具
1. 执行UDS 10 01 (默认会话)
2. 执行UDS 10 03 (扩展会话)
3. 循环调用UDS 22服务，遍历指定DID范围
4. 记录正响应的DID和数据
使用Python isotp处理多字节数据传输
"""

import socket
import time
import sys
import os
from datetime import datetime
import isotp

class UDS22DIDScanner:
    """UDS 22服务DID扫描器"""
    
    def __init__(self, request_id, response_id, did_start, did_end, interface='can0'):
        self.request_id = int(request_id, 16) if isinstance(request_id, str) else request_id
        self.response_id = int(response_id, 16) if isinstance(response_id, str) else response_id
        self.did_start = int(did_start, 16) if isinstance(did_start, str) else did_start
        self.did_end = int(did_end, 16) if isinstance(did_end, str) else did_end
        self.interface = interface
        
        self.isotp_socket = None
        self.log_file = None
        
        # 存储发现的DID
        self.successful_dids = {}  # {did: response_data}
        self.failed_dids = []
        
        # UDS服务常量
        self.UDS_SERVICE_10 = 0x10  # 诊断会话控制
        self.UDS_SERVICE_22 = 0x22  # 读数据标识符
        self.UDS_POSITIVE_RESPONSE = 0x40  # 正响应偏移
    
    def init_isotp_socket(self):
        """初始化ISO-TP socket"""
        try:
            # 创建ISO-TP socket
            self.isotp_socket = isotp.socket()

            # 绑定到CAN接口和地址
            self.isotp_socket.bind(self.interface, isotp.Address(
                txid=self.request_id,
                rxid=self.response_id
            ))

            # 设置超时
            self.isotp_socket.settimeout(2.0)

            print(f"ISO-TP socket初始化成功")
            print(f"  接口: {self.interface}")
            print(f"  发送ID: 0x{self.request_id:03X}")
            print(f"  接收ID: 0x{self.response_id:03X}")
            return True

        except Exception as e:
            print(f"初始化ISO-TP socket失败: {e}")
            print("尝试使用备用方法...")
            return self.init_isotp_socket_fallback()

    def init_isotp_socket_fallback(self):
        """备用ISO-TP socket初始化方法"""
        try:
            # 创建ISO-TP socket (简化版本)
            self.isotp_socket = isotp.socket()

            # 直接绑定，不设置额外选项
            addr = isotp.Address(txid=self.request_id, rxid=self.response_id)
            self.isotp_socket.bind(self.interface, addr)

            # 设置超时
            self.isotp_socket.settimeout(2.0)

            print(f"ISO-TP socket备用初始化成功")
            print(f"  接口: {self.interface}")
            print(f"  发送ID: 0x{self.request_id:03X}")
            print(f"  接收ID: 0x{self.response_id:03X}")
            return True

        except Exception as e:
            print(f"备用ISO-TP socket初始化也失败: {e}")
            print("请检查python-can-isotp是否正确安装")
            print("安装命令: pip install can-isotp")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_22_did_scan_{self.request_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS 22服务DID扫描 - {datetime.now()}\n")
            self.log_file.write(f"请求CAN ID: 0x{self.request_id:03X}\n")
            self.log_file.write(f"响应CAN ID: 0x{self.response_id:03X}\n")
            self.log_file.write(f"DID范围: 0x{self.did_start:04X} - 0x{self.did_end:04X}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_uds_request(self, data):
        """发送UDS请求并接收响应"""
        try:
            # 发送数据
            self.isotp_socket.send(data)
            self.log_message(f"发送: {' '.join(f'{b:02X}' for b in data)}", False)
            
            # 接收响应
            response = self.isotp_socket.recv()
            self.log_message(f"接收: {' '.join(f'{b:02X}' for b in response)}", False)
            
            return response
            
        except socket.timeout:
            self.log_message("接收超时", False)
            return None
        except Exception as e:
            self.log_message(f"通信错误: {e}", False)
            return None
    
    def execute_uds_10_01(self):
        """执行UDS 10 01 (默认会话)"""
        self.log_message("执行UDS 10 01 (默认会话)...")
        request = bytes([self.UDS_SERVICE_10, 0x01])
        response = self.send_uds_request(request)
        
        if response and len(response) >= 2 and response[0] == (self.UDS_SERVICE_10 + self.UDS_POSITIVE_RESPONSE):
            self.log_message("✅ UDS 10 01 成功")
            return True
        else:
            self.log_message("❌ UDS 10 01 失败")
            return False
    
    def execute_uds_10_03(self):
        """执行UDS 10 03 (扩展会话)"""
        self.log_message("执行UDS 10 03 (扩展会话)...")
        request = bytes([self.UDS_SERVICE_10, 0x03])
        response = self.send_uds_request(request)
        
        if response and len(response) >= 2 and response[0] == (self.UDS_SERVICE_10 + self.UDS_POSITIVE_RESPONSE):
            self.log_message("✅ UDS 10 03 成功")
            return True
        else:
            self.log_message("❌ UDS 10 03 失败")
            return False
    
    def scan_did(self, did):
        """扫描单个DID"""
        # 构造UDS 22请求
        request = bytes([self.UDS_SERVICE_22, (did >> 8) & 0xFF, did & 0xFF])
        
        self.log_message(f"扫描DID 0x{did:04X}...", False)
        response = self.send_uds_request(request)
        
        if response is None:
            self.log_message(f"DID 0x{did:04X}: 无响应", False)
            self.failed_dids.append(did)
            return False
        
        # 检查是否为正响应
        if len(response) >= 3 and response[0] == (self.UDS_SERVICE_22 + self.UDS_POSITIVE_RESPONSE):
            # 验证DID回显
            response_did = (response[1] << 8) | response[2]
            if response_did == did:
                data = response[3:]  # DID数据
                self.log_message(f"✅ DID 0x{did:04X}: {' '.join(f'{b:02X}' for b in data)}")
                self.successful_dids[did] = data
                return True
            else:
                self.log_message(f"❌ DID 0x{did:04X}: DID回显错误 (期望0x{did:04X}, 收到0x{response_did:04X})", False)
                self.failed_dids.append(did)
                return False
        
        # 检查是否为负响应
        elif len(response) >= 3 and response[0] == 0x7F and response[1] == self.UDS_SERVICE_22:
            nrc = response[2]
            self.log_message(f"❌ DID 0x{did:04X}: 负响应 NRC=0x{nrc:02X}", False)
            self.failed_dids.append(did)
            return False
        
        else:
            self.log_message(f"❌ DID 0x{did:04X}: 无效响应格式", False)
            self.failed_dids.append(did)
            return False
    
    def scan_did_range(self):
        """扫描DID范围"""
        print(f"\n开始DID扫描: 0x{self.did_start:04X} - 0x{self.did_end:04X}")
        print("=" * 50)
        
        total_dids = self.did_end - self.did_start + 1
        scanned = 0
        
        for did in range(self.did_start, self.did_end + 1):
            scanned += 1
            progress = (scanned / total_dids) * 100
            
            # 每100个DID显示一次进度
            if scanned % 100 == 0 or scanned == 1:
                print(f"\r扫描进度: {progress:5.1f}% (DID 0x{did:04X})", end='', flush=True)
            
            success = self.scan_did(did)
            
            if success:
                print()  # 换行显示成功的DID
            
            # 短暂延时避免总线拥塞
            time.sleep(0.05)
        
        print()  # 最终换行
        print(f"\n扫描完成: 发现 {len(self.successful_dids)} 个有效DID")
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 60)
        print("DID扫描结果摘要")
        print("=" * 60)
        
        total_scanned = len(self.successful_dids) + len(self.failed_dids)
        success_count = len(self.successful_dids)
        
        print(f"总扫描DID数: {total_scanned}")
        print(f"成功DID数:   {success_count}")
        print(f"失败DID数:   {len(self.failed_dids)}")
        print(f"成功率:      {success_count/total_scanned*100:.2f}%" if total_scanned > 0 else "成功率: 0%")
        
        if self.successful_dids:
            print(f"\n✅ 发现有效DID ({len(self.successful_dids)}个):")
            for did, data in sorted(self.successful_dids.items()):
                data_str = ' '.join(f'{b:02X}' for b in data)
                data_len = len(data)
                print(f"  DID 0x{did:04X}: [{data_len:2d}字节] {data_str}")
                
                # 记录到日志
                self.log_message(f"有效DID: 0x{did:04X} = {data_str}", False)
        else:
            print("\n❌ 未发现有效DID")
            self.log_message("未发现有效DID", False)
        
        # 记录统计信息到日志
        self.log_message(f"\nDID扫描统计:", False)
        self.log_message(f"总扫描DID数: {total_scanned}", False)
        self.log_message(f"成功DID数: {success_count}", False)
        self.log_message(f"失败DID数: {len(self.failed_dids)}", False)
        self.log_message(f"成功率: {success_count/total_scanned*100:.2f}%" if total_scanned > 0 else "成功率: 0%", False)
        
        return len(self.successful_dids) > 0
    
    def cleanup(self):
        """清理资源"""
        if self.isotp_socket:
            self.isotp_socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行完整扫描"""
        print("UDS 22服务DID扫描工具")
        print("=" * 25)
        print(f"请求CAN ID: 0x{self.request_id:03X}")
        print(f"响应CAN ID: 0x{self.response_id:03X}")
        print(f"DID范围: 0x{self.did_start:04X} - 0x{self.did_end:04X}")
        print(f"CAN接口: {self.interface}")
        
        # 初始化
        if not self.init_isotp_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行UDS 10 01
            if not self.execute_uds_10_01():
                return False
            
            time.sleep(0.5)
            
            # 执行UDS 10 03
            if not self.execute_uds_10_03():
                return False
            
            time.sleep(0.5)
            
            # 扫描DID范围
            self.scan_did_range()
            
            # 分析结果
            return self.print_summary()
            
        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False
        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def parse_hex_value(value_str, name):
    """解析十六进制值"""
    try:
        if value_str.startswith('0x') or value_str.startswith('0X'):
            return int(value_str, 16)
        else:
            return int(value_str, 16)
    except ValueError:
        raise ValueError(f"无效的{name}: {value_str}")

def main():
    """主函数"""
    if len(sys.argv) < 5:
        print("用法: python3 10_03_22_did_scan.py <请求CAN_ID> <响应CAN_ID> <DID开始> <DID结束> [接口]")
        print("示例: python3 10_03_22_did_scan.py 0x715 0x795 0x1000 0x10FF")
        print("      python3 10_03_22_did_scan.py 715 795 1000 10FF can0")
        print("说明:")
        print("  请求CAN_ID: 必需，发送UDS请求的CAN ID")
        print("  响应CAN_ID: 必需，接收UDS响应的CAN ID")
        print("  DID开始:    必需，扫描的起始DID")
        print("  DID结束:    必需，扫描的结束DID")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)
    
    try:
        # 解析参数
        request_id = parse_hex_value(sys.argv[1], "请求CAN ID")
        response_id = parse_hex_value(sys.argv[2], "响应CAN ID")
        did_start = parse_hex_value(sys.argv[3], "DID开始")
        did_end = parse_hex_value(sys.argv[4], "DID结束")
        
        # 验证参数范围
        if request_id < 0 or request_id > 0x7FF:
            print(f"❌ 请求CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if response_id < 0 or response_id > 0x7FF:
            print(f"❌ 响应CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if did_start > did_end:
            print(f"❌ DID开始值不能大于结束值")
            sys.exit(1)
        
        if did_start < 0 or did_end > 0xFFFF:
            print(f"❌ DID必须在0x0000-0xFFFF范围内")
            sys.exit(1)
        
        # 获取接口名称
        interface = sys.argv[5] if len(sys.argv) > 5 else 'can0'
        
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    scanner = UDS22DIDScanner(request_id, response_id, did_start, did_end, interface)
    
    try:
        success = scanner.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
