#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"
#include "bms_aes.h"

int main() {
    printf("BMS UDS 27服务 - Key计算\n");
    printf("=======================\n\n");
    
    // 您提供的seed
    uint8_t seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    printf("输入数据:\n");
    printf("========\n");
    printf("Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", seed[i]);
    }
    printf("\n");
    
    printf("AES密钥: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", aes_key[i]);
    }
    printf("\n\n");
    
    // 使用BMS专用AES算法计算key
    uint8_t calculated_key[16];
    bms_aes_encrypt(seed, calculated_key, aes_key);
    
    printf("BMS AES算法计算过程:\n");
    printf("===================\n");
    bms_aes_debug(seed, aes_key, 1);
    
    printf("最终结果:\n");
    printf("========\n");
    printf("计算的Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", calculated_key[i]);
    }
    printf("\n\n");
    
    // 格式化输出，便于复制使用
    printf("格式化输出:\n");
    printf("===========\n");
    printf("Key (连续): ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", calculated_key[i]);
    }
    printf("\n");
    
    printf("Key (空格): ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", calculated_key[i]);
    }
    printf("\n");
    
    printf("Key (C数组): {");
    for (int i = 0; i < 16; i++) {
        printf("0x%02X", calculated_key[i]);
        if (i < 15) printf(", ");
    }
    printf("}\n");
    
    return 0;
}
