# UDS 27服务 - 安全访问实现

这个项目实现了UDS (Unified Diagnostic Services) 27服务的安全访问功能，使用AES128算法进行seed和key的计算。

**默认配置**: 使用物理地址 0x715 作为请求ID，0x795 作为响应ID。

## 文件结构

```
bms_test/
├── aes/
│   ├── aes.c           # AES128算法实现
│   └── aes.h           # AES128头文件
├── uds_27_service.c    # 基础版本UDS 27服务实现
├── uds_27_advanced.c   # 高级版本UDS 27服务实现
├── uds_config.h        # 配置文件
├── 27_comm.txt         # 通信示例记录
├── init_can0.sh        # CAN接口初始化脚本
├── test_uds27.sh       # 测试脚本
├── Makefile            # 编译配置
└── README.md           # 说明文档
```

## 功能特性

- **UDS 27服务实现**: 完整的安全访问流程
- **AES128加密**: 使用标准AES128算法计算key
- **多种密钥支持**: 默认、全零、简单、自定义密钥
- **灵活的CAN ID**: 支持0x715(物理地址)和0x7DF(功能地址)请求地址
- **多帧传输**: 支持ISO-TP多帧传输协议
- **详细日志**: 可选的详细输出模式
- **CAN模拟器**: 内置BMS模拟器用于测试

## 编译

### 安装依赖
```bash
sudo apt-get update
sudo apt-get install -y build-essential can-utils
```

### 编译程序
```bash
# 编译所有版本
make all

# 或者分别编译
make basic      # 编译基础版本
make advanced   # 编译高级版本
```

## 使用方法

### 1. 设置CAN接口

#### 真实CAN接口
```bash
# 设置CAN接口 (需要硬件CAN适配器)
sudo ./init_can0.sh
```

#### 虚拟CAN接口 (用于测试)
```bash
sudo modprobe vcan
sudo ip link add dev vcan0 type vcan
sudo ip link set up vcan0
```

### 2. 运行程序

#### 基础版本
```bash
./uds_27_service
```

#### 高级版本 (推荐)
```bash
# 使用默认配置 (0x715物理地址)
./uds_27_advanced

# 使用功能地址
./uds_27_advanced -r 0x7DF

# 使用不同的AES密钥
./uds_27_advanced -k 1          # 全零密钥
./uds_27_advanced -k 2          # 简单密钥
./uds_27_advanced -c 0123456789ABCDEFFEDCBA9876543210  # 自定义密钥

# 详细输出模式
./uds_27_advanced -v

# 显示帮助
./uds_27_advanced -h
```

#### CAN模拟器
```bash
# 编译模拟器
make simulator

# 运行模拟器 (在另一个终端)
./can_simulator

# 然后在另一个终端运行客户端
./uds_27_advanced -v
```

### 3. 使用测试脚本
```bash
./test_uds27.sh
```

## 命令行参数

高级版本支持以下命令行参数：

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| -i | --interface | CAN接口名称 | can0 |
| -r | --request-id | 请求CAN ID | 0x715 |
| -s | --response-id | 响应CAN ID | 0x795 |
| -k | --key-type | AES密钥类型 (0-3) | 0 |
| -c | --custom-key | 自定义AES密钥 | - |
| -t | --timeout | 超时时间(ms) | 1000 |
| -v | --verbose | 详细输出 | 关闭 |
| -h | --help | 显示帮助 | - |

### 密钥类型说明

- **0 (默认)**: 标准AES测试向量密钥
- **1 (全零)**: 全零密钥 (用于测试)
- **2 (简单)**: 简单模式密钥
- **3 (自定义)**: 用户提供的自定义密钥

## UDS 27服务流程

1. **请求Seed** (27 03):
   ```
   发送: 715 [3] 02 27 03
   接收: 795 [8] 10 12 67 03 [16字节seed...]
   ```

2. **发送Key** (27 04):
   ```
   发送: 715 [8] 10 12 27 04 00 [key前3字节]
   发送: 715 [8] 21 [key中7字节]
   发送: 715 [6] 22 [key后6字节]
   接收: 795 [3] 02 67 04  (成功)
   或者: 795 [4] 03 7F 27 7F  (失败)
   ```

## AES128密钥计算

程序使用AES128 ECB模式对seed进行加密来生成key：

```c
key = AES128_ECB_Encrypt(seed, aes_key)
```

## 故障排除

### 常见问题

1. **CAN接口不存在**
   ```
   错误: CAN接口 can0 不存在
   解决: 运行 sudo ./init_can0.sh 或设置虚拟CAN接口
   ```

2. **权限不足**
   ```
   错误: bind: Operation not permitted
   解决: 使用 sudo 运行程序
   ```

3. **接收超时**
   ```
   错误: 接收响应超时
   解决: 检查CAN总线连接和目标设备状态
   ```

4. **Key验证失败**
   ```
   错误: Key验证失败 - NRC: 0x7F
   解决: 尝试不同的AES密钥类型
   ```

### 调试技巧

1. **使用详细模式**: 添加 `-v` 参数查看详细通信过程
2. **监控CAN总线**: 使用 `candump can0` 监控CAN通信
3. **测试不同密钥**: 尝试不同的AES密钥类型
4. **检查CAN ID**: 确认使用正确的请求和响应CAN ID

## 注意事项

1. **安全性**: 实际使用时需要配置正确的AES密钥
2. **兼容性**: 不同的BMS可能使用不同的密钥算法
3. **时序**: 某些系统对时序要求严格
4. **权限**: 访问CAN接口需要适当的系统权限

## 许可证

本项目仅供学习和研究使用。
