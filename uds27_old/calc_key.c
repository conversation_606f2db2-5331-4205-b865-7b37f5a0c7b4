#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "aes/aes.h"
#include "aes_keys.h"

// 解析十六进制字符串到字节数组
int parse_hex_string(const char *hex_str, uint8_t *bytes, int max_bytes) {
    int len = strlen(hex_str);
    
    // 移除空格和其他分隔符
    char clean_hex[256];
    int clean_pos = 0;
    for (int i = 0; i < len && clean_pos < 255; i++) {
        char c = hex_str[i];
        if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f')) {
            clean_hex[clean_pos++] = c;
        }
    }
    clean_hex[clean_pos] = '\0';
    
    if (clean_pos != max_bytes * 2) {
        printf("错误: 需要%d个十六进制字符，但提供了%d个\n", max_bytes * 2, clean_pos);
        return -1;
    }
    
    for (int i = 0; i < max_bytes; i++) {
        char hex_byte[3] = {clean_hex[i*2], clean_hex[i*2+1], '\0'};
        bytes[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }
    
    return 0;
}

// 计算UDS 27 04要发送的key (标准AES结果)
void calculate_key_for_uds27(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);
}

void print_usage(const char *program_name) {
    printf("用法: %s <seed>\n", program_name);
    printf("\n");
    printf("参数:\n");
    printf("  seed    16字节的seed (32个十六进制字符)\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s 86BA464A7CE02814D40282904ACAF522\n", program_name);
    printf("  %s \"86 BA 46 4A 7C E0 28 14 D4 02 82 90 A3 CA F5 22\"\n", program_name);
    printf("  %s 86BA464A7CE02814D40282904ACAF522\n", program_name);
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        print_usage(argv[0]);
        return 1;
    }
    
    uint8_t seed[16];
    
    // 解析输入的seed
    if (parse_hex_string(argv[1], seed, 16) < 0) {
        printf("错误: 无效的seed格式\n");
        print_usage(argv[0]);
        return 1;
    }
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    // 计算要发送的key
    uint8_t key[16];
    calculate_key_for_uds27(seed, key, aes_key);
    
    printf("UDS 27服务 Key计算\n");
    printf("==================\n\n");
    
    printf("输入Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", seed[i]);
    }
    printf("\n");
    
    printf("AES密钥:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_key[i]);
    }
    printf("\n\n");
    
    printf("UDS 27 04要发送的Key:\n");
    printf("====================\n");
    printf("Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", key[i]);
    }
    printf("\n\n");
    
    printf("格式化输出:\n");
    printf("===========\n");
    printf("连续格式: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", key[i]);
    }
    printf("\n");
    
    printf("C数组格式: {");
    for (int i = 0; i < 16; i++) {
        printf("0x%02X", key[i]);
        if (i < 15) printf(", ");
    }
    printf("}\n");
    
    return 0;
}
