#include <stdio.h>
#include <stdint.h>
#include <string.h>

void test_key_frame_construction() {
    // 测试用的key
    uint8_t key[16] = {
        0xAF, 0x1C, 0xFA, 0x7A, 0x3D, 0xEA, 0x98, 0x3B,
        0xB5, 0x56, 0x37, 0xCD, 0xEA, 0xE6, 0x38, 0x05
    };
    
    printf("测试Key帧构造\n");
    printf("=============\n\n");
    
    printf("原始Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", key[i]);
    }
    printf("\n\n");
    
    // 构造修正后的帧
    uint8_t first_frame[8] = {0x10, 0x12, 0x27, 0x04};
    uint8_t second_frame[8] = {0x21};
    uint8_t third_frame[8] = {0x22};
    
    // 复制key的前4个字节到第一帧 (从first_frame[4]开始)
    first_frame[4] = key[0];
    first_frame[5] = key[1];
    first_frame[6] = key[2];
    first_frame[7] = key[3];
    
    // 复制key的中间7个字节到第二帧
    for (int i = 0; i < 7; i++) {
        second_frame[1 + i] = key[4 + i];
    }
    
    // 复制key的最后5个字节到第三帧
    for (int i = 0; i < 5; i++) {
        third_frame[1 + i] = key[11 + i];
    }
    
    printf("修正后的帧构造:\n");
    printf("===============\n");
    printf("第一帧: ");
    for (int i = 0; i < 8; i++) {
        printf("%02X ", first_frame[i]);
    }
    printf("(包含key的前4字节)\n");
    
    printf("第二帧: ");
    for (int i = 0; i < 8; i++) {
        printf("%02X ", second_frame[i]);
    }
    printf("(包含key的中7字节)\n");
    
    printf("第三帧: ");
    for (int i = 0; i < 6; i++) {  // 只显示6字节
        printf("%02X ", third_frame[i]);
    }
    printf("(包含key的后5字节)\n\n");
    
    // 重新组装key验证
    uint8_t reconstructed_key[16];
    
    // 从第一帧提取
    reconstructed_key[0] = first_frame[4];
    reconstructed_key[1] = first_frame[5];
    reconstructed_key[2] = first_frame[6];
    reconstructed_key[3] = first_frame[7];
    
    // 从第二帧提取
    for (int i = 0; i < 7; i++) {
        reconstructed_key[4 + i] = second_frame[1 + i];
    }
    
    // 从第三帧提取
    for (int i = 0; i < 5; i++) {
        reconstructed_key[11 + i] = third_frame[1 + i];
    }
    
    printf("重新组装的Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", reconstructed_key[i]);
    }
    printf("\n");
    
    // 验证
    int match = memcmp(key, reconstructed_key, 16) == 0;
    printf("验证结果: %s\n", match ? "✅ 匹配" : "❌ 不匹配");
    
    if (!match) {
        printf("\n差异分析:\n");
        for (int i = 0; i < 16; i++) {
            if (key[i] != reconstructed_key[i]) {
                printf("字节%2d: 原始=%02X, 重组=%02X\n", i, key[i], reconstructed_key[i]);
            }
        }
    }
    
    printf("\n期望的CAN帧发送:\n");
    printf("================\n");
    printf("发送: can0  715   [8]  10 12 27 04 AF 1C FA 7A\n");
    printf("发送: can0  715   [8]  21 3D EA 98 3B B5 56 37\n");
    printf("发送: can0  715   [6]  22 CD EA E6 38 05\n");
}

int main() {
    test_key_frame_construction();
    return 0;
}
