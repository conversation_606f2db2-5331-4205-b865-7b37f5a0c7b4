#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"

// 尝试各种可能的变换
int test_transform(uint8_t *aes_result, uint8_t *target, const char *description) {
    printf("测试: %s\n", description);
    printf("结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_result[i]);
    }
    
    int match = memcmp(aes_result, target, 16) == 0;
    printf(" -> %s\n", match ? "✅ 匹配!" : "❌ 不匹配");
    return match;
}

int main() {
    printf("寻找正确的BMS变换算法\n");
    printf("=====================\n\n");
    
    // 真实数据
    uint8_t real_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    uint8_t real_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0xA2, 0x4E, 0xBB, 0x0A
    };
    
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    // 标准AES计算
    uint8_t aes_result[16];
    struct AES_ctx ctx;
    AES_init_ctx(&ctx, aes_key);
    memcpy(aes_result, real_seed, 16);
    AES_ECB_encrypt(&ctx, aes_result);
    
    printf("基础AES结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", aes_result[i]);
    }
    printf("\n");
    
    printf("目标Key:     ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_key[i]);
    }
    printf("\n\n");
    
    // 分析最后4个字节的变换
    printf("最后4字节详细分析:\n");
    printf("=================\n");
    uint8_t aes_last4[4] = {aes_result[12], aes_result[13], aes_result[14], aes_result[15]};
    uint8_t real_last4[4] = {real_key[12], real_key[13], real_key[14], real_key[15]};
    
    printf("AES:  %02X %02X %02X %02X\n", aes_last4[0], aes_last4[1], aes_last4[2], aes_last4[3]);
    printf("真实: %02X %02X %02X %02X\n", real_last4[0], real_last4[1], real_last4[2], real_last4[3]);
    
    // 尝试各种变换
    printf("\n尝试各种变换:\n");
    printf("=============\n");
    
    // 1. 尝试字节位置交换
    uint8_t test1[16];
    memcpy(test1, aes_result, 16);
    // 交换 [12,13,14,15] -> [15,14,13,12]
    test1[12] = aes_result[15];
    test1[13] = aes_result[14]; 
    test1[14] = aes_result[13];
    test1[15] = aes_result[12];
    if (test_transform(test1, real_key, "完全反转最后4字节")) return 0;
    
    // 2. 尝试两两交换
    uint8_t test2[16];
    memcpy(test2, aes_result, 16);
    test2[12] = aes_result[13]; test2[13] = aes_result[12];
    test2[14] = aes_result[15]; test2[15] = aes_result[14];
    if (test_transform(test2, real_key, "两两交换最后4字节")) return 0;
    
    // 3. 尝试循环移位
    uint8_t test3[16];
    memcpy(test3, aes_result, 16);
    test3[12] = aes_result[13];
    test3[13] = aes_result[14];
    test3[14] = aes_result[15];
    test3[15] = aes_result[12];
    if (test_transform(test3, real_key, "左循环移位1位")) return 0;
    
    // 4. 尝试右循环移位
    uint8_t test4[16];
    memcpy(test4, aes_result, 16);
    test4[12] = aes_result[15];
    test4[13] = aes_result[12];
    test4[14] = aes_result[13];
    test4[15] = aes_result[14];
    if (test_transform(test4, real_key, "右循环移位1位")) return 0;
    
    // 5. 尝试特定的XOR模式
    uint8_t xor_patterns[] = {0xF8, 0x6A, 0x50, 0xB0};
    uint8_t test5[16];
    memcpy(test5, aes_result, 16);
    for (int i = 0; i < 4; i++) {
        test5[12+i] ^= xor_patterns[i];
    }
    if (test_transform(test5, real_key, "特定XOR模式")) return 0;
    
    // 6. 尝试位操作
    uint8_t test6[16];
    memcpy(test6, aes_result, 16);
    for (int i = 12; i < 16; i++) {
        test6[i] = ((test6[i] << 4) | (test6[i] >> 4)); // 半字节交换
    }
    if (test_transform(test6, real_key, "半字节交换")) return 0;
    
    // 7. 尝试加法运算
    uint8_t test7[16];
    memcpy(test7, aes_result, 16);
    test7[12] = (test7[12] + 0x48) & 0xFF;
    test7[13] = (test7[13] + 0x2A) & 0xFF;
    test7[14] = (test7[14] - 0x30) & 0xFF;
    test7[15] = (test7[15] - 0xB0) & 0xFF;
    if (test_transform(test7, real_key, "特定加减运算")) return 0;
    
    // 8. 尝试查表替换 (简单的S-box)
    uint8_t sbox[256];
    for (int i = 0; i < 256; i++) {
        sbox[i] = i; // 初始化为恒等映射
    }
    // 根据观察到的变换设置特定映射
    sbox[0x5A] = 0xA2;
    sbox[0x24] = 0x4E;
    sbox[0xEB] = 0xBB;
    sbox[0xBA] = 0x0A;
    
    uint8_t test8[16];
    memcpy(test8, aes_result, 16);
    for (int i = 12; i < 16; i++) {
        test8[i] = sbox[test8[i]];
    }
    if (test_transform(test8, real_key, "查表替换")) return 0;
    
    printf("\n❌ 未找到匹配的变换算法\n");
    printf("可能的原因:\n");
    printf("1. 使用了不同的AES密钥\n");
    printf("2. 使用了更复杂的后处理算法\n");
    printf("3. 使用了不同的AES模式 (如CBC而非ECB)\n");
    printf("4. 在AES之前对seed进行了预处理\n");
    
    return 1;
}
