#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"
#include "bms_aes.h"

int main() {
    printf("修正后的UDS 27服务逻辑验证\n");
    printf("==========================\n\n");
    
    // 从实际通信记录中提取的seed
    uint8_t real_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    // 真实通信记录中发送的key (从27 04帧中提取)
    uint8_t real_sent_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0x5A, 0x24, 0xEB, 0xBA  // 注意最后4字节
    };
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    printf("真实通信记录分析:\n");
    printf("================\n");
    printf("发送的27 04帧:\n");
    printf("0x715  [8] 10 12 27 04 CF 43 FF 5E\n");
    printf("0x715  [8] 21 2A 8C 26 44 C9 40 45\n");
    printf("0x715  [8] 22 05 5A 24 EB BA 55 55\n");
    printf("                   ^^ ^^ ^^ ^^  <- 注意最后4字节\n\n");
    
    printf("提取的数据:\n");
    printf("===========\n");
    printf("Seed:         ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_seed[i]);
    }
    printf("\n");
    
    printf("发送的Key:    ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", real_sent_key[i]);
    }
    printf("\n\n");
    
    // 计算标准AES结果 (应该与发送的key匹配)
    uint8_t calculated_aes_key[16];
    struct AES_ctx ctx;
    AES_init_ctx(&ctx, aes_key);
    memcpy(calculated_aes_key, real_seed, 16);
    AES_ECB_encrypt(&ctx, calculated_aes_key);
    
    printf("计算结果:\n");
    printf("========\n");
    printf("标准AES结果:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", calculated_aes_key[i]);
    }
    printf("\n");
    
    // 计算BMS后处理结果 (BMS内部验证用)
    uint8_t bms_processed_key[16];
    bms_aes_encrypt(real_seed, bms_processed_key, aes_key);
    
    printf("BMS后处理:    ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", bms_processed_key[i]);
    }
    printf("\n\n");
    
    // 验证逻辑
    printf("验证结果:\n");
    printf("========\n");
    
    // 检查发送的key是否与标准AES结果匹配
    int aes_match = memcmp(real_sent_key, calculated_aes_key, 16) == 0;
    printf("发送Key vs 标准AES: %s\n", aes_match ? "✅ 匹配" : "❌ 不匹配");
    
    // 检查发送的key是否与BMS后处理结果匹配
    int bms_match = memcmp(real_sent_key, bms_processed_key, 16) == 0;
    printf("发送Key vs BMS后处理: %s\n", bms_match ? "✅ 匹配" : "❌ 不匹配");
    
    printf("\n结论:\n");
    printf("====\n");
    if (aes_match) {
        printf("✅ 正确! UDS 27 04应该发送标准AES结果\n");
        printf("   发送: CF43FF5E2A8C2644C9404505**5A24EBBA**\n");
        printf("   BMS内部会对接收到的key进行后处理验证\n");
    } else if (bms_match) {
        printf("❌ 错误! 不应该发送BMS后处理结果\n");
    } else {
        printf("❓ 未知情况，需要进一步分析\n");
    }
    
    printf("\n修正后的逻辑:\n");
    printf("=============\n");
    printf("1. 客户端收到seed: %s\n", "D321B29669A943F693D15E4082A93FEA");
    printf("2. 客户端计算标准AES: %s\n", "CF43FF5E2A8C2644C9404505**5A24EBBA**");
    printf("3. 客户端发送标准AES结果作为key\n");
    printf("4. BMS收到key后，内部进行后处理验证\n");
    printf("5. BMS验证: 收到的key + 后处理 = 预期结果\n");
    
    return aes_match ? 0 : 1;
}
