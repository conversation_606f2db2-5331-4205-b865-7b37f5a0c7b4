#!/bin/bash

# UDS 27服务演示脚本
# 展示如何使用CAN模拟器和客户端程序

echo "UDS 27服务演示"
echo "=============="
echo ""

# 检查程序是否已编译
if [ ! -f "uds_27_advanced" ] || [ ! -f "can_simulator" ]; then
    echo "正在编译程序..."
    make all
    if [ $? -ne 0 ]; then
        echo "编译失败!"
        exit 1
    fi
    echo "编译完成"
    echo ""
fi

# 检查CAN接口
if ! ip link show can0 > /dev/null 2>&1; then
    echo "错误: CAN接口 can0 不存在"
    echo "请先运行: sudo ./init_can0.sh"
    exit 1
fi

echo "CAN接口状态:"
ip -details link show can0 | head -3
echo ""

echo "演示说明:"
echo "========="
echo "1. 本演示将启动CAN模拟器来模拟BMS设备"
echo "2. 然后运行UDS 27客户端程序进行安全访问"
echo "3. 模拟器使用与客户端相同的AES密钥，所以验证会成功"
echo ""

echo "默认配置:"
echo "- 请求地址: 0x715 (物理地址)"
echo "- 响应地址: 0x795"
echo "- AES密钥: 标准测试向量密钥"
echo ""

read -p "按回车键开始演示..."

echo ""
echo "步骤1: 启动CAN模拟器"
echo "==================="

# 启动模拟器 (后台运行)
./can_simulator &
SIMULATOR_PID=$!

echo "CAN模拟器已启动 (PID: $SIMULATOR_PID)"
echo "等待模拟器初始化..."
sleep 2

echo ""
echo "步骤2: 运行UDS 27客户端"
echo "====================="

echo "使用默认配置 (0x715物理地址):"
echo ""

# 运行客户端
./uds_27_advanced -v

CLIENT_RESULT=$?

echo ""
echo "步骤3: 测试功能地址"
echo "=================="

if [ $CLIENT_RESULT -eq 0 ]; then
    echo "物理地址测试成功! 现在测试功能地址..."
    echo ""
    sleep 1
    
    echo "使用功能地址 (0x7DF):"
    echo ""
    ./uds_27_advanced -r 0x7DF -v
else
    echo "物理地址测试失败，跳过功能地址测试"
fi

echo ""
echo "步骤4: 清理"
echo "==========="

# 停止模拟器
echo "正在停止CAN模拟器..."
kill $SIMULATOR_PID 2>/dev/null
wait $SIMULATOR_PID 2>/dev/null

echo "演示完成!"
echo ""

echo "其他测试选项:"
echo "============"
echo "1. 手动运行模拟器: ./can_simulator"
echo "2. 手动运行客户端: ./uds_27_advanced -v"
echo "3. 使用测试脚本: ./test_uds27.sh"
echo "4. 查看帮助信息: ./uds_27_advanced -h"
echo ""

echo "注意事项:"
echo "========"
echo "- 模拟器和客户端必须使用相同的AES密钥才能验证成功"
echo "- 可以通过 -k 参数测试不同的密钥类型"
echo "- 可以通过 -c 参数使用自定义密钥"
echo "- 详细的使用说明请参考 README.md"
