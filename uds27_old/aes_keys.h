#ifndef AES_KEYS_H
#define AES_KEYS_H

#include <stdint.h>

/*
 * AES-128 密钥配置文件
 * 
 * 这个文件包含了用于UDS 27服务的各种AES密钥配置
 */

// 真实BMS系统AES密钥 (从实际通信记录中获得)
// 密钥: BE11A1C1120344052687183234B9A1A2
static const uint8_t AES_KEY_REAL_BMS[16] = {
    0xBE, 0x11, 0xA1, 0xC1, 0x12, 0x03, 0x44, 0x05,
    0x26, 0x87, 0x18, 0x32, 0x34, 0xB9, 0xA1, 0xA2
};

// 标准AES-128测试向量密钥 (NIST标准)
// 之前选中的密钥: 2b7e151628aed2a6abf7158809cf4f3c
static const uint8_t AES_KEY_NIST_TEST[16] = {
    0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
    0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
};

// 全零密钥 (用于测试)
static const uint8_t AES_KEY_ZERO[16] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// 简单模式密钥 (递增模式)
static const uint8_t AES_KEY_SIMPLE[16] = {
    0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
    0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10
};

// 常见的BMS厂商密钥模式 (示例)
static const uint8_t AES_KEY_BMS_PATTERN1[16] = {
    0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
    0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00
};

// 另一种常见模式
static const uint8_t AES_KEY_BMS_PATTERN2[16] = {
    0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A,
    0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5, 0x5A, 0xA5
};

/*
 * 密钥说明:
 * 
 * 1. AES_KEY_NIST_TEST (您选中的密钥):
 *    - 这是NIST (美国国家标准与技术研究院) 的标准AES-128测试向量
 *    - 十六进制: 2b7e151628aed2a6abf7158809cf4f3c
 *    - 广泛用于AES算法的测试和验证
 *    - 在实际产品中不应使用，因为是公开的测试密钥
 * 
 * 2. AES_KEY_ZERO:
 *    - 全零密钥，主要用于调试和测试
 *    - 安全性最低，仅用于开发阶段
 * 
 * 3. AES_KEY_SIMPLE:
 *    - 简单的递增/递减模式
 *    - 容易记忆，适合初期测试
 * 
 * 4. AES_KEY_BMS_PATTERN1/2:
 *    - 模拟真实BMS可能使用的密钥模式
 *    - 实际使用时应该替换为厂商提供的真实密钥
 * 
 * 安全建议:
 * - 生产环境中应使用随机生成的强密钥
 * - 密钥应该安全存储，不应硬编码在代码中
 * - 定期更换密钥以提高安全性
 * - 使用硬件安全模块(HSM)保护密钥
 */

// 密钥类型枚举
typedef enum {
    AES_KEY_TYPE_REAL_BMS = 0,  // 真实BMS密钥 (从实际通信记录获得)
    AES_KEY_TYPE_NIST = 1,      // NIST标准测试向量
    AES_KEY_TYPE_ZERO = 2,      // 全零密钥
    AES_KEY_TYPE_SIMPLE = 3,    // 简单模式
    AES_KEY_TYPE_BMS1 = 4,      // BMS模式1
    AES_KEY_TYPE_BMS2 = 5,      // BMS模式2
    AES_KEY_TYPE_CUSTOM = 6     // 自定义密钥
} aes_key_type_t;

// 获取指定类型的密钥
static inline const uint8_t* get_aes_key_by_type(aes_key_type_t type) {
    switch (type) {
        case AES_KEY_TYPE_NIST:
            return AES_KEY_NIST_TEST;
        case AES_KEY_TYPE_ZERO:
            return AES_KEY_ZERO;
        case AES_KEY_TYPE_SIMPLE:
            return AES_KEY_SIMPLE;
        case AES_KEY_TYPE_BMS1:
            return AES_KEY_BMS_PATTERN1;
        case AES_KEY_TYPE_BMS2:
            return AES_KEY_BMS_PATTERN2;
        case AES_KEY_TYPE_REAL_BMS:
        default:
            return AES_KEY_REAL_BMS;
    }
}

// 获取密钥类型的描述
static inline const char* get_aes_key_description(aes_key_type_t type) {
    switch (type) {
        case AES_KEY_TYPE_REAL_BMS:
            return "真实BMS密钥 (BE11A1C1120344052687183234B9A1A2)";
        case AES_KEY_TYPE_NIST:
            return "NIST标准测试向量 (2b7e151628aed2a6abf7158809cf4f3c)";
        case AES_KEY_TYPE_ZERO:
            return "全零密钥 (用于测试)";
        case AES_KEY_TYPE_SIMPLE:
            return "简单递增模式";
        case AES_KEY_TYPE_BMS1:
            return "BMS模式1 (示例)";
        case AES_KEY_TYPE_BMS2:
            return "BMS模式2 (示例)";
        case AES_KEY_TYPE_CUSTOM:
            return "自定义密钥";
        default:
            return "未知密钥类型";
    }
}

// 打印密钥信息
static inline void print_aes_key_info(aes_key_type_t type, const uint8_t* key) {
    printf("AES密钥信息:\n");
    printf("  类型: %s\n", get_aes_key_description(type));
    printf("  密钥: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", key[i]);
        if (i == 7) printf(" ");
    }
    printf("\n");
}

#endif // AES_KEYS_H
