#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <ifaddrs.h>
#include "aes/aes.h"
#include "bms_aes.h"

#define CAN_INTERFACE "can0"
#define UDS_REQUEST_ID 0x715    // 物理地址 (可以替换为 0x7DF 功能地址)
#define UDS_RESPONSE_ID 0x795
#define UDS_SERVICE_27 0x27
#define UDS_SUBFUNCTION_REQUEST_SEED 0x03
#define UDS_SUBFUNCTION_SEND_KEY 0x04

// AES密钥 - 这里需要根据实际情况设置正确的密钥
static const uint8_t aes_key[16] = {
    0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
    0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
};

typedef struct {
    int socket;
    struct sockaddr_can addr;
} can_context_t;

// 初始化CAN接口
int init_can(can_context_t *ctx) {
    struct ifreq ifr;
    
    ctx->socket = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (ctx->socket < 0) {
        perror("socket");
        return -1;
    }
    
    strcpy(ifr.ifr_name, CAN_INTERFACE);
    ioctl(ctx->socket, SIOCGIFINDEX, &ifr);
    
    ctx->addr.can_family = AF_CAN;
    ctx->addr.can_ifindex = ifr.ifr_ifindex;
    
    if (bind(ctx->socket, (struct sockaddr *)&ctx->addr, sizeof(ctx->addr)) < 0) {
        perror("bind");
        close(ctx->socket);
        return -1;
    }
    
    return 0;
}

// 发送CAN帧
int send_can_frame(can_context_t *ctx, uint32_t id, uint8_t *data, uint8_t len) {
    struct can_frame frame;
    
    frame.can_id = id;
    frame.can_dlc = len;
    memcpy(frame.data, data, len);
    
    if (write(ctx->socket, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        perror("write");
        return -1;
    }
    
    printf("发送: can0  %03X   [%d] ", id, len);
    for (int i = 0; i < len; i++) {
        printf(" %02X", data[i]);
    }
    printf("\n");
    
    return 0;
}

// 接收CAN帧
int receive_can_frame(can_context_t *ctx, struct can_frame *frame, int timeout_ms) {
    fd_set readfds;
    struct timeval timeout;
    
    FD_ZERO(&readfds);
    FD_SET(ctx->socket, &readfds);
    
    timeout.tv_sec = timeout_ms / 1000;
    timeout.tv_usec = (timeout_ms % 1000) * 1000;
    
    int ret = select(ctx->socket + 1, &readfds, NULL, NULL, &timeout);
    if (ret <= 0) {
        return -1; // 超时或错误
    }
    
    if (read(ctx->socket, frame, sizeof(struct can_frame)) < 0) {
        perror("read");
        return -1;
    }
    
    printf("接收: can0  %03X   [%d] ", frame->can_id, frame->can_dlc);
    for (int i = 0; i < frame->can_dlc; i++) {
        printf(" %02X", frame->data[i]);
    }
    printf("\n");
    
    return 0;
}

// 使用标准AES算法计算key (用于UDS 27 04发送)
void calculate_key_with_aes(const uint8_t *seed, uint8_t *key) {
    struct AES_ctx ctx;

    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);

    // 复制seed到key缓冲区
    memcpy(key, seed, 16);

    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);

    printf("Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", seed[i]);
    }
    printf("\n");

    printf("Key:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", key[i]);
    }
    printf(" (标准AES结果)\n");
}

// 发送UDS 27 03请求获取seed
int request_seed(can_context_t *ctx, uint8_t *seed) {
    uint8_t request[3] = {0x02, UDS_SERVICE_27, UDS_SUBFUNCTION_REQUEST_SEED};
    struct can_frame response;
    uint8_t multi_frame_data[256];
    int total_length = 0;
    
    // 发送请求
    if (send_can_frame(ctx, UDS_REQUEST_ID, request, 3) < 0) {
        return -1;
    }
    
    // 接收响应
    if (receive_can_frame(ctx, &response, 1000) < 0) {
        printf("接收响应超时\n");
        return -1;
    }
    
    // 检查是否是多帧响应
    if (response.data[0] == 0x10) {
        // 第一帧
        total_length = response.data[1];
        memcpy(multi_frame_data, &response.data[2], response.can_dlc - 2);
        int received_bytes = response.can_dlc - 2;
        
        // 发送流控制帧
        uint8_t flow_control[3] = {0x30, 0x00, 0x00};
        send_can_frame(ctx, UDS_REQUEST_ID, flow_control, 3);
        
        // 接收后续帧
        while (received_bytes < total_length) {
            if (receive_can_frame(ctx, &response, 1000) < 0) {
                printf("接收后续帧超时\n");
                return -1;
            }
            
            if ((response.data[0] & 0xF0) == 0x20) {
                // 后续帧
                int copy_bytes = (total_length - received_bytes > response.can_dlc - 1) ? 
                                response.can_dlc - 1 : total_length - received_bytes;
                memcpy(&multi_frame_data[received_bytes], &response.data[1], copy_bytes);
                received_bytes += copy_bytes;
            }
        }
        
        // 检查响应
        if (multi_frame_data[0] == 0x67 && multi_frame_data[1] == UDS_SUBFUNCTION_REQUEST_SEED) {
            memcpy(seed, &multi_frame_data[2], 16);
            return 0;
        }
    } else if (response.data[0] >= 0x06) {
        // 单帧响应 - 但seed是16字节，不可能在单帧中传输
        // 这里只是为了兼容性，实际上seed总是通过多帧传输
        printf("收到单帧响应，但seed需要16字节，应该使用多帧传输\n");
        return -1;
    }
    
    printf("获取seed失败\n");
    return -1;
}

// 发送UDS 27 04请求发送key
int send_key(can_context_t *ctx, const uint8_t *key) {
    uint8_t first_frame[8] = {0x10, 0x12, UDS_SERVICE_27, UDS_SUBFUNCTION_SEND_KEY};
    uint8_t second_frame[8] = {0x21};
    uint8_t third_frame[8] = {0x22}; // 增加缓冲区大小
    struct can_frame response;

    // 复制key的前4个字节到第一帧 (从first_frame[4]开始)
    first_frame[4] = key[0];
    first_frame[5] = key[1];
    first_frame[6] = key[2];
    first_frame[7] = key[3];

    // 复制key的中间7个字节到第二帧
    for (int i = 0; i < 7; i++) {
        second_frame[1 + i] = key[4 + i];
    }

    // 复制key的最后5个字节到第三帧
    for (int i = 0; i < 5; i++) {
        third_frame[1 + i] = key[11 + i];
    }
    
    // 发送第一帧
    if (send_can_frame(ctx, UDS_REQUEST_ID, first_frame, 8) < 0) {
        return -1;
    }
    
    // 等待流控制帧
    if (receive_can_frame(ctx, &response, 1000) < 0) {
        printf("接收流控制帧超时\n");
        return -1;
    }
    
    if (response.data[0] != 0x30) {
        printf("未收到正确的流控制帧\n");
        return -1;
    }
    
    // 发送第二帧
    if (send_can_frame(ctx, UDS_REQUEST_ID, second_frame, 8) < 0) {
        return -1;
    }
    
    // 发送第三帧 (只发送5个字节的key数据 + 1个序列号 = 6字节)
    if (send_can_frame(ctx, UDS_REQUEST_ID, third_frame, 6) < 0) {
        return -1;
    }
    
    // 接收响应
    if (receive_can_frame(ctx, &response, 1000) < 0) {
        printf("接收响应超时\n");
        return -1;
    }
    
    // 检查响应
    if (response.data[0] == 0x03 && response.data[1] == 0x7F && 
        response.data[2] == UDS_SERVICE_27 && response.data[3] == 0x7F) {
        printf("Key验证失败 - 收到错误响应\n");
        return -1;
    } else if (response.data[1] == 0x67 && response.data[2] == UDS_SUBFUNCTION_SEND_KEY) {
        printf("Key验证成功!\n");
        return 0;
    }
    
    printf("未知响应\n");
    return -1;
}

int main() {
    can_context_t can_ctx;
    uint8_t seed[16];
    uint8_t key[16];
    
    printf("UDS 27服务 - 安全访问测试程序\n");
    printf("================================\n");
    
    // 初始化CAN接口
    if (init_can(&can_ctx) < 0) {
        printf("初始化CAN接口失败\n");
        return -1;
    }
    
    printf("CAN接口初始化成功\n\n");
    
    // 步骤1: 请求seed
    printf("步骤1: 请求seed...\n");
    if (request_seed(&can_ctx, seed) < 0) {
        printf("请求seed失败\n");
        close(can_ctx.socket);
        return -1;
    }
    
    printf("成功获取seed\n\n");
    
    // 步骤2: 计算key
    printf("步骤2: 使用AES128计算key...\n");
    calculate_key_with_aes(seed, key);
    printf("\n");
    
    // 步骤3: 发送key
    printf("步骤3: 发送key进行验证...\n");
    if (send_key(&can_ctx, key) < 0) {
        printf("Key验证失败\n");
        close(can_ctx.socket);
        return -1;
    }
    
    printf("\nUDS 27服务完成\n");
    close(can_ctx.socket);
    return 0;
}
