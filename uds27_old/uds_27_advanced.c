#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <getopt.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <ifaddrs.h>
#include "aes/aes.h"
#include "uds_config.h"
#include "bms_aes.h"

typedef struct {
    int socket;
    struct sockaddr_can addr;
    uds_config_t config;
} can_context_t;

// 打印使用说明
void print_usage(const char *program_name) {
    printf("用法: %s [选项]\n", program_name);
    printf("选项:\n");
    printf("  -i, --interface=IFACE   CAN接口名称 (默认: can0)\n");
    printf("  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)\n");
    printf("  -s, --response-id=ID    响应CAN ID (默认: 0x795)\n");
    printf("  -k, --key-type=TYPE     AES密钥类型:\n");
    printf("                          0=真实BMS, 1=NIST标准, 2=全零, 3=简单, 4=BMS1, 5=BMS2, 6=自定义\n");
    printf("  -c, --custom-key=KEY    自定义AES密钥 (32位十六进制字符串)\n");
    printf("  -t, --timeout=MS        超时时间毫秒 (默认: 1000)\n");
    printf("  -v, --verbose           详细输出\n");
    printf("  -h, --help              显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                                    # 使用默认配置\n", program_name);
    printf("  %s -r 0x7DF                          # 使用功能地址\n", program_name);
    printf("  %s -k 1                              # 使用全零密钥\n", program_name);
    printf("  %s -c 0123456789ABCDEFFEDCBA9876543210  # 使用自定义密钥\n", program_name);
}

// 解析十六进制字符串到字节数组
int parse_hex_string(const char *hex_str, uint8_t *bytes, int max_bytes) {
    int len = strlen(hex_str);
    if (len != max_bytes * 2) {
        return -1;
    }
    
    for (int i = 0; i < max_bytes; i++) {
        char hex_byte[3] = {hex_str[i*2], hex_str[i*2+1], '\0'};
        bytes[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }
    
    return 0;
}

// 解析命令行参数
int parse_arguments(int argc, char *argv[], uds_config_t *config, char *interface, int *verbose) {
    static struct option long_options[] = {
        {"interface", required_argument, 0, 'i'},
        {"request-id", required_argument, 0, 'r'},
        {"response-id", required_argument, 0, 's'},
        {"key-type", required_argument, 0, 'k'},
        {"custom-key", required_argument, 0, 'c'},
        {"timeout", required_argument, 0, 't'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    *config = DEFAULT_CONFIG;
    strcpy(interface, CAN_INTERFACE);
    *verbose = 0;
    
    int c;
    while ((c = getopt_long(argc, argv, "i:r:s:k:c:t:vh", long_options, NULL)) != -1) {
        switch (c) {
            case 'i':
                strcpy(interface, optarg);
                break;
            case 'r':
                config->request_id = strtol(optarg, NULL, 0);
                break;
            case 's':
                config->response_id = strtol(optarg, NULL, 0);
                break;
            case 'k':
                config->key_type = atoi(optarg);
                if (config->key_type < 0 || config->key_type > 6) {
                    fprintf(stderr, "错误: 无效的密钥类型 %d\n", config->key_type);
                    return -1;
                }
                break;
            case 'c':
                config->key_type = AES_KEY_TYPE_CUSTOM;
                if (parse_hex_string(optarg, config->custom_key, 16) < 0) {
                    fprintf(stderr, "错误: 无效的自定义密钥格式\n");
                    return -1;
                }
                break;
            case 't':
                config->timeout_ms = atoi(optarg);
                break;
            case 'v':
                *verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                exit(0);
            case '?':
                return -1;
            default:
                abort();
        }
    }
    
    return 0;
}

// 初始化CAN接口
int init_can(can_context_t *ctx, const char *interface) {
    struct ifreq ifr;
    
    ctx->socket = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (ctx->socket < 0) {
        perror("socket");
        return -1;
    }
    
    strcpy(ifr.ifr_name, interface);
    if (ioctl(ctx->socket, SIOCGIFINDEX, &ifr) < 0) {
        perror("ioctl SIOCGIFINDEX");
        close(ctx->socket);
        return -1;
    }
    
    ctx->addr.can_family = AF_CAN;
    ctx->addr.can_ifindex = ifr.ifr_ifindex;
    
    if (bind(ctx->socket, (struct sockaddr *)&ctx->addr, sizeof(ctx->addr)) < 0) {
        perror("bind");
        close(ctx->socket);
        return -1;
    }
    
    return 0;
}

// 发送CAN帧
int send_can_frame(can_context_t *ctx, uint32_t id, uint8_t *data, uint8_t len, int verbose) {
    struct can_frame frame;
    
    frame.can_id = id;
    frame.can_dlc = len;
    memcpy(frame.data, data, len);
    
    if (write(ctx->socket, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        perror("write");
        return -1;
    }
    
    if (verbose) {
        printf("发送: can0  %03X   [%d] ", id, len);
        for (int i = 0; i < len; i++) {
            printf(" %02X", data[i]);
        }
        printf("\n");
    }
    
    return 0;
}

// 接收CAN帧
int receive_can_frame(can_context_t *ctx, struct can_frame *frame, int timeout_ms, int verbose) {
    fd_set readfds;
    struct timeval timeout;
    
    FD_ZERO(&readfds);
    FD_SET(ctx->socket, &readfds);
    
    timeout.tv_sec = timeout_ms / 1000;
    timeout.tv_usec = (timeout_ms % 1000) * 1000;
    
    int ret = select(ctx->socket + 1, &readfds, NULL, NULL, &timeout);
    if (ret <= 0) {
        return -1; // 超时或错误
    }
    
    if (read(ctx->socket, frame, sizeof(struct can_frame)) < 0) {
        perror("read");
        return -1;
    }
    
    if (verbose) {
        printf("接收: can0  %03X   [%d] ", frame->can_id, frame->can_dlc);
        for (int i = 0; i < frame->can_dlc; i++) {
            printf(" %02X", frame->data[i]);
        }
        printf("\n");
    }
    
    return 0;
}

// 使用标准AES算法计算key (用于UDS 27 04发送)
void calculate_key_with_aes(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key, int verbose) {
    struct AES_ctx ctx;

    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);

    // 复制seed到key缓冲区
    memcpy(key, seed, 16);

    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);

    if (verbose) {
        printf("UDS 27服务Key计算过程:\n");
        printf("=====================\n");
        printf("输入Seed: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", seed[i]);
        }
        printf("\n");

        printf("AES密钥:  ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", aes_key[i]);
        }
        printf("\n");

        printf("发送Key:  ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", key[i]);
        }
        printf(" (标准AES结果)\n");

        // 显示BMS后处理结果作为对比
        uint8_t bms_key[16];
        bms_aes_encrypt(seed, bms_key, aes_key);
        printf("BMS验证:  ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", bms_key[i]);
        }
        printf(" (BMS内部验证用)\n\n");
    }
}

// 发送UDS 27 03请求获取seed
int request_seed(can_context_t *ctx, uint8_t *seed, int verbose) {
    uint8_t request[3] = {0x02, UDS_SERVICE_27, UDS_SUBFUNCTION_REQUEST_SEED};
    struct can_frame response;
    uint8_t multi_frame_data[256];
    int total_length = 0;
    
    // 发送请求
    if (send_can_frame(ctx, ctx->config.request_id, request, 3, verbose) < 0) {
        return -1;
    }
    
    // 接收响应
    if (receive_can_frame(ctx, &response, ctx->config.timeout_ms, verbose) < 0) {
        printf("接收响应超时\n");
        return -1;
    }
    
    // 检查响应ID
    if (response.can_id != ctx->config.response_id) {
        if (verbose) {
            printf("忽略非目标响应ID: 0x%03X\n", response.can_id);
        }
        return -1;
    }
    
    // 检查是否是多帧响应
    if (response.data[0] == 0x10) {
        // 第一帧
        total_length = response.data[1];
        memcpy(multi_frame_data, &response.data[2], response.can_dlc - 2);
        int received_bytes = response.can_dlc - 2;
        
        // 发送流控制帧
        uint8_t flow_control[3] = {0x30, 0x00, 0x00};
        send_can_frame(ctx, ctx->config.request_id, flow_control, 3, verbose);
        
        // 接收后续帧
        while (received_bytes < total_length) {
            if (receive_can_frame(ctx, &response, FLOW_CONTROL_TIMEOUT_MS, verbose) < 0) {
                printf("接收后续帧超时\n");
                return -1;
            }
            
            if ((response.data[0] & 0xF0) == 0x20) {
                // 后续帧
                int copy_bytes = (total_length - received_bytes > response.can_dlc - 1) ? 
                                response.can_dlc - 1 : total_length - received_bytes;
                memcpy(&multi_frame_data[received_bytes], &response.data[1], copy_bytes);
                received_bytes += copy_bytes;
            }
        }
        
        // 检查响应
        if (multi_frame_data[0] == 0x67 && multi_frame_data[1] == UDS_SUBFUNCTION_REQUEST_SEED) {
            memcpy(seed, &multi_frame_data[2], 16);
            return 0;
        }
    } else if (response.data[0] >= 0x06) {
        // 单帧响应 - 但seed是16字节，不可能在单帧中传输
        // 这里只是为了兼容性，实际上seed总是通过多帧传输
        if (verbose) {
            printf("收到单帧响应，但seed需要16字节，应该使用多帧传输\n");
        }
        return -1;
    }
    
    // 检查错误响应
    if (response.data[0] == 0x03 && response.data[1] == 0x7F && response.data[2] == UDS_SERVICE_27) {
        printf("UDS错误响应: NRC=0x%02X\n", response.data[3]);
        return -1;
    }
    
    printf("获取seed失败 - 未知响应格式\n");
    return -1;
}

// 发送UDS 27 04请求发送key
int send_key(can_context_t *ctx, const uint8_t *key, int verbose) {
    uint8_t first_frame[8] = {0x10, 0x12, UDS_SERVICE_27, UDS_SUBFUNCTION_SEND_KEY};
    uint8_t second_frame[8] = {0x21};
    uint8_t third_frame[8] = {0x22}; // 增加缓冲区大小
    struct can_frame response;

    // 复制key的前4个字节到第一帧 (从first_frame[4]开始)
    first_frame[4] = key[0];
    first_frame[5] = key[1];
    first_frame[6] = key[2];
    first_frame[7] = key[3];

    // 复制key的中间7个字节到第二帧
    for (int i = 0; i < 7; i++) {
        second_frame[1 + i] = key[4 + i];
    }

    // 复制key的最后5个字节到第三帧
    for (int i = 0; i < 5; i++) {
        third_frame[1 + i] = key[11 + i];
    }

    // 发送第一帧
    if (send_can_frame(ctx, ctx->config.request_id, first_frame, 8, verbose) < 0) {
        return -1;
    }

    // 等待流控制帧
    if (receive_can_frame(ctx, &response, FLOW_CONTROL_TIMEOUT_MS, verbose) < 0) {
        printf("接收流控制帧超时\n");
        return -1;
    }

    if (response.data[0] != 0x30) {
        printf("未收到正确的流控制帧\n");
        return -1;
    }

    // 发送第二帧
    if (send_can_frame(ctx, ctx->config.request_id, second_frame, 8, verbose) < 0) {
        return -1;
    }

    // 发送第三帧 (只发送5个字节的key数据 + 1个序列号 = 6字节)
    if (send_can_frame(ctx, ctx->config.request_id, third_frame, 6, verbose) < 0) {
        return -1;
    }

    // 接收响应
    if (receive_can_frame(ctx, &response, ctx->config.timeout_ms, verbose) < 0) {
        printf("接收响应超时\n");
        return -1;
    }

    // 检查响应
    if (response.data[0] == 0x03 && response.data[1] == 0x7F &&
        response.data[2] == UDS_SERVICE_27) {
        printf("Key验证失败 - NRC: 0x%02X\n", response.data[3]);
        return -1;
    } else if (response.data[1] == 0x67 && response.data[2] == UDS_SUBFUNCTION_SEND_KEY) {
        printf("Key验证成功!\n");
        return 0;
    }

    printf("未知响应格式\n");
    return -1;
}

int main(int argc, char *argv[]) {
    can_context_t can_ctx;
    uint8_t seed[16];
    uint8_t key[16];
    char interface[16];
    int verbose = 0;

    printf("UDS 27服务 - 安全访问高级测试程序\n");
    printf("====================================\n");

    // 解析命令行参数
    if (parse_arguments(argc, argv, &can_ctx.config, interface, &verbose) < 0) {
        print_usage(argv[0]);
        return -1;
    }

    // 获取AES密钥
    const uint8_t *aes_key = get_aes_key(can_ctx.config.key_type, can_ctx.config.custom_key);

    // 显示配置信息
    if (verbose) {
        printf("配置信息:\n");
        printf("  CAN接口: %s\n", interface);
        printf("  请求ID: 0x%03X\n", can_ctx.config.request_id);
        printf("  响应ID: 0x%03X\n", can_ctx.config.response_id);
        printf("  超时时间: %d ms\n", can_ctx.config.timeout_ms);
        printf("\n");
        print_aes_key_info(can_ctx.config.key_type, aes_key);
        printf("\n");
    }

    // 初始化CAN接口
    if (init_can(&can_ctx, interface) < 0) {
        printf("初始化CAN接口失败\n");
        return -1;
    }

    printf("CAN接口 %s 初始化成功\n\n", interface);

    // 步骤1: 请求seed
    printf("步骤1: 请求seed...\n");
    if (request_seed(&can_ctx, seed, verbose) < 0) {
        printf("请求seed失败\n");
        close(can_ctx.socket);
        return -1;
    }

    printf("成功获取seed\n\n");

    // 步骤2: 计算key
    printf("步骤2: 使用AES128计算key...\n");
    calculate_key_with_aes(seed, key, aes_key, verbose);
    printf("\n");

    // 步骤3: 发送key
    printf("步骤3: 发送key进行验证...\n");
    if (send_key(&can_ctx, key, verbose) < 0) {
        printf("Key验证失败\n");
        close(can_ctx.socket);
        return -1;
    }

    printf("\nUDS 27服务完成\n");
    close(can_ctx.socket);
    return 0;
}
