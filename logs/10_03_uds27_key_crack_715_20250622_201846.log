UDS 27服务AES密钥破解 - 2025-06-22 20:18:46.310091
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:18:46.310] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:18:46.310] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:46.310] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:46.310] ✅ UDS 10 01 成功 (第1次尝试)
[20:18:46.811] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:18:46.811] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:18:46.813] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:18:46.813] ✅ UDS 10 03 成功 (第1次尝试)
[20:18:46.813] === 第  1次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:18:46.813] 第  1次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:18:46.813] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:18:46.813] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:46.814] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:46.814] ✅ UDS 10 01 成功 (第1次尝试)
[20:18:47.815] 第  1次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:18:47.815] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:18:47.815] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:18:47.816] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:18:47.816] ✅ UDS 10 03 成功 (第1次尝试)
[20:18:48.817] 第  1次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:18:48.817] 第  1次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:18:48.826] 第  1次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:18:48.826] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:18:48.826] === 第  1次测试结束 ===
[20:18:50.827] === 第  2次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:18:50.827] 第  2次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:18:50.827] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:18:50.827] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:50.827] 接收: can0  715   [3]  02 27 03
[20:18:50.827] 接收: can0  795   [8]  10 12 67 03 E3 49 0D B1
[20:18:50.827] ❌ UDS 10 01 失败 (第1次尝试)
[20:18:50.827] 等待1秒后重试...
[20:18:51.828] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:18:51.828] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:51.828] 接收: can0  715   [3]  30 00 00
[20:18:51.828] 接收: can0  795   [8]  21 92 46 FB 77 87 72 4D
[20:18:51.828] ❌ UDS 10 01 失败 (第2次尝试)
[20:18:51.828] 等待1秒后重试...
[20:18:52.829] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:18:52.829] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:52.829] 接收: can0  795   [8]  22 F5 C0 58 DD 8E 55 55
[20:18:52.829] ❌ UDS 10 01 失败 (第3次尝试)
[20:18:52.829] ❌ UDS 10 01 最终失败 (已重试3次)
[20:18:52.829] 第  2次: UDS 10 01 最终失败，测试终止
[20:18:52.830] === 第  3次测试开始: 密钥 00000000000000000000000000000000 ===
[20:18:52.830] 第  3次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:18:52.830] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:18:52.830] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:52.830] 接收: can0  715   [8]  10 12 27 04 77 AA 8A 9F
[20:18:52.830] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:18:52.830] ❌ UDS 10 01 失败 (第1次尝试)
[20:18:52.830] 等待1秒后重试...
[20:18:53.831] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:18:53.831] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:53.831] 接收: can0  715   [8]  21 20 82 CA A6 4D 71 01
[20:18:53.831] 接收: can0  715   [6]  22 82 FE 05 9F 37
[20:18:53.831] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[20:18:53.831] ❌ UDS 10 01 失败 (第2次尝试)
[20:18:53.831] 等待1秒后重试...
[20:18:54.832] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:18:54.832] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:54.832] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:54.832] ✅ UDS 10 01 成功 (第3次尝试)
[20:18:55.833] 第  3次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:18:55.833] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:18:55.833] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:18:55.833] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:55.833] ✅ UDS 10 03 成功 (第1次尝试)
[20:18:56.834] 第  3次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[20:18:56.835] 第  3次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[20:18:56.843] 第  3次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[20:18:56.843] 第  3次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:18:56.843] === 第  3次测试结束 ===
[20:18:58.845] === 第  4次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:18:58.845] 第  4次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:18:58.845] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:18:58.845] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:18:58.845] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:58.845] ✅ UDS 10 01 成功 (第1次尝试)
[20:18:59.846] 第  4次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:18:59.847] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:18:59.847] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:18:59.847] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:18:59.847] ✅ UDS 10 03 成功 (第1次尝试)
[20:19:00.848] 第  4次 - 步骤3: 执行UDS 27测试 (密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
[20:19:00.848] 第  4次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF -v
[20:19:00.856] 第  4次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFFFFFFFFFFFFFFF FFFFFFFFFFFFFFFF

CAN接口 can0 初始化...
[20:19:00.856] 第  4次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ❌ 失败 (未收到 02 67 04)
[20:19:00.856] === 第  4次测试结束 ===
[20:19:02.857] === 第  5次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:19:02.857] 第  5次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:19:02.857] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:19:02.857] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:19:02.857] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:19:02.857] ✅ UDS 10 01 成功 (第1次尝试)
[20:19:03.858] 第  5次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:19:03.858] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:19:03.858] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:19:03.858] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:19:03.858] ✅ UDS 10 03 成功 (第1次尝试)
[20:19:04.859] 第  5次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:19:04.859] 第  5次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:19:04.868] 第  5次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:19:04.868] 第  5次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:19:04.868] === 第  5次测试结束 ===
[20:19:06.869] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:19:06.869] 
破解结果统计:
[20:19:06.869] 总测试密钥数: 5
[20:19:06.869] 成功密钥数: 1
[20:19:06.869] 失败密钥数: 4
[20:19:06.869] 成功率: 20.00%
