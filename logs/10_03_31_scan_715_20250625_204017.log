UDS 31服务例程控制扫描 - 2025-06-25 20:40:17.798676
请求CAN ID: 0x715
响应CAN ID: 0x795
测试例程数量: 70
============================================================

[20:40:17.798] 执行UDS 10 01 (默认会话)...
[20:40:17.798] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:40:17.799] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:40:17.799] ✅ UDS 10 01 成功
[20:40:18.300] 执行UDS 10 03 (扩展会话)...
[20:40:18.300] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:40:18.301] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:40:18.301] ✅ UDS 10 03 成功
[20:40:18.802] 测试例程 0x0001 - 启动例程...
[20:40:18.802] 发送: can0  715   [8]  04 31 01 00 01 55 55 55
[20:40:18.804] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:18.804] ❌ 例程 0x0001: 负响应 NRC=0x31
[20:40:18.904] 测试例程 0x0001 - 停止例程...
[20:40:18.904] 发送: can0  715   [8]  04 31 02 00 01 55 55 55
[20:40:18.905] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:18.905] ❌ 例程 0x0001: 负响应 NRC=0x12
[20:40:19.005] 测试例程 0x0001 - 请求结果...
[20:40:19.005] 发送: can0  715   [8]  04 31 03 00 01 55 55 55
[20:40:19.006] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.006] ❌ 例程 0x0001: 负响应 NRC=0x12
[20:40:19.106] 测试例程 0x0002 - 启动例程...
[20:40:19.106] 发送: can0  715   [8]  04 31 01 00 02 55 55 55
[20:40:19.107] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:19.107] ❌ 例程 0x0002: 负响应 NRC=0x31
[20:40:19.207] 测试例程 0x0002 - 停止例程...
[20:40:19.207] 发送: can0  715   [8]  04 31 02 00 02 55 55 55
[20:40:19.209] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.209] ❌ 例程 0x0002: 负响应 NRC=0x12
[20:40:19.309] 测试例程 0x0002 - 请求结果...
[20:40:19.309] 发送: can0  715   [8]  04 31 03 00 02 55 55 55
[20:40:19.311] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.311] ❌ 例程 0x0002: 负响应 NRC=0x12
[20:40:19.411] 测试例程 0x0003 - 启动例程...
[20:40:19.411] 发送: can0  715   [8]  04 31 01 00 03 55 55 55
[20:40:19.412] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:19.412] ❌ 例程 0x0003: 负响应 NRC=0x31
[20:40:19.512] 测试例程 0x0003 - 停止例程...
[20:40:19.512] 发送: can0  715   [8]  04 31 02 00 03 55 55 55
[20:40:19.513] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.513] ❌ 例程 0x0003: 负响应 NRC=0x12
[20:40:19.613] 测试例程 0x0003 - 请求结果...
[20:40:19.613] 发送: can0  715   [8]  04 31 03 00 03 55 55 55
[20:40:19.614] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.614] ❌ 例程 0x0003: 负响应 NRC=0x12
[20:40:19.714] 测试例程 0x0004 - 启动例程...
[20:40:19.714] 发送: can0  715   [8]  04 31 01 00 04 55 55 55
[20:40:19.715] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:19.715] ❌ 例程 0x0004: 负响应 NRC=0x31
[20:40:19.815] 测试例程 0x0004 - 停止例程...
[20:40:19.815] 发送: can0  715   [8]  04 31 02 00 04 55 55 55
[20:40:19.816] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.816] ❌ 例程 0x0004: 负响应 NRC=0x12
[20:40:19.916] 测试例程 0x0004 - 请求结果...
[20:40:19.916] 发送: can0  715   [8]  04 31 03 00 04 55 55 55
[20:40:19.917] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:19.917] ❌ 例程 0x0004: 负响应 NRC=0x12
[20:40:20.017] 测试例程 0x0005 - 启动例程...
[20:40:20.017] 发送: can0  715   [8]  04 31 01 00 05 55 55 55
[20:40:20.018] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:20.018] ❌ 例程 0x0005: 负响应 NRC=0x31
[20:40:20.118] 测试例程 0x0005 - 停止例程...
[20:40:20.118] 发送: can0  715   [8]  04 31 02 00 05 55 55 55
[20:40:20.119] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.119] ❌ 例程 0x0005: 负响应 NRC=0x12
[20:40:20.219] 测试例程 0x0005 - 请求结果...
[20:40:20.219] 发送: can0  715   [8]  04 31 03 00 05 55 55 55
[20:40:20.220] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.220] ❌ 例程 0x0005: 负响应 NRC=0x12
[20:40:20.320] 测试例程 0x0010 - 启动例程...
[20:40:20.320] 发送: can0  715   [8]  04 31 01 00 10 55 55 55
[20:40:20.321] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:20.321] ❌ 例程 0x0010: 负响应 NRC=0x31
[20:40:20.421] 测试例程 0x0010 - 停止例程...
[20:40:20.421] 发送: can0  715   [8]  04 31 02 00 10 55 55 55
[20:40:20.422] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.422] ❌ 例程 0x0010: 负响应 NRC=0x12
[20:40:20.522] 测试例程 0x0010 - 请求结果...
[20:40:20.523] 发送: can0  715   [8]  04 31 03 00 10 55 55 55
[20:40:20.524] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.524] ❌ 例程 0x0010: 负响应 NRC=0x12
[20:40:20.624] 测试例程 0x0011 - 启动例程...
[20:40:20.625] 发送: can0  715   [8]  04 31 01 00 11 55 55 55
[20:40:20.626] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:20.626] ❌ 例程 0x0011: 负响应 NRC=0x31
[20:40:20.726] 测试例程 0x0011 - 停止例程...
[20:40:20.727] 发送: can0  715   [8]  04 31 02 00 11 55 55 55
[20:40:20.728] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.728] ❌ 例程 0x0011: 负响应 NRC=0x12
[20:40:20.828] 测试例程 0x0011 - 请求结果...
[20:40:20.829] 发送: can0  715   [8]  04 31 03 00 11 55 55 55
[20:40:20.830] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:20.830] ❌ 例程 0x0011: 负响应 NRC=0x12
[20:40:20.930] 测试例程 0x0012 - 启动例程...
[20:40:20.931] 发送: can0  715   [8]  04 31 01 00 12 55 55 55
[20:40:20.931] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:20.931] ❌ 例程 0x0012: 负响应 NRC=0x31
[20:40:21.031] 测试例程 0x0012 - 停止例程...
[20:40:21.032] 发送: can0  715   [8]  04 31 02 00 12 55 55 55
[20:40:21.032] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.032] ❌ 例程 0x0012: 负响应 NRC=0x12
[20:40:21.133] 测试例程 0x0012 - 请求结果...
[20:40:21.133] 发送: can0  715   [8]  04 31 03 00 12 55 55 55
[20:40:21.133] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.133] ❌ 例程 0x0012: 负响应 NRC=0x12
[20:40:21.234] 测试例程 0x0013 - 启动例程...
[20:40:21.234] 发送: can0  715   [8]  04 31 01 00 13 55 55 55
[20:40:21.235] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:21.235] ❌ 例程 0x0013: 负响应 NRC=0x31
[20:40:21.336] 测试例程 0x0013 - 停止例程...
[20:40:21.336] 发送: can0  715   [8]  04 31 02 00 13 55 55 55
[20:40:21.336] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.336] ❌ 例程 0x0013: 负响应 NRC=0x12
[20:40:21.437] 测试例程 0x0013 - 请求结果...
[20:40:21.437] 发送: can0  715   [8]  04 31 03 00 13 55 55 55
[20:40:21.437] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.437] ❌ 例程 0x0013: 负响应 NRC=0x12
[20:40:21.538] 测试例程 0x0014 - 启动例程...
[20:40:21.538] 发送: can0  715   [8]  04 31 01 00 14 55 55 55
[20:40:21.539] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:21.539] ❌ 例程 0x0014: 负响应 NRC=0x31
[20:40:21.639] 测试例程 0x0014 - 停止例程...
[20:40:21.639] 发送: can0  715   [8]  04 31 02 00 14 55 55 55
[20:40:21.641] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.641] ❌ 例程 0x0014: 负响应 NRC=0x12
[20:40:21.741] 测试例程 0x0014 - 请求结果...
[20:40:21.741] 发送: can0  715   [8]  04 31 03 00 14 55 55 55
[20:40:21.743] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.743] ❌ 例程 0x0014: 负响应 NRC=0x12
[20:40:21.843] 测试例程 0x0020 - 启动例程...
[20:40:21.843] 发送: can0  715   [8]  04 31 01 00 20 55 55 55
[20:40:21.844] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:21.844] ❌ 例程 0x0020: 负响应 NRC=0x31
[20:40:21.944] 测试例程 0x0020 - 停止例程...
[20:40:21.944] 发送: can0  715   [8]  04 31 02 00 20 55 55 55
[20:40:21.945] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:21.945] ❌ 例程 0x0020: 负响应 NRC=0x12
[20:40:22.045] 测试例程 0x0020 - 请求结果...
[20:40:22.045] 发送: can0  715   [8]  04 31 03 00 20 55 55 55
[20:40:22.048] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.048] ❌ 例程 0x0020: 负响应 NRC=0x12
[20:40:22.149] 测试例程 0x0021 - 启动例程...
[20:40:22.149] 发送: can0  715   [8]  04 31 01 00 21 55 55 55
[20:40:22.150] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:22.150] ❌ 例程 0x0021: 负响应 NRC=0x31
[20:40:22.250] 测试例程 0x0021 - 停止例程...
[20:40:22.250] 发送: can0  715   [8]  04 31 02 00 21 55 55 55
[20:40:22.252] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.252] ❌ 例程 0x0021: 负响应 NRC=0x12
[20:40:22.352] 测试例程 0x0021 - 请求结果...
[20:40:22.352] 发送: can0  715   [8]  04 31 03 00 21 55 55 55
[20:40:22.354] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.354] ❌ 例程 0x0021: 负响应 NRC=0x12
[20:40:22.454] 测试例程 0x0022 - 启动例程...
[20:40:22.454] 发送: can0  715   [8]  04 31 01 00 22 55 55 55
[20:40:22.455] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:22.455] ❌ 例程 0x0022: 负响应 NRC=0x31
[20:40:22.555] 测试例程 0x0022 - 停止例程...
[20:40:22.555] 发送: can0  715   [8]  04 31 02 00 22 55 55 55
[20:40:22.557] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.557] ❌ 例程 0x0022: 负响应 NRC=0x12
[20:40:22.657] 测试例程 0x0022 - 请求结果...
[20:40:22.657] 发送: can0  715   [8]  04 31 03 00 22 55 55 55
[20:40:22.659] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.659] ❌ 例程 0x0022: 负响应 NRC=0x12
[20:40:22.759] 测试例程 0x0023 - 启动例程...
[20:40:22.759] 发送: can0  715   [8]  04 31 01 00 23 55 55 55
[20:40:22.760] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:22.760] ❌ 例程 0x0023: 负响应 NRC=0x31
[20:40:22.860] 测试例程 0x0023 - 停止例程...
[20:40:22.860] 发送: can0  715   [8]  04 31 02 00 23 55 55 55
[20:40:22.862] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.862] ❌ 例程 0x0023: 负响应 NRC=0x12
[20:40:22.962] 测试例程 0x0023 - 请求结果...
[20:40:22.962] 发送: can0  715   [8]  04 31 03 00 23 55 55 55
[20:40:22.964] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:22.964] ❌ 例程 0x0023: 负响应 NRC=0x12
[20:40:23.064] 测试例程 0x0024 - 启动例程...
[20:40:23.064] 发送: can0  715   [8]  04 31 01 00 24 55 55 55
[20:40:23.066] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:23.066] ❌ 例程 0x0024: 负响应 NRC=0x31
[20:40:23.166] 测试例程 0x0024 - 停止例程...
[20:40:23.167] 发送: can0  715   [8]  04 31 02 00 24 55 55 55
[20:40:23.168] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.168] ❌ 例程 0x0024: 负响应 NRC=0x12
[20:40:23.268] 测试例程 0x0024 - 请求结果...
[20:40:23.268] 发送: can0  715   [8]  04 31 03 00 24 55 55 55
[20:40:23.269] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.269] ❌ 例程 0x0024: 负响应 NRC=0x12
[20:40:23.369] 测试例程 0x0100 - 启动例程...
[20:40:23.369] 发送: can0  715   [8]  04 31 01 01 00 55 55 55
[20:40:23.370] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:23.370] ❌ 例程 0x0100: 负响应 NRC=0x31
[20:40:23.470] 测试例程 0x0100 - 停止例程...
[20:40:23.471] 发送: can0  715   [8]  04 31 02 01 00 55 55 55
[20:40:23.472] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.472] ❌ 例程 0x0100: 负响应 NRC=0x12
[20:40:23.572] 测试例程 0x0100 - 请求结果...
[20:40:23.573] 发送: can0  715   [8]  04 31 03 01 00 55 55 55
[20:40:23.573] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.573] ❌ 例程 0x0100: 负响应 NRC=0x12
[20:40:23.673] 测试例程 0x0101 - 启动例程...
[20:40:23.674] 发送: can0  715   [8]  04 31 01 01 01 55 55 55
[20:40:23.675] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:23.675] ❌ 例程 0x0101: 负响应 NRC=0x31
[20:40:23.775] 测试例程 0x0101 - 停止例程...
[20:40:23.776] 发送: can0  715   [8]  04 31 02 01 01 55 55 55
[20:40:23.776] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.776] ❌ 例程 0x0101: 负响应 NRC=0x12
[20:40:23.876] 测试例程 0x0101 - 请求结果...
[20:40:23.877] 发送: can0  715   [8]  04 31 03 01 01 55 55 55
[20:40:23.877] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:23.877] ❌ 例程 0x0101: 负响应 NRC=0x12
[20:40:23.978] 测试例程 0x0102 - 启动例程...
[20:40:23.978] 发送: can0  715   [8]  04 31 01 01 02 55 55 55
[20:40:23.979] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:23.979] ❌ 例程 0x0102: 负响应 NRC=0x31
[20:40:24.080] 测试例程 0x0102 - 停止例程...
[20:40:24.080] 发送: can0  715   [8]  04 31 02 01 02 55 55 55
[20:40:24.081] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.081] ❌ 例程 0x0102: 负响应 NRC=0x12
[20:40:24.182] 测试例程 0x0102 - 请求结果...
[20:40:24.182] 发送: can0  715   [8]  04 31 03 01 02 55 55 55
[20:40:24.182] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.182] ❌ 例程 0x0102: 负响应 NRC=0x12
[20:40:24.283] 测试例程 0x0103 - 启动例程...
[20:40:24.283] 发送: can0  715   [8]  04 31 01 01 03 55 55 55
[20:40:24.283] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:24.283] ❌ 例程 0x0103: 负响应 NRC=0x31
[20:40:24.384] 测试例程 0x0103 - 停止例程...
[20:40:24.384] 发送: can0  715   [8]  04 31 02 01 03 55 55 55
[20:40:24.385] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.386] ❌ 例程 0x0103: 负响应 NRC=0x12
[20:40:24.486] 测试例程 0x0103 - 请求结果...
[20:40:24.486] 发送: can0  715   [8]  04 31 03 01 03 55 55 55
[20:40:24.487] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.487] ❌ 例程 0x0103: 负响应 NRC=0x12
[20:40:24.588] 测试例程 0x0104 - 启动例程...
[20:40:24.588] 发送: can0  715   [8]  04 31 01 01 04 55 55 55
[20:40:24.588] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:24.588] ❌ 例程 0x0104: 负响应 NRC=0x31
[20:40:24.689] 测试例程 0x0104 - 停止例程...
[20:40:24.689] 发送: can0  715   [8]  04 31 02 01 04 55 55 55
[20:40:24.690] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.690] ❌ 例程 0x0104: 负响应 NRC=0x12
[20:40:24.790] 测试例程 0x0104 - 请求结果...
[20:40:24.790] 发送: can0  715   [8]  04 31 03 01 04 55 55 55
[20:40:24.791] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.791] ❌ 例程 0x0104: 负响应 NRC=0x12
[20:40:24.891] 测试例程 0x0200 - 启动例程...
[20:40:24.891] 发送: can0  715   [8]  04 31 01 02 00 55 55 55
[20:40:24.892] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:24.892] ❌ 例程 0x0200: 负响应 NRC=0x31
[20:40:24.992] 测试例程 0x0200 - 停止例程...
[20:40:24.992] 发送: can0  715   [8]  04 31 02 02 00 55 55 55
[20:40:24.993] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:24.993] ❌ 例程 0x0200: 负响应 NRC=0x12
[20:40:25.093] 测试例程 0x0200 - 请求结果...
[20:40:25.093] 发送: can0  715   [8]  04 31 03 02 00 55 55 55
[20:40:25.094] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.094] ❌ 例程 0x0200: 负响应 NRC=0x12
[20:40:25.194] 测试例程 0x0201 - 启动例程...
[20:40:25.194] 发送: can0  715   [8]  04 31 01 02 01 55 55 55
[20:40:25.196] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:25.196] ❌ 例程 0x0201: 负响应 NRC=0x31
[20:40:25.296] 测试例程 0x0201 - 停止例程...
[20:40:25.296] 发送: can0  715   [8]  04 31 02 02 01 55 55 55
[20:40:25.298] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.298] ❌ 例程 0x0201: 负响应 NRC=0x12
[20:40:25.398] 测试例程 0x0201 - 请求结果...
[20:40:25.398] 发送: can0  715   [8]  04 31 03 02 01 55 55 55
[20:40:25.400] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.400] ❌ 例程 0x0201: 负响应 NRC=0x12
[20:40:25.500] 测试例程 0x0202 - 启动例程...
[20:40:25.500] 发送: can0  715   [8]  04 31 01 02 02 55 55 55
[20:40:25.502] 接收: can0  795   [8]  03 7F 31 13 55 55 55 55
[20:40:25.502] ❌ 例程 0x0202: 负响应 NRC=0x13
[20:40:25.602] 测试例程 0x0202 - 停止例程...
[20:40:25.602] 发送: can0  715   [8]  04 31 02 02 02 55 55 55
[20:40:25.604] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.604] ❌ 例程 0x0202: 负响应 NRC=0x12
[20:40:25.704] 测试例程 0x0202 - 请求结果...
[20:40:25.704] 发送: can0  715   [8]  04 31 03 02 02 55 55 55
[20:40:25.705] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.705] ❌ 例程 0x0202: 负响应 NRC=0x12
[20:40:25.805] 测试例程 0x0203 - 启动例程...
[20:40:25.805] 发送: can0  715   [8]  04 31 01 02 03 55 55 55
[20:40:25.806] 接收: can0  795   [8]  05 71 01 02 03 00 55 55
[20:40:25.806] ✅ 例程 0x0203 (启动例程): 01 02 03 00 55 55
[20:40:25.906] 测试例程 0x0203 - 停止例程...
[20:40:25.906] 发送: can0  715   [8]  04 31 02 02 03 55 55 55
[20:40:25.908] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:25.908] ❌ 例程 0x0203: 负响应 NRC=0x12
[20:40:26.008] 测试例程 0x0203 - 请求结果...
[20:40:26.008] 发送: can0  715   [8]  04 31 03 02 03 55 55 55
[20:40:26.010] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.010] ❌ 例程 0x0203: 负响应 NRC=0x12
[20:40:26.110] 测试例程 0x0204 - 启动例程...
[20:40:26.110] 发送: can0  715   [8]  04 31 01 02 04 55 55 55
[20:40:26.111] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:26.111] ❌ 例程 0x0204: 负响应 NRC=0x31
[20:40:26.211] 测试例程 0x0204 - 停止例程...
[20:40:26.211] 发送: can0  715   [8]  04 31 02 02 04 55 55 55
[20:40:26.212] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.212] ❌ 例程 0x0204: 负响应 NRC=0x12
[20:40:26.312] 测试例程 0x0204 - 请求结果...
[20:40:26.312] 发送: can0  715   [8]  04 31 03 02 04 55 55 55
[20:40:26.313] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.313] ❌ 例程 0x0204: 负响应 NRC=0x12
[20:40:26.413] 测试例程 0x1000 - 启动例程...
[20:40:26.413] 发送: can0  715   [8]  04 31 01 10 00 55 55 55
[20:40:26.414] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:26.414] ❌ 例程 0x1000: 负响应 NRC=0x31
[20:40:26.514] 测试例程 0x1000 - 停止例程...
[20:40:26.514] 发送: can0  715   [8]  04 31 02 10 00 55 55 55
[20:40:26.515] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.515] ❌ 例程 0x1000: 负响应 NRC=0x12
[20:40:26.615] 测试例程 0x1000 - 请求结果...
[20:40:26.615] 发送: can0  715   [8]  04 31 03 10 00 55 55 55
[20:40:26.616] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.616] ❌ 例程 0x1000: 负响应 NRC=0x12
[20:40:26.716] 测试例程 0x1001 - 启动例程...
[20:40:26.717] 发送: can0  715   [8]  04 31 01 10 01 55 55 55
[20:40:26.717] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:26.717] ❌ 例程 0x1001: 负响应 NRC=0x31
[20:40:26.818] 测试例程 0x1001 - 停止例程...
[20:40:26.818] 发送: can0  715   [8]  04 31 02 10 01 55 55 55
[20:40:26.819] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.819] ❌ 例程 0x1001: 负响应 NRC=0x12
[20:40:26.919] 测试例程 0x1001 - 请求结果...
[20:40:26.920] 发送: can0  715   [8]  04 31 03 10 01 55 55 55
[20:40:26.920] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:26.920] ❌ 例程 0x1001: 负响应 NRC=0x12
[20:40:27.020] 测试例程 0x1002 - 启动例程...
[20:40:27.020] 发送: can0  715   [8]  04 31 01 10 02 55 55 55
[20:40:27.021] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:27.021] ❌ 例程 0x1002: 负响应 NRC=0x31
[20:40:27.121] 测试例程 0x1002 - 停止例程...
[20:40:27.122] 发送: can0  715   [8]  04 31 02 10 02 55 55 55
[20:40:27.123] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.123] ❌ 例程 0x1002: 负响应 NRC=0x12
[20:40:27.224] 测试例程 0x1002 - 请求结果...
[20:40:27.224] 发送: can0  715   [8]  04 31 03 10 02 55 55 55
[20:40:27.224] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.225] ❌ 例程 0x1002: 负响应 NRC=0x12
[20:40:27.325] 测试例程 0x1003 - 启动例程...
[20:40:27.325] 发送: can0  715   [8]  04 31 01 10 03 55 55 55
[20:40:27.326] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:27.326] ❌ 例程 0x1003: 负响应 NRC=0x31
[20:40:27.427] 测试例程 0x1003 - 停止例程...
[20:40:27.427] 发送: can0  715   [8]  04 31 02 10 03 55 55 55
[20:40:27.427] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.427] ❌ 例程 0x1003: 负响应 NRC=0x12
[20:40:27.528] 测试例程 0x1003 - 请求结果...
[20:40:27.528] 发送: can0  715   [8]  04 31 03 10 03 55 55 55
[20:40:27.528] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.528] ❌ 例程 0x1003: 负响应 NRC=0x12
[20:40:27.629] 测试例程 0x1004 - 启动例程...
[20:40:27.629] 发送: can0  715   [8]  04 31 01 10 04 55 55 55
[20:40:27.630] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:27.630] ❌ 例程 0x1004: 负响应 NRC=0x31
[20:40:27.730] 测试例程 0x1004 - 停止例程...
[20:40:27.730] 发送: can0  715   [8]  04 31 02 10 04 55 55 55
[20:40:27.731] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.731] ❌ 例程 0x1004: 负响应 NRC=0x12
[20:40:27.831] 测试例程 0x1004 - 请求结果...
[20:40:27.831] 发送: can0  715   [8]  04 31 03 10 04 55 55 55
[20:40:27.833] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:27.833] ❌ 例程 0x1004: 负响应 NRC=0x12
[20:40:27.933] 测试例程 0x2000 - 启动例程...
[20:40:27.933] 发送: can0  715   [8]  04 31 01 20 00 55 55 55
[20:40:27.934] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:27.934] ❌ 例程 0x2000: 负响应 NRC=0x31
[20:40:28.034] 测试例程 0x2000 - 停止例程...
[20:40:28.034] 发送: can0  715   [8]  04 31 02 20 00 55 55 55
[20:40:28.035] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.035] ❌ 例程 0x2000: 负响应 NRC=0x12
[20:40:28.135] 测试例程 0x2000 - 请求结果...
[20:40:28.135] 发送: can0  715   [8]  04 31 03 20 00 55 55 55
[20:40:28.136] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.136] ❌ 例程 0x2000: 负响应 NRC=0x12
[20:40:28.236] 测试例程 0x2001 - 启动例程...
[20:40:28.236] 发送: can0  715   [8]  04 31 01 20 01 55 55 55
[20:40:28.237] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:28.237] ❌ 例程 0x2001: 负响应 NRC=0x31
[20:40:28.337] 测试例程 0x2001 - 停止例程...
[20:40:28.337] 发送: can0  715   [8]  04 31 02 20 01 55 55 55
[20:40:28.338] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.338] ❌ 例程 0x2001: 负响应 NRC=0x12
[20:40:28.438] 测试例程 0x2001 - 请求结果...
[20:40:28.438] 发送: can0  715   [8]  04 31 03 20 01 55 55 55
[20:40:28.439] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.439] ❌ 例程 0x2001: 负响应 NRC=0x12
[20:40:28.539] 测试例程 0x2002 - 启动例程...
[20:40:28.539] 发送: can0  715   [8]  04 31 01 20 02 55 55 55
[20:40:28.540] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:28.540] ❌ 例程 0x2002: 负响应 NRC=0x31
[20:40:28.640] 测试例程 0x2002 - 停止例程...
[20:40:28.640] 发送: can0  715   [8]  04 31 02 20 02 55 55 55
[20:40:28.642] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.642] ❌ 例程 0x2002: 负响应 NRC=0x12
[20:40:28.742] 测试例程 0x2002 - 请求结果...
[20:40:28.742] 发送: can0  715   [8]  04 31 03 20 02 55 55 55
[20:40:28.743] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.743] ❌ 例程 0x2002: 负响应 NRC=0x12
[20:40:28.843] 测试例程 0x2003 - 启动例程...
[20:40:28.843] 发送: can0  715   [8]  04 31 01 20 03 55 55 55
[20:40:28.845] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:28.845] ❌ 例程 0x2003: 负响应 NRC=0x31
[20:40:28.945] 测试例程 0x2003 - 停止例程...
[20:40:28.945] 发送: can0  715   [8]  04 31 02 20 03 55 55 55
[20:40:28.947] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:28.947] ❌ 例程 0x2003: 负响应 NRC=0x12
[20:40:29.047] 测试例程 0x2003 - 请求结果...
[20:40:29.047] 发送: can0  715   [8]  04 31 03 20 03 55 55 55
[20:40:29.049] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.049] ❌ 例程 0x2003: 负响应 NRC=0x12
[20:40:29.149] 测试例程 0x2004 - 启动例程...
[20:40:29.149] 发送: can0  715   [8]  04 31 01 20 04 55 55 55
[20:40:29.150] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:29.150] ❌ 例程 0x2004: 负响应 NRC=0x31
[20:40:29.250] 测试例程 0x2004 - 停止例程...
[20:40:29.250] 发送: can0  715   [8]  04 31 02 20 04 55 55 55
[20:40:29.252] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.252] ❌ 例程 0x2004: 负响应 NRC=0x12
[20:40:29.352] 测试例程 0x2004 - 请求结果...
[20:40:29.352] 发送: can0  715   [8]  04 31 03 20 04 55 55 55
[20:40:29.353] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.353] ❌ 例程 0x2004: 负响应 NRC=0x12
[20:40:29.453] 测试例程 0x3000 - 启动例程...
[20:40:29.454] 发送: can0  715   [8]  04 31 01 30 00 55 55 55
[20:40:29.455] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:29.455] ❌ 例程 0x3000: 负响应 NRC=0x31
[20:40:29.555] 测试例程 0x3000 - 停止例程...
[20:40:29.555] 发送: can0  715   [8]  04 31 02 30 00 55 55 55
[20:40:29.556] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.556] ❌ 例程 0x3000: 负响应 NRC=0x12
[20:40:29.656] 测试例程 0x3000 - 请求结果...
[20:40:29.656] 发送: can0  715   [8]  04 31 03 30 00 55 55 55
[20:40:29.657] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.657] ❌ 例程 0x3000: 负响应 NRC=0x12
[20:40:29.757] 测试例程 0x3001 - 启动例程...
[20:40:29.758] 发送: can0  715   [8]  04 31 01 30 01 55 55 55
[20:40:29.787] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:29.787] ❌ 例程 0x3001: 负响应 NRC=0x31
[20:40:29.887] 测试例程 0x3001 - 停止例程...
[20:40:29.887] 发送: can0  715   [8]  04 31 02 30 01 55 55 55
[20:40:29.888] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.888] ❌ 例程 0x3001: 负响应 NRC=0x12
[20:40:29.988] 测试例程 0x3001 - 请求结果...
[20:40:29.989] 发送: can0  715   [8]  04 31 03 30 01 55 55 55
[20:40:29.989] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:29.989] ❌ 例程 0x3001: 负响应 NRC=0x12
[20:40:30.089] 测试例程 0x3002 - 启动例程...
[20:40:30.090] 发送: can0  715   [8]  04 31 01 30 02 55 55 55
[20:40:30.090] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:30.090] ❌ 例程 0x3002: 负响应 NRC=0x31
[20:40:30.191] 测试例程 0x3002 - 停止例程...
[20:40:30.191] 发送: can0  715   [8]  04 31 02 30 02 55 55 55
[20:40:30.191] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.191] ❌ 例程 0x3002: 负响应 NRC=0x12
[20:40:30.292] 测试例程 0x3002 - 请求结果...
[20:40:30.292] 发送: can0  715   [8]  04 31 03 30 02 55 55 55
[20:40:30.292] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.292] ❌ 例程 0x3002: 负响应 NRC=0x12
[20:40:30.393] 测试例程 0x3003 - 启动例程...
[20:40:30.393] 发送: can0  715   [8]  04 31 01 30 03 55 55 55
[20:40:30.393] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:30.393] ❌ 例程 0x3003: 负响应 NRC=0x31
[20:40:30.494] 测试例程 0x3003 - 停止例程...
[20:40:30.494] 发送: can0  715   [8]  04 31 02 30 03 55 55 55
[20:40:30.494] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.494] ❌ 例程 0x3003: 负响应 NRC=0x12
[20:40:30.595] 测试例程 0x3003 - 请求结果...
[20:40:30.595] 发送: can0  715   [8]  04 31 03 30 03 55 55 55
[20:40:30.595] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.596] ❌ 例程 0x3003: 负响应 NRC=0x12
[20:40:30.696] 测试例程 0x3004 - 启动例程...
[20:40:30.696] 发送: can0  715   [8]  04 31 01 30 04 55 55 55
[20:40:30.697] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:30.697] ❌ 例程 0x3004: 负响应 NRC=0x31
[20:40:30.797] 测试例程 0x3004 - 停止例程...
[20:40:30.797] 发送: can0  715   [8]  04 31 02 30 04 55 55 55
[20:40:30.798] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.798] ❌ 例程 0x3004: 负响应 NRC=0x12
[20:40:30.898] 测试例程 0x3004 - 请求结果...
[20:40:30.898] 发送: can0  715   [8]  04 31 03 30 04 55 55 55
[20:40:30.899] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:30.899] ❌ 例程 0x3004: 负响应 NRC=0x12
[20:40:30.999] 测试例程 0xF010 - 启动例程...
[20:40:30.999] 发送: can0  715   [8]  04 31 01 F0 10 55 55 55
[20:40:31.000] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:31.000] ❌ 例程 0xF010: 负响应 NRC=0x31
[20:40:31.100] 测试例程 0xF010 - 停止例程...
[20:40:31.100] 发送: can0  715   [8]  04 31 02 F0 10 55 55 55
[20:40:31.102] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.102] ❌ 例程 0xF010: 负响应 NRC=0x12
[20:40:31.202] 测试例程 0xF010 - 请求结果...
[20:40:31.202] 发送: can0  715   [8]  04 31 03 F0 10 55 55 55
[20:40:31.203] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.203] ❌ 例程 0xF010: 负响应 NRC=0x12
[20:40:31.303] 测试例程 0xF011 - 启动例程...
[20:40:31.303] 发送: can0  715   [8]  04 31 01 F0 11 55 55 55
[20:40:31.304] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:31.304] ❌ 例程 0xF011: 负响应 NRC=0x31
[20:40:31.404] 测试例程 0xF011 - 停止例程...
[20:40:31.404] 发送: can0  715   [8]  04 31 02 F0 11 55 55 55
[20:40:31.405] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.405] ❌ 例程 0xF011: 负响应 NRC=0x12
[20:40:31.505] 测试例程 0xF011 - 请求结果...
[20:40:31.505] 发送: can0  715   [8]  04 31 03 F0 11 55 55 55
[20:40:31.506] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.506] ❌ 例程 0xF011: 负响应 NRC=0x12
[20:40:31.606] 测试例程 0xF012 - 启动例程...
[20:40:31.606] 发送: can0  715   [8]  04 31 01 F0 12 55 55 55
[20:40:31.608] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:31.608] ❌ 例程 0xF012: 负响应 NRC=0x31
[20:40:31.708] 测试例程 0xF012 - 停止例程...
[20:40:31.708] 发送: can0  715   [8]  04 31 02 F0 12 55 55 55
[20:40:31.710] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.710] ❌ 例程 0xF012: 负响应 NRC=0x12
[20:40:31.810] 测试例程 0xF012 - 请求结果...
[20:40:31.810] 发送: can0  715   [8]  04 31 03 F0 12 55 55 55
[20:40:31.811] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:31.811] ❌ 例程 0xF012: 负响应 NRC=0x12
[20:40:31.911] 测试例程 0xF013 - 启动例程...
[20:40:31.911] 发送: can0  715   [8]  04 31 01 F0 13 55 55 55
[20:40:31.912] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:31.912] ❌ 例程 0xF013: 负响应 NRC=0x31
[20:40:32.012] 测试例程 0xF013 - 停止例程...
[20:40:32.012] 发送: can0  715   [8]  04 31 02 F0 13 55 55 55
[20:40:32.013] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.013] ❌ 例程 0xF013: 负响应 NRC=0x12
[20:40:32.113] 测试例程 0xF013 - 请求结果...
[20:40:32.113] 发送: can0  715   [8]  04 31 03 F0 13 55 55 55
[20:40:32.114] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.114] ❌ 例程 0xF013: 负响应 NRC=0x12
[20:40:32.214] 测试例程 0xF014 - 启动例程...
[20:40:32.214] 发送: can0  715   [8]  04 31 01 F0 14 55 55 55
[20:40:32.215] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:32.215] ❌ 例程 0xF014: 负响应 NRC=0x31
[20:40:32.315] 测试例程 0xF014 - 停止例程...
[20:40:32.315] 发送: can0  715   [8]  04 31 02 F0 14 55 55 55
[20:40:32.316] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.316] ❌ 例程 0xF014: 负响应 NRC=0x12
[20:40:32.416] 测试例程 0xF014 - 请求结果...
[20:40:32.416] 发送: can0  715   [8]  04 31 03 F0 14 55 55 55
[20:40:32.418] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.418] ❌ 例程 0xF014: 负响应 NRC=0x12
[20:40:32.518] 测试例程 0xF020 - 启动例程...
[20:40:32.518] 发送: can0  715   [8]  04 31 01 F0 20 55 55 55
[20:40:32.519] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:32.519] ❌ 例程 0xF020: 负响应 NRC=0x31
[20:40:32.619] 测试例程 0xF020 - 停止例程...
[20:40:32.620] 发送: can0  715   [8]  04 31 02 F0 20 55 55 55
[20:40:32.620] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.620] ❌ 例程 0xF020: 负响应 NRC=0x12
[20:40:32.720] 测试例程 0xF020 - 请求结果...
[20:40:32.721] 发送: can0  715   [8]  04 31 03 F0 20 55 55 55
[20:40:32.721] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.721] ❌ 例程 0xF020: 负响应 NRC=0x12
[20:40:32.821] 测试例程 0xF021 - 启动例程...
[20:40:32.822] 发送: can0  715   [8]  04 31 01 F0 21 55 55 55
[20:40:32.823] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:32.823] ❌ 例程 0xF021: 负响应 NRC=0x31
[20:40:32.923] 测试例程 0xF021 - 停止例程...
[20:40:32.924] 发送: can0  715   [8]  04 31 02 F0 21 55 55 55
[20:40:32.924] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:32.924] ❌ 例程 0xF021: 负响应 NRC=0x12
[20:40:33.024] 测试例程 0xF021 - 请求结果...
[20:40:33.025] 发送: can0  715   [8]  04 31 03 F0 21 55 55 55
[20:40:33.025] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.025] ❌ 例程 0xF021: 负响应 NRC=0x12
[20:40:33.126] 测试例程 0xF022 - 启动例程...
[20:40:33.126] 发送: can0  715   [8]  04 31 01 F0 22 55 55 55
[20:40:33.126] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:33.126] ❌ 例程 0xF022: 负响应 NRC=0x31
[20:40:33.227] 测试例程 0xF022 - 停止例程...
[20:40:33.227] 发送: can0  715   [8]  04 31 02 F0 22 55 55 55
[20:40:33.227] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.227] ❌ 例程 0xF022: 负响应 NRC=0x12
[20:40:33.328] 测试例程 0xF022 - 请求结果...
[20:40:33.328] 发送: can0  715   [8]  04 31 03 F0 22 55 55 55
[20:40:33.328] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.328] ❌ 例程 0xF022: 负响应 NRC=0x12
[20:40:33.429] 测试例程 0xF023 - 启动例程...
[20:40:33.429] 发送: can0  715   [8]  04 31 01 F0 23 55 55 55
[20:40:33.429] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:33.429] ❌ 例程 0xF023: 负响应 NRC=0x31
[20:40:33.530] 测试例程 0xF023 - 停止例程...
[20:40:33.530] 发送: can0  715   [8]  04 31 02 F0 23 55 55 55
[20:40:33.530] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.530] ❌ 例程 0xF023: 负响应 NRC=0x12
[20:40:33.631] 测试例程 0xF023 - 请求结果...
[20:40:33.631] 发送: can0  715   [8]  04 31 03 F0 23 55 55 55
[20:40:33.631] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.632] ❌ 例程 0xF023: 负响应 NRC=0x12
[20:40:33.732] 测试例程 0xF024 - 启动例程...
[20:40:33.732] 发送: can0  715   [8]  04 31 01 F0 24 55 55 55
[20:40:33.734] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:33.734] ❌ 例程 0xF024: 负响应 NRC=0x31
[20:40:33.834] 测试例程 0xF024 - 停止例程...
[20:40:33.834] 发送: can0  715   [8]  04 31 02 F0 24 55 55 55
[20:40:33.836] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.836] ❌ 例程 0xF024: 负响应 NRC=0x12
[20:40:33.936] 测试例程 0xF024 - 请求结果...
[20:40:33.936] 发送: can0  715   [8]  04 31 03 F0 24 55 55 55
[20:40:33.937] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:33.937] ❌ 例程 0xF024: 负响应 NRC=0x12
[20:40:34.037] 测试例程 0xF100 - 启动例程...
[20:40:34.037] 发送: can0  715   [8]  04 31 01 F1 00 55 55 55
[20:40:34.038] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:34.038] ❌ 例程 0xF100: 负响应 NRC=0x31
[20:40:34.138] 测试例程 0xF100 - 停止例程...
[20:40:34.138] 发送: can0  715   [8]  04 31 02 F1 00 55 55 55
[20:40:34.139] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.139] ❌ 例程 0xF100: 负响应 NRC=0x12
[20:40:34.239] 测试例程 0xF100 - 请求结果...
[20:40:34.239] 发送: can0  715   [8]  04 31 03 F1 00 55 55 55
[20:40:34.240] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.240] ❌ 例程 0xF100: 负响应 NRC=0x12
[20:40:34.340] 测试例程 0xF101 - 启动例程...
[20:40:34.340] 发送: can0  715   [8]  04 31 01 F1 01 55 55 55
[20:40:34.342] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:34.342] ❌ 例程 0xF101: 负响应 NRC=0x31
[20:40:34.442] 测试例程 0xF101 - 停止例程...
[20:40:34.442] 发送: can0  715   [8]  04 31 02 F1 01 55 55 55
[20:40:34.443] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.443] ❌ 例程 0xF101: 负响应 NRC=0x12
[20:40:34.543] 测试例程 0xF101 - 请求结果...
[20:40:34.543] 发送: can0  715   [8]  04 31 03 F1 01 55 55 55
[20:40:34.544] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.544] ❌ 例程 0xF101: 负响应 NRC=0x12
[20:40:34.644] 测试例程 0xF102 - 启动例程...
[20:40:34.644] 发送: can0  715   [8]  04 31 01 F1 02 55 55 55
[20:40:34.645] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:34.645] ❌ 例程 0xF102: 负响应 NRC=0x31
[20:40:34.745] 测试例程 0xF102 - 停止例程...
[20:40:34.745] 发送: can0  715   [8]  04 31 02 F1 02 55 55 55
[20:40:34.746] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.746] ❌ 例程 0xF102: 负响应 NRC=0x12
[20:40:34.846] 测试例程 0xF102 - 请求结果...
[20:40:34.846] 发送: can0  715   [8]  04 31 03 F1 02 55 55 55
[20:40:34.847] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:34.847] ❌ 例程 0xF102: 负响应 NRC=0x12
[20:40:34.947] 测试例程 0xF103 - 启动例程...
[20:40:34.947] 发送: can0  715   [8]  04 31 01 F1 03 55 55 55
[20:40:34.948] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:34.948] ❌ 例程 0xF103: 负响应 NRC=0x31
[20:40:35.048] 测试例程 0xF103 - 停止例程...
[20:40:35.048] 发送: can0  715   [8]  04 31 02 F1 03 55 55 55
[20:40:35.049] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.049] ❌ 例程 0xF103: 负响应 NRC=0x12
[20:40:35.149] 测试例程 0xF103 - 请求结果...
[20:40:35.149] 发送: can0  715   [8]  04 31 03 F1 03 55 55 55
[20:40:35.151] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.151] ❌ 例程 0xF103: 负响应 NRC=0x12
[20:40:35.251] 测试例程 0xF104 - 启动例程...
[20:40:35.251] 发送: can0  715   [8]  04 31 01 F1 04 55 55 55
[20:40:35.252] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:35.252] ❌ 例程 0xF104: 负响应 NRC=0x31
[20:40:35.352] 测试例程 0xF104 - 停止例程...
[20:40:35.352] 发送: can0  715   [8]  04 31 02 F1 04 55 55 55
[20:40:35.353] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.353] ❌ 例程 0xF104: 负响应 NRC=0x12
[20:40:35.453] 测试例程 0xF104 - 请求结果...
[20:40:35.453] 发送: can0  715   [8]  04 31 03 F1 04 55 55 55
[20:40:35.454] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.454] ❌ 例程 0xF104: 负响应 NRC=0x12
[20:40:35.554] 测试例程 0xF200 - 启动例程...
[20:40:35.554] 发送: can0  715   [8]  04 31 01 F2 00 55 55 55
[20:40:35.555] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:35.555] ❌ 例程 0xF200: 负响应 NRC=0x31
[20:40:35.655] 测试例程 0xF200 - 停止例程...
[20:40:35.655] 发送: can0  715   [8]  04 31 02 F2 00 55 55 55
[20:40:35.656] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.656] ❌ 例程 0xF200: 负响应 NRC=0x12
[20:40:35.756] 测试例程 0xF200 - 请求结果...
[20:40:35.757] 发送: can0  715   [8]  04 31 03 F2 00 55 55 55
[20:40:35.758] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.758] ❌ 例程 0xF200: 负响应 NRC=0x12
[20:40:35.858] 测试例程 0xF201 - 启动例程...
[20:40:35.859] 发送: can0  715   [8]  04 31 01 F2 01 55 55 55
[20:40:35.860] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:35.860] ❌ 例程 0xF201: 负响应 NRC=0x31
[20:40:35.960] 测试例程 0xF201 - 停止例程...
[20:40:35.961] 发送: can0  715   [8]  04 31 02 F2 01 55 55 55
[20:40:35.962] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:35.962] ❌ 例程 0xF201: 负响应 NRC=0x12
[20:40:36.063] 测试例程 0xF201 - 请求结果...
[20:40:36.063] 发送: can0  715   [8]  04 31 03 F2 01 55 55 55
[20:40:36.063] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.063] ❌ 例程 0xF201: 负响应 NRC=0x12
[20:40:36.164] 测试例程 0xF202 - 启动例程...
[20:40:36.164] 发送: can0  715   [8]  04 31 01 F2 02 55 55 55
[20:40:36.164] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:36.164] ❌ 例程 0xF202: 负响应 NRC=0x31
[20:40:36.265] 测试例程 0xF202 - 停止例程...
[20:40:36.265] 发送: can0  715   [8]  04 31 02 F2 02 55 55 55
[20:40:36.265] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.265] ❌ 例程 0xF202: 负响应 NRC=0x12
[20:40:36.366] 测试例程 0xF202 - 请求结果...
[20:40:36.366] 发送: can0  715   [8]  04 31 03 F2 02 55 55 55
[20:40:36.366] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.366] ❌ 例程 0xF202: 负响应 NRC=0x12
[20:40:36.467] 测试例程 0xF203 - 启动例程...
[20:40:36.467] 发送: can0  715   [8]  04 31 01 F2 03 55 55 55
[20:40:36.467] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:36.467] ❌ 例程 0xF203: 负响应 NRC=0x31
[20:40:36.568] 测试例程 0xF203 - 停止例程...
[20:40:36.568] 发送: can0  715   [8]  04 31 02 F2 03 55 55 55
[20:40:36.568] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.568] ❌ 例程 0xF203: 负响应 NRC=0x12
[20:40:36.669] 测试例程 0xF203 - 请求结果...
[20:40:36.669] 发送: can0  715   [8]  04 31 03 F2 03 55 55 55
[20:40:36.671] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.671] ❌ 例程 0xF203: 负响应 NRC=0x12
[20:40:36.771] 测试例程 0xF204 - 启动例程...
[20:40:36.771] 发送: can0  715   [8]  04 31 01 F2 04 55 55 55
[20:40:36.773] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:36.773] ❌ 例程 0xF204: 负响应 NRC=0x31
[20:40:36.873] 测试例程 0xF204 - 停止例程...
[20:40:36.873] 发送: can0  715   [8]  04 31 02 F2 04 55 55 55
[20:40:36.875] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.875] ❌ 例程 0xF204: 负响应 NRC=0x12
[20:40:36.975] 测试例程 0xF204 - 请求结果...
[20:40:36.975] 发送: can0  715   [8]  04 31 03 F2 04 55 55 55
[20:40:36.977] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:36.977] ❌ 例程 0xF204: 负响应 NRC=0x12
[20:40:37.077] 测试例程 0xFF00 - 启动例程...
[20:40:37.077] 发送: can0  715   [8]  04 31 01 FF 00 55 55 55
[20:40:37.078] 接收: can0  795   [8]  03 7F 31 13 55 55 55 55
[20:40:37.078] ❌ 例程 0xFF00: 负响应 NRC=0x13
[20:40:37.178] 测试例程 0xFF00 - 停止例程...
[20:40:37.178] 发送: can0  715   [8]  04 31 02 FF 00 55 55 55
[20:40:37.179] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.179] ❌ 例程 0xFF00: 负响应 NRC=0x12
[20:40:37.279] 测试例程 0xFF00 - 请求结果...
[20:40:37.279] 发送: can0  715   [8]  04 31 03 FF 00 55 55 55
[20:40:37.280] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.280] ❌ 例程 0xFF00: 负响应 NRC=0x12
[20:40:37.380] 测试例程 0xFF01 - 启动例程...
[20:40:37.380] 发送: can0  715   [8]  04 31 01 FF 01 55 55 55
[20:40:37.391] 接收: can0  795   [8]  05 71 01 FF 01 00 55 55
[20:40:37.391] ✅ 例程 0xFF01 (启动例程): 01 FF 01 00 55 55
[20:40:37.491] 测试例程 0xFF01 - 停止例程...
[20:40:37.491] 发送: can0  715   [8]  04 31 02 FF 01 55 55 55
[20:40:37.493] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.493] ❌ 例程 0xFF01: 负响应 NRC=0x12
[20:40:37.593] 测试例程 0xFF01 - 请求结果...
[20:40:37.593] 发送: can0  715   [8]  04 31 03 FF 01 55 55 55
[20:40:37.594] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.594] ❌ 例程 0xFF01: 负响应 NRC=0x12
[20:40:37.694] 测试例程 0xFF02 - 启动例程...
[20:40:37.694] 发送: can0  715   [8]  04 31 01 FF 02 55 55 55
[20:40:37.695] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:37.695] ❌ 例程 0xFF02: 负响应 NRC=0x31
[20:40:37.795] 测试例程 0xFF02 - 停止例程...
[20:40:37.795] 发送: can0  715   [8]  04 31 02 FF 02 55 55 55
[20:40:37.797] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.797] ❌ 例程 0xFF02: 负响应 NRC=0x12
[20:40:37.897] 测试例程 0xFF02 - 请求结果...
[20:40:37.897] 发送: can0  715   [8]  04 31 03 FF 02 55 55 55
[20:40:37.898] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:37.898] ❌ 例程 0xFF02: 负响应 NRC=0x12
[20:40:37.998] 测试例程 0xFF03 - 启动例程...
[20:40:37.998] 发送: can0  715   [8]  04 31 01 FF 03 55 55 55
[20:40:37.999] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:37.999] ❌ 例程 0xFF03: 负响应 NRC=0x31
[20:40:38.099] 测试例程 0xFF03 - 停止例程...
[20:40:38.099] 发送: can0  715   [8]  04 31 02 FF 03 55 55 55
[20:40:38.100] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.100] ❌ 例程 0xFF03: 负响应 NRC=0x12
[20:40:38.200] 测试例程 0xFF03 - 请求结果...
[20:40:38.200] 发送: can0  715   [8]  04 31 03 FF 03 55 55 55
[20:40:38.201] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.201] ❌ 例程 0xFF03: 负响应 NRC=0x12
[20:40:38.301] 测试例程 0xFF04 - 启动例程...
[20:40:38.301] 发送: can0  715   [8]  04 31 01 FF 04 55 55 55
[20:40:38.302] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:38.302] ❌ 例程 0xFF04: 负响应 NRC=0x31
[20:40:38.402] 测试例程 0xFF04 - 停止例程...
[20:40:38.402] 发送: can0  715   [8]  04 31 02 FF 04 55 55 55
[20:40:38.404] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.404] ❌ 例程 0xFF04: 负响应 NRC=0x12
[20:40:38.504] 测试例程 0xFF04 - 请求结果...
[20:40:38.504] 发送: can0  715   [8]  04 31 03 FF 04 55 55 55
[20:40:38.505] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.505] ❌ 例程 0xFF04: 负响应 NRC=0x12
[20:40:38.605] 测试例程 0xFF10 - 启动例程...
[20:40:38.605] 发送: can0  715   [8]  04 31 01 FF 10 55 55 55
[20:40:38.606] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:38.606] ❌ 例程 0xFF10: 负响应 NRC=0x31
[20:40:38.706] 测试例程 0xFF10 - 停止例程...
[20:40:38.706] 发送: can0  715   [8]  04 31 02 FF 10 55 55 55
[20:40:38.710] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.710] ❌ 例程 0xFF10: 负响应 NRC=0x12
[20:40:38.810] 测试例程 0xFF10 - 请求结果...
[20:40:38.810] 发送: can0  715   [8]  04 31 03 FF 10 55 55 55
[20:40:38.811] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:38.811] ❌ 例程 0xFF10: 负响应 NRC=0x12
[20:40:38.911] 测试例程 0xFF11 - 启动例程...
[20:40:38.912] 发送: can0  715   [8]  04 31 01 FF 11 55 55 55
[20:40:38.912] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:38.912] ❌ 例程 0xFF11: 负响应 NRC=0x31
[20:40:39.012] 测试例程 0xFF11 - 停止例程...
[20:40:39.013] 发送: can0  715   [8]  04 31 02 FF 11 55 55 55
[20:40:39.013] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.013] ❌ 例程 0xFF11: 负响应 NRC=0x12
[20:40:39.114] 测试例程 0xFF11 - 请求结果...
[20:40:39.114] 发送: can0  715   [8]  04 31 03 FF 11 55 55 55
[20:40:39.115] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.115] ❌ 例程 0xFF11: 负响应 NRC=0x12
[20:40:39.216] 测试例程 0xFF12 - 启动例程...
[20:40:39.216] 发送: can0  715   [8]  04 31 01 FF 12 55 55 55
[20:40:39.217] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:39.217] ❌ 例程 0xFF12: 负响应 NRC=0x31
[20:40:39.318] 测试例程 0xFF12 - 停止例程...
[20:40:39.318] 发送: can0  715   [8]  04 31 02 FF 12 55 55 55
[20:40:39.318] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.318] ❌ 例程 0xFF12: 负响应 NRC=0x12
[20:40:39.419] 测试例程 0xFF12 - 请求结果...
[20:40:39.419] 发送: can0  715   [8]  04 31 03 FF 12 55 55 55
[20:40:39.419] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.419] ❌ 例程 0xFF12: 负响应 NRC=0x12
[20:40:39.520] 测试例程 0xFF13 - 启动例程...
[20:40:39.520] 发送: can0  715   [8]  04 31 01 FF 13 55 55 55
[20:40:39.522] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:39.522] ❌ 例程 0xFF13: 负响应 NRC=0x31
[20:40:39.622] 测试例程 0xFF13 - 停止例程...
[20:40:39.622] 发送: can0  715   [8]  04 31 02 FF 13 55 55 55
[20:40:39.624] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.624] ❌ 例程 0xFF13: 负响应 NRC=0x12
[20:40:39.724] 测试例程 0xFF13 - 请求结果...
[20:40:39.724] 发送: can0  715   [8]  04 31 03 FF 13 55 55 55
[20:40:39.725] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.726] ❌ 例程 0xFF13: 负响应 NRC=0x12
[20:40:39.826] 测试例程 0xFF14 - 启动例程...
[20:40:39.826] 发送: can0  715   [8]  04 31 01 FF 14 55 55 55
[20:40:39.826] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:40:39.826] ❌ 例程 0xFF14: 负响应 NRC=0x31
[20:40:39.927] 测试例程 0xFF14 - 停止例程...
[20:40:39.927] 发送: can0  715   [8]  04 31 02 FF 14 55 55 55
[20:40:39.928] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:39.928] ❌ 例程 0xFF14: 负响应 NRC=0x12
[20:40:40.028] 测试例程 0xFF14 - 请求结果...
[20:40:40.028] 发送: can0  715   [8]  04 31 03 FF 14 55 55 55
[20:40:40.029] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:40:40.029] ❌ 例程 0xFF14: 负响应 NRC=0x12
[20:40:40.129] 有效例程: 0x0203 子功能0x01 = 01 02 03 00 55 55
[20:40:40.129] 有效例程: 0xFF01 子功能0x01 = 01 FF 01 00 55 55
[20:40:40.129] 
例程扫描统计:
[20:40:40.129] 总测试例程数: 210
[20:40:40.129] 成功例程数: 2
[20:40:40.129] 失败例程数: 208
[20:40:40.129] 成功率: 0.95%
