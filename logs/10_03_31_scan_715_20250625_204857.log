UDS 31服务例程控制扫描 - 2025-06-25 20:48:57.617354
请求CAN ID: 0x715
响应CAN ID: 0x795
测试例程数量: 70
============================================================

[20:48:57.617] 执行UDS 10 01 (默认会话)...
[20:48:57.617] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:48:57.618] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:48:57.618] ✅ UDS 10 01 成功
[20:48:58.118] 执行UDS 10 03 (扩展会话)...
[20:48:58.118] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:48:58.120] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:48:58.120] ✅ UDS 10 03 成功
[20:48:58.621] 测试例程 0x0001 - 启动例程...
[20:48:58.621] 发送: can0  715   [8]  04 31 01 00 01 55 55 55
[20:48:58.622] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:48:58.622] ❌ 例程 0x0001: 负响应 NRC=0x31
[20:48:58.722] 测试例程 0x0001 - 停止例程...
[20:48:58.722] 发送: can0  715   [8]  04 31 02 00 01 55 55 55
[20:48:58.723] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:58.723] ❌ 例程 0x0001: 负响应 NRC=0x12
[20:48:58.823] 测试例程 0x0001 - 请求结果...
[20:48:58.823] 发送: can0  715   [8]  04 31 03 00 01 55 55 55
[20:48:58.824] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:58.824] ❌ 例程 0x0001: 负响应 NRC=0x12
[20:48:58.924] 测试例程 0x0002 - 启动例程...
[20:48:58.924] 发送: can0  715   [8]  04 31 01 00 02 55 55 55
[20:48:58.925] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:48:58.925] ❌ 例程 0x0002: 负响应 NRC=0x31
[20:48:59.025] 测试例程 0x0002 - 停止例程...
[20:48:59.026] 发送: can0  715   [8]  04 31 02 00 02 55 55 55
[20:48:59.026] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.026] ❌ 例程 0x0002: 负响应 NRC=0x12
[20:48:59.126] 测试例程 0x0002 - 请求结果...
[20:48:59.127] 发送: can0  715   [8]  04 31 03 00 02 55 55 55
[20:48:59.127] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.127] ❌ 例程 0x0002: 负响应 NRC=0x12
[20:48:59.227] 测试例程 0x0003 - 启动例程...
[20:48:59.228] 发送: can0  715   [8]  04 31 01 00 03 55 55 55
[20:48:59.228] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:48:59.228] ❌ 例程 0x0003: 负响应 NRC=0x31
[20:48:59.329] 测试例程 0x0003 - 停止例程...
[20:48:59.329] 发送: can0  715   [8]  04 31 02 00 03 55 55 55
[20:48:59.329] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.329] ❌ 例程 0x0003: 负响应 NRC=0x12
[20:48:59.430] 测试例程 0x0003 - 请求结果...
[20:48:59.430] 发送: can0  715   [8]  04 31 03 00 03 55 55 55
[20:48:59.433] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.433] ❌ 例程 0x0003: 负响应 NRC=0x12
[20:48:59.533] 测试例程 0x0004 - 启动例程...
[20:48:59.533] 发送: can0  715   [8]  04 31 01 00 04 55 55 55
[20:48:59.535] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:48:59.535] ❌ 例程 0x0004: 负响应 NRC=0x31
[20:48:59.635] 测试例程 0x0004 - 停止例程...
[20:48:59.635] 发送: can0  715   [8]  04 31 02 00 04 55 55 55
[20:48:59.636] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.637] ❌ 例程 0x0004: 负响应 NRC=0x12
[20:48:59.737] 测试例程 0x0004 - 请求结果...
[20:48:59.737] 发送: can0  715   [8]  04 31 03 00 04 55 55 55
[20:48:59.738] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.738] ❌ 例程 0x0004: 负响应 NRC=0x12
[20:48:59.838] 测试例程 0x0005 - 启动例程...
[20:48:59.838] 发送: can0  715   [8]  04 31 01 00 05 55 55 55
[20:48:59.840] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:48:59.840] ❌ 例程 0x0005: 负响应 NRC=0x31
[20:48:59.940] 测试例程 0x0005 - 停止例程...
[20:48:59.940] 发送: can0  715   [8]  04 31 02 00 05 55 55 55
[20:48:59.941] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:48:59.941] ❌ 例程 0x0005: 负响应 NRC=0x12
[20:49:00.041] 测试例程 0x0005 - 请求结果...
[20:49:00.041] 发送: can0  715   [8]  04 31 03 00 05 55 55 55
[20:49:00.043] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.043] ❌ 例程 0x0005: 负响应 NRC=0x12
[20:49:00.143] 测试例程 0x0010 - 启动例程...
[20:49:00.143] 发送: can0  715   [8]  04 31 01 00 10 55 55 55
[20:49:00.145] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:00.145] ❌ 例程 0x0010: 负响应 NRC=0x31
[20:49:00.245] 测试例程 0x0010 - 停止例程...
[20:49:00.245] 发送: can0  715   [8]  04 31 02 00 10 55 55 55
[20:49:00.247] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.247] ❌ 例程 0x0010: 负响应 NRC=0x12
[20:49:00.347] 测试例程 0x0010 - 请求结果...
[20:49:00.347] 发送: can0  715   [8]  04 31 03 00 10 55 55 55
[20:49:00.348] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.348] ❌ 例程 0x0010: 负响应 NRC=0x12
[20:49:00.448] 测试例程 0x0011 - 启动例程...
[20:49:00.448] 发送: can0  715   [8]  04 31 01 00 11 55 55 55
[20:49:00.449] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:00.449] ❌ 例程 0x0011: 负响应 NRC=0x31
[20:49:00.549] 测试例程 0x0011 - 停止例程...
[20:49:00.549] 发送: can0  715   [8]  04 31 02 00 11 55 55 55
[20:49:00.550] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.550] ❌ 例程 0x0011: 负响应 NRC=0x12
[20:49:00.650] 测试例程 0x0011 - 请求结果...
[20:49:00.650] 发送: can0  715   [8]  04 31 03 00 11 55 55 55
[20:49:00.652] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.652] ❌ 例程 0x0011: 负响应 NRC=0x12
[20:49:00.752] 测试例程 0x0012 - 启动例程...
[20:49:00.752] 发送: can0  715   [8]  04 31 01 00 12 55 55 55
[20:49:00.754] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:00.754] ❌ 例程 0x0012: 负响应 NRC=0x31
[20:49:00.854] 测试例程 0x0012 - 停止例程...
[20:49:00.854] 发送: can0  715   [8]  04 31 02 00 12 55 55 55
[20:49:00.855] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.855] ❌ 例程 0x0012: 负响应 NRC=0x12
[20:49:00.955] 测试例程 0x0012 - 请求结果...
[20:49:00.955] 发送: can0  715   [8]  04 31 03 00 12 55 55 55
[20:49:00.956] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:00.956] ❌ 例程 0x0012: 负响应 NRC=0x12
[20:49:01.056] 测试例程 0x0013 - 启动例程...
[20:49:01.056] 发送: can0  715   [8]  04 31 01 00 13 55 55 55
[20:49:01.057] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:01.057] ❌ 例程 0x0013: 负响应 NRC=0x31
[20:49:01.157] 测试例程 0x0013 - 停止例程...
[20:49:01.157] 发送: can0  715   [8]  04 31 02 00 13 55 55 55
[20:49:01.158] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.158] ❌ 例程 0x0013: 负响应 NRC=0x12
[20:49:01.258] 测试例程 0x0013 - 请求结果...
[20:49:01.258] 发送: can0  715   [8]  04 31 03 00 13 55 55 55
[20:49:01.259] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.259] ❌ 例程 0x0013: 负响应 NRC=0x12
[20:49:01.359] 测试例程 0x0014 - 启动例程...
[20:49:01.360] 发送: can0  715   [8]  04 31 01 00 14 55 55 55
[20:49:01.361] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:01.361] ❌ 例程 0x0014: 负响应 NRC=0x31
[20:49:01.461] 测试例程 0x0014 - 停止例程...
[20:49:01.462] 发送: can0  715   [8]  04 31 02 00 14 55 55 55
[20:49:01.463] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.463] ❌ 例程 0x0014: 负响应 NRC=0x12
[20:49:01.563] 测试例程 0x0014 - 请求结果...
[20:49:01.564] 发送: can0  715   [8]  04 31 03 00 14 55 55 55
[20:49:01.564] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.564] ❌ 例程 0x0014: 负响应 NRC=0x12
[20:49:01.664] 测试例程 0x0020 - 启动例程...
[20:49:01.665] 发送: can0  715   [8]  04 31 01 00 20 55 55 55
[20:49:01.665] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:01.666] ❌ 例程 0x0020: 负响应 NRC=0x31
[20:49:01.766] 测试例程 0x0020 - 停止例程...
[20:49:01.766] 发送: can0  715   [8]  04 31 02 00 20 55 55 55
[20:49:01.767] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.768] ❌ 例程 0x0020: 负响应 NRC=0x12
[20:49:01.868] 测试例程 0x0020 - 请求结果...
[20:49:01.868] 发送: can0  715   [8]  04 31 03 00 20 55 55 55
[20:49:01.869] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:01.870] ❌ 例程 0x0020: 负响应 NRC=0x12
[20:49:01.970] 测试例程 0x0021 - 启动例程...
[20:49:01.970] 发送: can0  715   [8]  04 31 01 00 21 55 55 55
[20:49:01.972] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:01.972] ❌ 例程 0x0021: 负响应 NRC=0x31
[20:49:02.072] 测试例程 0x0021 - 停止例程...
[20:49:02.072] 发送: can0  715   [8]  04 31 02 00 21 55 55 55
[20:49:02.074] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.074] ❌ 例程 0x0021: 负响应 NRC=0x12
[20:49:02.174] 测试例程 0x0021 - 请求结果...
[20:49:02.174] 发送: can0  715   [8]  04 31 03 00 21 55 55 55
[20:49:02.175] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.175] ❌ 例程 0x0021: 负响应 NRC=0x12
[20:49:02.275] 测试例程 0x0022 - 启动例程...
[20:49:02.275] 发送: can0  715   [8]  04 31 01 00 22 55 55 55
[20:49:02.276] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:02.276] ❌ 例程 0x0022: 负响应 NRC=0x31
[20:49:02.376] 测试例程 0x0022 - 停止例程...
[20:49:02.376] 发送: can0  715   [8]  04 31 02 00 22 55 55 55
[20:49:02.377] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.377] ❌ 例程 0x0022: 负响应 NRC=0x12
[20:49:02.477] 测试例程 0x0022 - 请求结果...
[20:49:02.477] 发送: can0  715   [8]  04 31 03 00 22 55 55 55
[20:49:02.479] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.479] ❌ 例程 0x0022: 负响应 NRC=0x12
[20:49:02.579] 测试例程 0x0023 - 启动例程...
[20:49:02.579] 发送: can0  715   [8]  04 31 01 00 23 55 55 55
[20:49:02.580] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:02.580] ❌ 例程 0x0023: 负响应 NRC=0x31
[20:49:02.680] 测试例程 0x0023 - 停止例程...
[20:49:02.680] 发送: can0  715   [8]  04 31 02 00 23 55 55 55
[20:49:02.681] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.681] ❌ 例程 0x0023: 负响应 NRC=0x12
[20:49:02.781] 测试例程 0x0023 - 请求结果...
[20:49:02.781] 发送: can0  715   [8]  04 31 03 00 23 55 55 55
[20:49:02.782] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.782] ❌ 例程 0x0023: 负响应 NRC=0x12
[20:49:02.882] 测试例程 0x0024 - 启动例程...
[20:49:02.882] 发送: can0  715   [8]  04 31 01 00 24 55 55 55
[20:49:02.883] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:02.883] ❌ 例程 0x0024: 负响应 NRC=0x31
[20:49:02.983] 测试例程 0x0024 - 停止例程...
[20:49:02.983] 发送: can0  715   [8]  04 31 02 00 24 55 55 55
[20:49:02.985] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:02.985] ❌ 例程 0x0024: 负响应 NRC=0x12
[20:49:03.085] 测试例程 0x0024 - 请求结果...
[20:49:03.085] 发送: can0  715   [8]  04 31 03 00 24 55 55 55
[20:49:03.086] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.086] ❌ 例程 0x0024: 负响应 NRC=0x12
[20:49:03.186] 测试例程 0x0100 - 启动例程...
[20:49:03.186] 发送: can0  715   [8]  04 31 01 01 00 55 55 55
[20:49:03.187] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:03.187] ❌ 例程 0x0100: 负响应 NRC=0x31
[20:49:03.287] 测试例程 0x0100 - 停止例程...
[20:49:03.287] 发送: can0  715   [8]  04 31 02 01 00 55 55 55
[20:49:03.288] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.288] ❌ 例程 0x0100: 负响应 NRC=0x12
[20:49:03.388] 测试例程 0x0100 - 请求结果...
[20:49:03.388] 发送: can0  715   [8]  04 31 03 01 00 55 55 55
[20:49:03.390] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.390] ❌ 例程 0x0100: 负响应 NRC=0x12
[20:49:03.490] 测试例程 0x0101 - 启动例程...
[20:49:03.491] 发送: can0  715   [8]  04 31 01 01 01 55 55 55
[20:49:03.492] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:03.492] ❌ 例程 0x0101: 负响应 NRC=0x31
[20:49:03.592] 测试例程 0x0101 - 停止例程...
[20:49:03.593] 发送: can0  715   [8]  04 31 02 01 01 55 55 55
[20:49:03.594] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.594] ❌ 例程 0x0101: 负响应 NRC=0x12
[20:49:03.694] 测试例程 0x0101 - 请求结果...
[20:49:03.695] 发送: can0  715   [8]  04 31 03 01 01 55 55 55
[20:49:03.695] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.695] ❌ 例程 0x0101: 负响应 NRC=0x12
[20:49:03.795] 测试例程 0x0102 - 启动例程...
[20:49:03.796] 发送: can0  715   [8]  04 31 01 01 02 55 55 55
[20:49:03.796] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:03.796] ❌ 例程 0x0102: 负响应 NRC=0x31
[20:49:03.896] 测试例程 0x0102 - 停止例程...
[20:49:03.897] 发送: can0  715   [8]  04 31 02 01 02 55 55 55
[20:49:03.897] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.897] ❌ 例程 0x0102: 负响应 NRC=0x12
[20:49:03.997] 测试例程 0x0102 - 请求结果...
[20:49:03.998] 发送: can0  715   [8]  04 31 03 01 02 55 55 55
[20:49:03.998] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:03.998] ❌ 例程 0x0102: 负响应 NRC=0x12
[20:49:04.098] 测试例程 0x0103 - 启动例程...
[20:49:04.099] 发送: can0  715   [8]  04 31 01 01 03 55 55 55
[20:49:04.099] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:04.099] ❌ 例程 0x0103: 负响应 NRC=0x31
[20:49:04.200] 测试例程 0x0103 - 停止例程...
[20:49:04.200] 发送: can0  715   [8]  04 31 02 01 03 55 55 55
[20:49:04.200] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.200] ❌ 例程 0x0103: 负响应 NRC=0x12
[20:49:04.301] 测试例程 0x0103 - 请求结果...
[20:49:04.301] 发送: can0  715   [8]  04 31 03 01 03 55 55 55
[20:49:04.301] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.302] ❌ 例程 0x0103: 负响应 NRC=0x12
[20:49:04.402] 测试例程 0x0104 - 启动例程...
[20:49:04.402] 发送: can0  715   [8]  04 31 01 01 04 55 55 55
[20:49:04.403] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:04.404] ❌ 例程 0x0104: 负响应 NRC=0x31
[20:49:04.504] 测试例程 0x0104 - 停止例程...
[20:49:04.504] 发送: can0  715   [8]  04 31 02 01 04 55 55 55
[20:49:04.507] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.507] ❌ 例程 0x0104: 负响应 NRC=0x12
[20:49:04.607] 测试例程 0x0104 - 请求结果...
[20:49:04.608] 发送: can0  715   [8]  04 31 03 01 04 55 55 55
[20:49:04.609] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.609] ❌ 例程 0x0104: 负响应 NRC=0x12
[20:49:04.709] 测试例程 0x0200 - 启动例程...
[20:49:04.709] 发送: can0  715   [8]  04 31 01 02 00 55 55 55
[20:49:04.711] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:04.711] ❌ 例程 0x0200: 负响应 NRC=0x31
[20:49:04.811] 测试例程 0x0200 - 停止例程...
[20:49:04.811] 发送: can0  715   [8]  04 31 02 02 00 55 55 55
[20:49:04.812] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.812] ❌ 例程 0x0200: 负响应 NRC=0x12
[20:49:04.912] 测试例程 0x0200 - 请求结果...
[20:49:04.912] 发送: can0  715   [8]  04 31 03 02 00 55 55 55
[20:49:04.913] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:04.913] ❌ 例程 0x0200: 负响应 NRC=0x12
[20:49:05.013] 测试例程 0x0201 - 启动例程...
[20:49:05.013] 发送: can0  715   [8]  04 31 01 02 01 55 55 55
[20:49:05.014] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:05.014] ❌ 例程 0x0201: 负响应 NRC=0x31
[20:49:05.114] 测试例程 0x0201 - 停止例程...
[20:49:05.114] 发送: can0  715   [8]  04 31 02 02 01 55 55 55
[20:49:05.115] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.115] ❌ 例程 0x0201: 负响应 NRC=0x12
[20:49:05.215] 测试例程 0x0201 - 请求结果...
[20:49:05.215] 发送: can0  715   [8]  04 31 03 02 01 55 55 55
[20:49:05.217] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.217] ❌ 例程 0x0201: 负响应 NRC=0x12
[20:49:05.317] 测试例程 0x0202 - 启动例程...
[20:49:05.317] 发送: can0  715   [8]  04 31 01 02 02 55 55 55
[20:49:05.318] 接收: can0  795   [8]  03 7F 31 13 55 55 55 55
[20:49:05.318] ❌ 例程 0x0202: 负响应 NRC=0x13
[20:49:05.418] 测试例程 0x0202 - 停止例程...
[20:49:05.418] 发送: can0  715   [8]  04 31 02 02 02 55 55 55
[20:49:05.420] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.420] ❌ 例程 0x0202: 负响应 NRC=0x12
[20:49:05.520] 测试例程 0x0202 - 请求结果...
[20:49:05.520] 发送: can0  715   [8]  04 31 03 02 02 55 55 55
[20:49:05.521] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.521] ❌ 例程 0x0202: 负响应 NRC=0x12
[20:49:05.621] 测试例程 0x0203 - 启动例程...
[20:49:05.621] 发送: can0  715   [8]  04 31 01 02 03 55 55 55
[20:49:05.622] 接收: can0  795   [8]  05 71 01 02 03 00 55 55
[20:49:05.622] ✅ 例程 0x0203 (启动例程): 01 02 03 00 55 55
[20:49:05.722] 测试例程 0x0203 - 停止例程...
[20:49:05.722] 发送: can0  715   [8]  04 31 02 02 03 55 55 55
[20:49:05.724] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.724] ❌ 例程 0x0203: 负响应 NRC=0x12
[20:49:05.824] 测试例程 0x0203 - 请求结果...
[20:49:05.824] 发送: can0  715   [8]  04 31 03 02 03 55 55 55
[20:49:05.826] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:05.826] ❌ 例程 0x0203: 负响应 NRC=0x12
[20:49:05.926] 测试例程 0x0204 - 启动例程...
[20:49:05.926] 发送: can0  715   [8]  04 31 01 02 04 55 55 55
[20:49:05.927] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:05.927] ❌ 例程 0x0204: 负响应 NRC=0x31
[20:49:06.027] 测试例程 0x0204 - 停止例程...
[20:49:06.027] 发送: can0  715   [8]  04 31 02 02 04 55 55 55
[20:49:06.028] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.028] ❌ 例程 0x0204: 负响应 NRC=0x12
[20:49:06.128] 测试例程 0x0204 - 请求结果...
[20:49:06.128] 发送: can0  715   [8]  04 31 03 02 04 55 55 55
[20:49:06.129] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.129] ❌ 例程 0x0204: 负响应 NRC=0x12
[20:49:06.229] 测试例程 0x1000 - 启动例程...
[20:49:06.230] 发送: can0  715   [8]  04 31 01 10 00 55 55 55
[20:49:06.230] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:06.230] ❌ 例程 0x1000: 负响应 NRC=0x31
[20:49:06.330] 测试例程 0x1000 - 停止例程...
[20:49:06.331] 发送: can0  715   [8]  04 31 02 10 00 55 55 55
[20:49:06.331] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.331] ❌ 例程 0x1000: 负响应 NRC=0x12
[20:49:06.431] 测试例程 0x1000 - 请求结果...
[20:49:06.432] 发送: can0  715   [8]  04 31 03 10 00 55 55 55
[20:49:06.432] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.432] ❌ 例程 0x1000: 负响应 NRC=0x12
[20:49:06.533] 测试例程 0x1001 - 启动例程...
[20:49:06.533] 发送: can0  715   [8]  04 31 01 10 01 55 55 55
[20:49:06.533] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:06.533] ❌ 例程 0x1001: 负响应 NRC=0x31
[20:49:06.634] 测试例程 0x1001 - 停止例程...
[20:49:06.634] 发送: can0  715   [8]  04 31 02 10 01 55 55 55
[20:49:06.634] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.635] ❌ 例程 0x1001: 负响应 NRC=0x12
[20:49:06.735] 测试例程 0x1001 - 请求结果...
[20:49:06.735] 发送: can0  715   [8]  04 31 03 10 01 55 55 55
[20:49:06.736] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.737] ❌ 例程 0x1001: 负响应 NRC=0x12
[20:49:06.837] 测试例程 0x1002 - 启动例程...
[20:49:06.837] 发送: can0  715   [8]  04 31 01 10 02 55 55 55
[20:49:06.838] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:06.839] ❌ 例程 0x1002: 负响应 NRC=0x31
[20:49:06.939] 测试例程 0x1002 - 停止例程...
[20:49:06.939] 发送: can0  715   [8]  04 31 02 10 02 55 55 55
[20:49:06.939] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:06.940] ❌ 例程 0x1002: 负响应 NRC=0x12
[20:49:07.040] 测试例程 0x1002 - 请求结果...
[20:49:07.040] 发送: can0  715   [8]  04 31 03 10 02 55 55 55
[20:49:07.041] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.041] ❌ 例程 0x1002: 负响应 NRC=0x12
[20:49:07.141] 测试例程 0x1003 - 启动例程...
[20:49:07.141] 发送: can0  715   [8]  04 31 01 10 03 55 55 55
[20:49:07.142] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:07.142] ❌ 例程 0x1003: 负响应 NRC=0x31
[20:49:07.242] 测试例程 0x1003 - 停止例程...
[20:49:07.242] 发送: can0  715   [8]  04 31 02 10 03 55 55 55
[20:49:07.244] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.244] ❌ 例程 0x1003: 负响应 NRC=0x12
[20:49:07.344] 测试例程 0x1003 - 请求结果...
[20:49:07.344] 发送: can0  715   [8]  04 31 03 10 03 55 55 55
[20:49:07.346] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.346] ❌ 例程 0x1003: 负响应 NRC=0x12
[20:49:07.446] 测试例程 0x1004 - 启动例程...
[20:49:07.446] 发送: can0  715   [8]  04 31 01 10 04 55 55 55
[20:49:07.447] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:07.447] ❌ 例程 0x1004: 负响应 NRC=0x31
[20:49:07.547] 测试例程 0x1004 - 停止例程...
[20:49:07.547] 发送: can0  715   [8]  04 31 02 10 04 55 55 55
[20:49:07.548] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.548] ❌ 例程 0x1004: 负响应 NRC=0x12
[20:49:07.648] 测试例程 0x1004 - 请求结果...
[20:49:07.648] 发送: can0  715   [8]  04 31 03 10 04 55 55 55
[20:49:07.649] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.649] ❌ 例程 0x1004: 负响应 NRC=0x12
[20:49:07.749] 测试例程 0x2000 - 启动例程...
[20:49:07.749] 发送: can0  715   [8]  04 31 01 20 00 55 55 55
[20:49:07.750] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:07.750] ❌ 例程 0x2000: 负响应 NRC=0x31
[20:49:07.850] 测试例程 0x2000 - 停止例程...
[20:49:07.850] 发送: can0  715   [8]  04 31 02 20 00 55 55 55
[20:49:07.851] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.851] ❌ 例程 0x2000: 负响应 NRC=0x12
[20:49:07.951] 测试例程 0x2000 - 请求结果...
[20:49:07.951] 发送: can0  715   [8]  04 31 03 20 00 55 55 55
[20:49:07.953] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:07.953] ❌ 例程 0x2000: 负响应 NRC=0x12
[20:49:08.053] 测试例程 0x2001 - 启动例程...
[20:49:08.053] 发送: can0  715   [8]  04 31 01 20 01 55 55 55
[20:49:08.055] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:08.055] ❌ 例程 0x2001: 负响应 NRC=0x31
[20:49:08.155] 测试例程 0x2001 - 停止例程...
[20:49:08.156] 发送: can0  715   [8]  04 31 02 20 01 55 55 55
[20:49:08.157] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.157] ❌ 例程 0x2001: 负响应 NRC=0x12
[20:49:08.257] 测试例程 0x2001 - 请求结果...
[20:49:08.257] 发送: can0  715   [8]  04 31 03 20 01 55 55 55
[20:49:08.258] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.258] ❌ 例程 0x2001: 负响应 NRC=0x12
[20:49:08.358] 测试例程 0x2002 - 启动例程...
[20:49:08.358] 发送: can0  715   [8]  04 31 01 20 02 55 55 55
[20:49:08.359] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:08.359] ❌ 例程 0x2002: 负响应 NRC=0x31
[20:49:08.459] 测试例程 0x2002 - 停止例程...
[20:49:08.460] 发送: can0  715   [8]  04 31 02 20 02 55 55 55
[20:49:08.461] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.461] ❌ 例程 0x2002: 负响应 NRC=0x12
[20:49:08.561] 测试例程 0x2002 - 请求结果...
[20:49:08.562] 发送: can0  715   [8]  04 31 03 20 02 55 55 55
[20:49:08.563] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.563] ❌ 例程 0x2002: 负响应 NRC=0x12
[20:49:08.663] 测试例程 0x2003 - 启动例程...
[20:49:08.664] 发送: can0  715   [8]  04 31 01 20 03 55 55 55
[20:49:08.664] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:08.664] ❌ 例程 0x2003: 负响应 NRC=0x31
[20:49:08.764] 测试例程 0x2003 - 停止例程...
[20:49:08.765] 发送: can0  715   [8]  04 31 02 20 03 55 55 55
[20:49:08.765] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.765] ❌ 例程 0x2003: 负响应 NRC=0x12
[20:49:08.866] 测试例程 0x2003 - 请求结果...
[20:49:08.866] 发送: can0  715   [8]  04 31 03 20 03 55 55 55
[20:49:08.866] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:08.866] ❌ 例程 0x2003: 负响应 NRC=0x12
[20:49:08.967] 测试例程 0x2004 - 启动例程...
[20:49:08.967] 发送: can0  715   [8]  04 31 01 20 04 55 55 55
[20:49:08.968] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:08.968] ❌ 例程 0x2004: 负响应 NRC=0x31
[20:49:09.069] 测试例程 0x2004 - 停止例程...
[20:49:09.069] 发送: can0  715   [8]  04 31 02 20 04 55 55 55
[20:49:09.070] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.070] ❌ 例程 0x2004: 负响应 NRC=0x12
[20:49:09.171] 测试例程 0x2004 - 请求结果...
[20:49:09.171] 发送: can0  715   [8]  04 31 03 20 04 55 55 55
[20:49:09.172] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.173] ❌ 例程 0x2004: 负响应 NRC=0x12
[20:49:09.273] 测试例程 0x3000 - 启动例程...
[20:49:09.273] 发送: can0  715   [8]  04 31 01 30 00 55 55 55
[20:49:09.274] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:09.274] ❌ 例程 0x3000: 负响应 NRC=0x31
[20:49:09.374] 测试例程 0x3000 - 停止例程...
[20:49:09.374] 发送: can0  715   [8]  04 31 02 30 00 55 55 55
[20:49:09.376] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.376] ❌ 例程 0x3000: 负响应 NRC=0x12
[20:49:09.476] 测试例程 0x3000 - 请求结果...
[20:49:09.476] 发送: can0  715   [8]  04 31 03 30 00 55 55 55
[20:49:09.477] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.477] ❌ 例程 0x3000: 负响应 NRC=0x12
[20:49:09.577] 测试例程 0x3001 - 启动例程...
[20:49:09.577] 发送: can0  715   [8]  04 31 01 30 01 55 55 55
[20:49:09.606] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:09.606] ❌ 例程 0x3001: 负响应 NRC=0x31
[20:49:09.706] 测试例程 0x3001 - 停止例程...
[20:49:09.706] 发送: can0  715   [8]  04 31 02 30 01 55 55 55
[20:49:09.708] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.708] ❌ 例程 0x3001: 负响应 NRC=0x12
[20:49:09.808] 测试例程 0x3001 - 请求结果...
[20:49:09.808] 发送: can0  715   [8]  04 31 03 30 01 55 55 55
[20:49:09.809] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:09.809] ❌ 例程 0x3001: 负响应 NRC=0x12
[20:49:09.909] 测试例程 0x3002 - 启动例程...
[20:49:09.909] 发送: can0  715   [8]  04 31 01 30 02 55 55 55
[20:49:09.911] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:09.911] ❌ 例程 0x3002: 负响应 NRC=0x31
[20:49:10.011] 测试例程 0x3002 - 停止例程...
[20:49:10.011] 发送: can0  715   [8]  04 31 02 30 02 55 55 55
[20:49:10.013] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.013] ❌ 例程 0x3002: 负响应 NRC=0x12
[20:49:10.113] 测试例程 0x3002 - 请求结果...
[20:49:10.113] 发送: can0  715   [8]  04 31 03 30 02 55 55 55
[20:49:10.115] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.115] ❌ 例程 0x3002: 负响应 NRC=0x12
[20:49:10.215] 测试例程 0x3003 - 启动例程...
[20:49:10.215] 发送: can0  715   [8]  04 31 01 30 03 55 55 55
[20:49:10.217] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:10.217] ❌ 例程 0x3003: 负响应 NRC=0x31
[20:49:10.317] 测试例程 0x3003 - 停止例程...
[20:49:10.317] 发送: can0  715   [8]  04 31 02 30 03 55 55 55
[20:49:10.319] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.319] ❌ 例程 0x3003: 负响应 NRC=0x12
[20:49:10.419] 测试例程 0x3003 - 请求结果...
[20:49:10.419] 发送: can0  715   [8]  04 31 03 30 03 55 55 55
[20:49:10.420] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.420] ❌ 例程 0x3003: 负响应 NRC=0x12
[20:49:10.520] 测试例程 0x3004 - 启动例程...
[20:49:10.520] 发送: can0  715   [8]  04 31 01 30 04 55 55 55
[20:49:10.521] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:10.521] ❌ 例程 0x3004: 负响应 NRC=0x31
[20:49:10.621] 测试例程 0x3004 - 停止例程...
[20:49:10.621] 发送: can0  715   [8]  04 31 02 30 04 55 55 55
[20:49:10.622] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.622] ❌ 例程 0x3004: 负响应 NRC=0x12
[20:49:10.722] 测试例程 0x3004 - 请求结果...
[20:49:10.722] 发送: can0  715   [8]  04 31 03 30 04 55 55 55
[20:49:10.723] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.723] ❌ 例程 0x3004: 负响应 NRC=0x12
[20:49:10.823] 测试例程 0xF010 - 启动例程...
[20:49:10.824] 发送: can0  715   [8]  04 31 01 F0 10 55 55 55
[20:49:10.825] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:10.825] ❌ 例程 0xF010: 负响应 NRC=0x31
[20:49:10.926] 测试例程 0xF010 - 停止例程...
[20:49:10.926] 发送: can0  715   [8]  04 31 02 F0 10 55 55 55
[20:49:10.927] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:10.927] ❌ 例程 0xF010: 负响应 NRC=0x12
[20:49:11.027] 测试例程 0xF010 - 请求结果...
[20:49:11.028] 发送: can0  715   [8]  04 31 03 F0 10 55 55 55
[20:49:11.028] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.028] ❌ 例程 0xF010: 负响应 NRC=0x12
[20:49:11.128] 测试例程 0xF011 - 启动例程...
[20:49:11.129] 发送: can0  715   [8]  04 31 01 F0 11 55 55 55
[20:49:11.129] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:11.129] ❌ 例程 0xF011: 负响应 NRC=0x31
[20:49:11.229] 测试例程 0xF011 - 停止例程...
[20:49:11.230] 发送: can0  715   [8]  04 31 02 F0 11 55 55 55
[20:49:11.230] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.230] ❌ 例程 0xF011: 负响应 NRC=0x12
[20:49:11.331] 测试例程 0xF011 - 请求结果...
[20:49:11.331] 发送: can0  715   [8]  04 31 03 F0 11 55 55 55
[20:49:11.331] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.331] ❌ 例程 0xF011: 负响应 NRC=0x12
[20:49:11.432] 测试例程 0xF012 - 启动例程...
[20:49:11.432] 发送: can0  715   [8]  04 31 01 F0 12 55 55 55
[20:49:11.432] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:11.432] ❌ 例程 0xF012: 负响应 NRC=0x31
[20:49:11.533] 测试例程 0xF012 - 停止例程...
[20:49:11.533] 发送: can0  715   [8]  04 31 02 F0 12 55 55 55
[20:49:11.533] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.533] ❌ 例程 0xF012: 负响应 NRC=0x12
[20:49:11.634] 测试例程 0xF012 - 请求结果...
[20:49:11.634] 发送: can0  715   [8]  04 31 03 F0 12 55 55 55
[20:49:11.635] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.635] ❌ 例程 0xF012: 负响应 NRC=0x12
[20:49:11.735] 测试例程 0xF013 - 启动例程...
[20:49:11.735] 发送: can0  715   [8]  04 31 01 F0 13 55 55 55
[20:49:11.736] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:11.736] ❌ 例程 0xF013: 负响应 NRC=0x31
[20:49:11.836] 测试例程 0xF013 - 停止例程...
[20:49:11.836] 发送: can0  715   [8]  04 31 02 F0 13 55 55 55
[20:49:11.837] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.837] ❌ 例程 0xF013: 负响应 NRC=0x12
[20:49:11.937] 测试例程 0xF013 - 请求结果...
[20:49:11.937] 发送: can0  715   [8]  04 31 03 F0 13 55 55 55
[20:49:11.938] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:11.938] ❌ 例程 0xF013: 负响应 NRC=0x12
[20:49:12.038] 测试例程 0xF014 - 启动例程...
[20:49:12.038] 发送: can0  715   [8]  04 31 01 F0 14 55 55 55
[20:49:12.039] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:12.039] ❌ 例程 0xF014: 负响应 NRC=0x31
[20:49:12.139] 测试例程 0xF014 - 停止例程...
[20:49:12.139] 发送: can0  715   [8]  04 31 02 F0 14 55 55 55
[20:49:12.140] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.140] ❌ 例程 0xF014: 负响应 NRC=0x12
[20:49:12.240] 测试例程 0xF014 - 请求结果...
[20:49:12.240] 发送: can0  715   [8]  04 31 03 F0 14 55 55 55
[20:49:12.241] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.241] ❌ 例程 0xF014: 负响应 NRC=0x12
[20:49:12.341] 测试例程 0xF020 - 启动例程...
[20:49:12.341] 发送: can0  715   [8]  04 31 01 F0 20 55 55 55
[20:49:12.342] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:12.342] ❌ 例程 0xF020: 负响应 NRC=0x31
[20:49:12.442] 测试例程 0xF020 - 停止例程...
[20:49:12.442] 发送: can0  715   [8]  04 31 02 F0 20 55 55 55
[20:49:12.443] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.443] ❌ 例程 0xF020: 负响应 NRC=0x12
[20:49:12.543] 测试例程 0xF020 - 请求结果...
[20:49:12.543] 发送: can0  715   [8]  04 31 03 F0 20 55 55 55
[20:49:12.544] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.544] ❌ 例程 0xF020: 负响应 NRC=0x12
[20:49:12.644] 测试例程 0xF021 - 启动例程...
[20:49:12.644] 发送: can0  715   [8]  04 31 01 F0 21 55 55 55
[20:49:12.645] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:12.645] ❌ 例程 0xF021: 负响应 NRC=0x31
[20:49:12.745] 测试例程 0xF021 - 停止例程...
[20:49:12.745] 发送: can0  715   [8]  04 31 02 F0 21 55 55 55
[20:49:12.746] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.746] ❌ 例程 0xF021: 负响应 NRC=0x12
[20:49:12.846] 测试例程 0xF021 - 请求结果...
[20:49:12.846] 发送: can0  715   [8]  04 31 03 F0 21 55 55 55
[20:49:12.847] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:12.847] ❌ 例程 0xF021: 负响应 NRC=0x12
[20:49:12.947] 测试例程 0xF022 - 启动例程...
[20:49:12.947] 发送: can0  715   [8]  04 31 01 F0 22 55 55 55
[20:49:12.948] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:12.948] ❌ 例程 0xF022: 负响应 NRC=0x31
[20:49:13.048] 测试例程 0xF022 - 停止例程...
[20:49:13.048] 发送: can0  715   [8]  04 31 02 F0 22 55 55 55
[20:49:13.049] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.049] ❌ 例程 0xF022: 负响应 NRC=0x12
[20:49:13.149] 测试例程 0xF022 - 请求结果...
[20:49:13.149] 发送: can0  715   [8]  04 31 03 F0 22 55 55 55
[20:49:13.150] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.150] ❌ 例程 0xF022: 负响应 NRC=0x12
[20:49:13.250] 测试例程 0xF023 - 启动例程...
[20:49:13.250] 发送: can0  715   [8]  04 31 01 F0 23 55 55 55
[20:49:13.251] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:13.251] ❌ 例程 0xF023: 负响应 NRC=0x31
[20:49:13.351] 测试例程 0xF023 - 停止例程...
[20:49:13.351] 发送: can0  715   [8]  04 31 02 F0 23 55 55 55
[20:49:13.352] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.352] ❌ 例程 0xF023: 负响应 NRC=0x12
[20:49:13.453] 测试例程 0xF023 - 请求结果...
[20:49:13.453] 发送: can0  715   [8]  04 31 03 F0 23 55 55 55
[20:49:13.454] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.454] ❌ 例程 0xF023: 负响应 NRC=0x12
[20:49:13.554] 测试例程 0xF024 - 启动例程...
[20:49:13.555] 发送: can0  715   [8]  04 31 01 F0 24 55 55 55
[20:49:13.555] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:13.555] ❌ 例程 0xF024: 负响应 NRC=0x31
[20:49:13.656] 测试例程 0xF024 - 停止例程...
[20:49:13.656] 发送: can0  715   [8]  04 31 02 F0 24 55 55 55
[20:49:13.656] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.656] ❌ 例程 0xF024: 负响应 NRC=0x12
[20:49:13.756] 测试例程 0xF024 - 请求结果...
[20:49:13.757] 发送: can0  715   [8]  04 31 03 F0 24 55 55 55
[20:49:13.757] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.757] ❌ 例程 0xF024: 负响应 NRC=0x12
[20:49:13.858] 测试例程 0xF100 - 启动例程...
[20:49:13.858] 发送: can0  715   [8]  04 31 01 F1 00 55 55 55
[20:49:13.858] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:13.858] ❌ 例程 0xF100: 负响应 NRC=0x31
[20:49:13.959] 测试例程 0xF100 - 停止例程...
[20:49:13.959] 发送: can0  715   [8]  04 31 02 F1 00 55 55 55
[20:49:13.960] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:13.960] ❌ 例程 0xF100: 负响应 NRC=0x12
[20:49:14.061] 测试例程 0xF100 - 请求结果...
[20:49:14.061] 发送: can0  715   [8]  04 31 03 F1 00 55 55 55
[20:49:14.061] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.062] ❌ 例程 0xF100: 负响应 NRC=0x12
[20:49:14.162] 测试例程 0xF101 - 启动例程...
[20:49:14.162] 发送: can0  715   [8]  04 31 01 F1 01 55 55 55
[20:49:14.164] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:14.164] ❌ 例程 0xF101: 负响应 NRC=0x31
[20:49:14.264] 测试例程 0xF101 - 停止例程...
[20:49:14.264] 发送: can0  715   [8]  04 31 02 F1 01 55 55 55
[20:49:14.265] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.265] ❌ 例程 0xF101: 负响应 NRC=0x12
[20:49:14.365] 测试例程 0xF101 - 请求结果...
[20:49:14.365] 发送: can0  715   [8]  04 31 03 F1 01 55 55 55
[20:49:14.367] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.367] ❌ 例程 0xF101: 负响应 NRC=0x12
[20:49:14.467] 测试例程 0xF102 - 启动例程...
[20:49:14.467] 发送: can0  715   [8]  04 31 01 F1 02 55 55 55
[20:49:14.469] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:14.469] ❌ 例程 0xF102: 负响应 NRC=0x31
[20:49:14.569] 测试例程 0xF102 - 停止例程...
[20:49:14.569] 发送: can0  715   [8]  04 31 02 F1 02 55 55 55
[20:49:14.571] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.571] ❌ 例程 0xF102: 负响应 NRC=0x12
[20:49:14.671] 测试例程 0xF102 - 请求结果...
[20:49:14.671] 发送: can0  715   [8]  04 31 03 F1 02 55 55 55
[20:49:14.702] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.702] ❌ 例程 0xF102: 负响应 NRC=0x12
[20:49:14.802] 测试例程 0xF103 - 启动例程...
[20:49:14.802] 发送: can0  715   [8]  04 31 01 F1 03 55 55 55
[20:49:14.803] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:14.803] ❌ 例程 0xF103: 负响应 NRC=0x31
[20:49:14.903] 测试例程 0xF103 - 停止例程...
[20:49:14.903] 发送: can0  715   [8]  04 31 02 F1 03 55 55 55
[20:49:14.904] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:14.904] ❌ 例程 0xF103: 负响应 NRC=0x12
[20:49:15.004] 测试例程 0xF103 - 请求结果...
[20:49:15.004] 发送: can0  715   [8]  04 31 03 F1 03 55 55 55
[20:49:15.005] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.005] ❌ 例程 0xF103: 负响应 NRC=0x12
[20:49:15.105] 测试例程 0xF104 - 启动例程...
[20:49:15.105] 发送: can0  715   [8]  04 31 01 F1 04 55 55 55
[20:49:15.106] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:15.106] ❌ 例程 0xF104: 负响应 NRC=0x31
[20:49:15.206] 测试例程 0xF104 - 停止例程...
[20:49:15.206] 发送: can0  715   [8]  04 31 02 F1 04 55 55 55
[20:49:15.207] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.207] ❌ 例程 0xF104: 负响应 NRC=0x12
[20:49:15.307] 测试例程 0xF104 - 请求结果...
[20:49:15.307] 发送: can0  715   [8]  04 31 03 F1 04 55 55 55
[20:49:15.308] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.308] ❌ 例程 0xF104: 负响应 NRC=0x12
[20:49:15.408] 测试例程 0xF200 - 启动例程...
[20:49:15.408] 发送: can0  715   [8]  04 31 01 F2 00 55 55 55
[20:49:15.409] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:15.409] ❌ 例程 0xF200: 负响应 NRC=0x31
[20:49:15.509] 测试例程 0xF200 - 停止例程...
[20:49:15.510] 发送: can0  715   [8]  04 31 02 F2 00 55 55 55
[20:49:15.511] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.511] ❌ 例程 0xF200: 负响应 NRC=0x12
[20:49:15.611] 测试例程 0xF200 - 请求结果...
[20:49:15.612] 发送: can0  715   [8]  04 31 03 F2 00 55 55 55
[20:49:15.613] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.613] ❌ 例程 0xF200: 负响应 NRC=0x12
[20:49:15.713] 测试例程 0xF201 - 启动例程...
[20:49:15.714] 发送: can0  715   [8]  04 31 01 F2 01 55 55 55
[20:49:15.715] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:15.715] ❌ 例程 0xF201: 负响应 NRC=0x31
[20:49:15.815] 测试例程 0xF201 - 停止例程...
[20:49:15.816] 发送: can0  715   [8]  04 31 02 F2 01 55 55 55
[20:49:15.817] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.817] ❌ 例程 0xF201: 负响应 NRC=0x12
[20:49:15.917] 测试例程 0xF201 - 请求结果...
[20:49:15.918] 发送: can0  715   [8]  04 31 03 F2 01 55 55 55
[20:49:15.918] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:15.918] ❌ 例程 0xF201: 负响应 NRC=0x12
[20:49:16.018] 测试例程 0xF202 - 启动例程...
[20:49:16.018] 发送: can0  715   [8]  04 31 01 F2 02 55 55 55
[20:49:16.019] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:16.019] ❌ 例程 0xF202: 负响应 NRC=0x31
[20:49:16.120] 测试例程 0xF202 - 停止例程...
[20:49:16.120] 发送: can0  715   [8]  04 31 02 F2 02 55 55 55
[20:49:16.121] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.121] ❌ 例程 0xF202: 负响应 NRC=0x12
[20:49:16.222] 测试例程 0xF202 - 请求结果...
[20:49:16.222] 发送: can0  715   [8]  04 31 03 F2 02 55 55 55
[20:49:16.222] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.222] ❌ 例程 0xF202: 负响应 NRC=0x12
[20:49:16.323] 测试例程 0xF203 - 启动例程...
[20:49:16.323] 发送: can0  715   [8]  04 31 01 F2 03 55 55 55
[20:49:16.323] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:16.323] ❌ 例程 0xF203: 负响应 NRC=0x31
[20:49:16.424] 测试例程 0xF203 - 停止例程...
[20:49:16.424] 发送: can0  715   [8]  04 31 02 F2 03 55 55 55
[20:49:16.425] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.425] ❌ 例程 0xF203: 负响应 NRC=0x12
[20:49:16.525] 测试例程 0xF203 - 请求结果...
[20:49:16.525] 发送: can0  715   [8]  04 31 03 F2 03 55 55 55
[20:49:16.527] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.527] ❌ 例程 0xF203: 负响应 NRC=0x12
[20:49:16.627] 测试例程 0xF204 - 启动例程...
[20:49:16.627] 发送: can0  715   [8]  04 31 01 F2 04 55 55 55
[20:49:16.628] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:16.628] ❌ 例程 0xF204: 负响应 NRC=0x31
[20:49:16.728] 测试例程 0xF204 - 停止例程...
[20:49:16.728] 发送: can0  715   [8]  04 31 02 F2 04 55 55 55
[20:49:16.729] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.729] ❌ 例程 0xF204: 负响应 NRC=0x12
[20:49:16.829] 测试例程 0xF204 - 请求结果...
[20:49:16.829] 发送: can0  715   [8]  04 31 03 F2 04 55 55 55
[20:49:16.830] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:16.830] ❌ 例程 0xF204: 负响应 NRC=0x12
[20:49:16.930] 测试例程 0xFF00 - 启动例程...
[20:49:16.930] 发送: can0  715   [8]  04 31 01 FF 00 55 55 55
[20:49:16.931] 接收: can0  795   [8]  03 7F 31 13 55 55 55 55
[20:49:16.931] ❌ 例程 0xFF00: 负响应 NRC=0x13
[20:49:17.031] 测试例程 0xFF00 - 停止例程...
[20:49:17.031] 发送: can0  715   [8]  04 31 02 FF 00 55 55 55
[20:49:17.032] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.032] ❌ 例程 0xFF00: 负响应 NRC=0x12
[20:49:17.132] 测试例程 0xFF00 - 请求结果...
[20:49:17.132] 发送: can0  715   [8]  04 31 03 FF 00 55 55 55
[20:49:17.133] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.133] ❌ 例程 0xFF00: 负响应 NRC=0x12
[20:49:17.233] 测试例程 0xFF01 - 启动例程...
[20:49:17.233] 发送: can0  715   [8]  04 31 01 FF 01 55 55 55
[20:49:17.244] 接收: can0  795   [8]  05 71 01 FF 01 00 55 55
[20:49:17.244] ✅ 例程 0xFF01 (启动例程): 01 FF 01 00 55 55
[20:49:17.344] 测试例程 0xFF01 - 停止例程...
[20:49:17.344] 发送: can0  715   [8]  04 31 02 FF 01 55 55 55
[20:49:17.346] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.346] ❌ 例程 0xFF01: 负响应 NRC=0x12
[20:49:17.446] 测试例程 0xFF01 - 请求结果...
[20:49:17.446] 发送: can0  715   [8]  04 31 03 FF 01 55 55 55
[20:49:17.447] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.447] ❌ 例程 0xFF01: 负响应 NRC=0x12
[20:49:17.547] 测试例程 0xFF02 - 启动例程...
[20:49:17.547] 发送: can0  715   [8]  04 31 01 FF 02 55 55 55
[20:49:17.549] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:17.549] ❌ 例程 0xFF02: 负响应 NRC=0x31
[20:49:17.649] 测试例程 0xFF02 - 停止例程...
[20:49:17.649] 发送: can0  715   [8]  04 31 02 FF 02 55 55 55
[20:49:17.651] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.651] ❌ 例程 0xFF02: 负响应 NRC=0x12
[20:49:17.751] 测试例程 0xFF02 - 请求结果...
[20:49:17.751] 发送: can0  715   [8]  04 31 03 FF 02 55 55 55
[20:49:17.753] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.753] ❌ 例程 0xFF02: 负响应 NRC=0x12
[20:49:17.853] 测试例程 0xFF03 - 启动例程...
[20:49:17.853] 发送: can0  715   [8]  04 31 01 FF 03 55 55 55
[20:49:17.854] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:17.854] ❌ 例程 0xFF03: 负响应 NRC=0x31
[20:49:17.954] 测试例程 0xFF03 - 停止例程...
[20:49:17.954] 发送: can0  715   [8]  04 31 02 FF 03 55 55 55
[20:49:17.955] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:17.955] ❌ 例程 0xFF03: 负响应 NRC=0x12
[20:49:18.055] 测试例程 0xFF03 - 请求结果...
[20:49:18.055] 发送: can0  715   [8]  04 31 03 FF 03 55 55 55
[20:49:18.056] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.056] ❌ 例程 0xFF03: 负响应 NRC=0x12
[20:49:18.156] 测试例程 0xFF04 - 启动例程...
[20:49:18.156] 发送: can0  715   [8]  04 31 01 FF 04 55 55 55
[20:49:18.157] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:18.157] ❌ 例程 0xFF04: 负响应 NRC=0x31
[20:49:18.257] 测试例程 0xFF04 - 停止例程...
[20:49:18.257] 发送: can0  715   [8]  04 31 02 FF 04 55 55 55
[20:49:18.258] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.258] ❌ 例程 0xFF04: 负响应 NRC=0x12
[20:49:18.358] 测试例程 0xFF04 - 请求结果...
[20:49:18.359] 发送: can0  715   [8]  04 31 03 FF 04 55 55 55
[20:49:18.359] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.359] ❌ 例程 0xFF04: 负响应 NRC=0x12
[20:49:18.459] 测试例程 0xFF10 - 启动例程...
[20:49:18.460] 发送: can0  715   [8]  04 31 01 FF 10 55 55 55
[20:49:18.460] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:18.460] ❌ 例程 0xFF10: 负响应 NRC=0x31
[20:49:18.560] 测试例程 0xFF10 - 停止例程...
[20:49:18.561] 发送: can0  715   [8]  04 31 02 FF 10 55 55 55
[20:49:18.561] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.561] ❌ 例程 0xFF10: 负响应 NRC=0x12
[20:49:18.662] 测试例程 0xFF10 - 请求结果...
[20:49:18.662] 发送: can0  715   [8]  04 31 03 FF 10 55 55 55
[20:49:18.662] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.662] ❌ 例程 0xFF10: 负响应 NRC=0x12
[20:49:18.763] 测试例程 0xFF11 - 启动例程...
[20:49:18.763] 发送: can0  715   [8]  04 31 01 FF 11 55 55 55
[20:49:18.763] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:18.763] ❌ 例程 0xFF11: 负响应 NRC=0x31
[20:49:18.864] 测试例程 0xFF11 - 停止例程...
[20:49:18.864] 发送: can0  715   [8]  04 31 02 FF 11 55 55 55
[20:49:18.864] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.865] ❌ 例程 0xFF11: 负响应 NRC=0x12
[20:49:18.965] 测试例程 0xFF11 - 请求结果...
[20:49:18.965] 发送: can0  715   [8]  04 31 03 FF 11 55 55 55
[20:49:18.967] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:18.967] ❌ 例程 0xFF11: 负响应 NRC=0x12
[20:49:19.067] 测试例程 0xFF12 - 启动例程...
[20:49:19.067] 发送: can0  715   [8]  04 31 01 FF 12 55 55 55
[20:49:19.069] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:19.069] ❌ 例程 0xFF12: 负响应 NRC=0x31
[20:49:19.169] 测试例程 0xFF12 - 停止例程...
[20:49:19.169] 发送: can0  715   [8]  04 31 02 FF 12 55 55 55
[20:49:19.171] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.171] ❌ 例程 0xFF12: 负响应 NRC=0x12
[20:49:19.271] 测试例程 0xFF12 - 请求结果...
[20:49:19.271] 发送: can0  715   [8]  04 31 03 FF 12 55 55 55
[20:49:19.272] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.272] ❌ 例程 0xFF12: 负响应 NRC=0x12
[20:49:19.372] 测试例程 0xFF13 - 启动例程...
[20:49:19.372] 发送: can0  715   [8]  04 31 01 FF 13 55 55 55
[20:49:19.373] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:19.373] ❌ 例程 0xFF13: 负响应 NRC=0x31
[20:49:19.473] 测试例程 0xFF13 - 停止例程...
[20:49:19.473] 发送: can0  715   [8]  04 31 02 FF 13 55 55 55
[20:49:19.474] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.474] ❌ 例程 0xFF13: 负响应 NRC=0x12
[20:49:19.574] 测试例程 0xFF13 - 请求结果...
[20:49:19.574] 发送: can0  715   [8]  04 31 03 FF 13 55 55 55
[20:49:19.575] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.575] ❌ 例程 0xFF13: 负响应 NRC=0x12
[20:49:19.675] 测试例程 0xFF14 - 启动例程...
[20:49:19.675] 发送: can0  715   [8]  04 31 01 FF 14 55 55 55
[20:49:19.676] 接收: can0  795   [8]  03 7F 31 31 55 55 55 55
[20:49:19.676] ❌ 例程 0xFF14: 负响应 NRC=0x31
[20:49:19.776] 测试例程 0xFF14 - 停止例程...
[20:49:19.776] 发送: can0  715   [8]  04 31 02 FF 14 55 55 55
[20:49:19.777] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.777] ❌ 例程 0xFF14: 负响应 NRC=0x12
[20:49:19.877] 测试例程 0xFF14 - 请求结果...
[20:49:19.877] 发送: can0  715   [8]  04 31 03 FF 14 55 55 55
[20:49:19.879] 接收: can0  795   [8]  03 7F 31 12 55 55 55 55
[20:49:19.879] ❌ 例程 0xFF14: 负响应 NRC=0x12
[20:49:19.979] 有效例程: 0x0203 子功能0x01 = 01 02 03 00 55 55
[20:49:19.979] 有效例程: 0xFF01 子功能0x01 = 01 FF 01 00 55 55
[20:49:19.979] 
例程扫描统计:
[20:49:19.979] 总测试例程数: 210
[20:49:19.979] 成功例程数: 2
[20:49:19.979] 失败例程数: 208
[20:49:19.979] 成功率: 0.95%
