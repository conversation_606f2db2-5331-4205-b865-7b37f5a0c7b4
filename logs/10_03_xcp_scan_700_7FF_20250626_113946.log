XCP连接扫描日志 - 2025-06-26 11:39:46.308017
扫描范围: 0x700 - 0x7FF
CAN接口: can0
============================================================

[11:39:46.308] 发送: can0  700   [8]  FF 00 00 00 00 00 00 00
[11:39:46.607] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:46.808] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:46.828] 发送: can0  701   [8]  FF 00 00 00 00 00 00 00
[11:39:47.108] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:47.358] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:47.379] 发送: can0  702   [8]  FF 00 00 00 00 00 00 00
[11:39:47.608] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:47.908] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:47.929] 发送: can0  703   [8]  FF 00 00 00 00 00 00 00
[11:39:48.108] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:48.459] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:48.479] 发送: can0  704   [8]  FF 00 00 00 00 00 00 00
[11:39:48.608] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:49.009] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:49.029] 发送: can0  705   [8]  FF 00 00 00 00 00 00 00
[11:39:49.108] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:49.559] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:49.579] 发送: can0  706   [8]  FF 00 00 00 00 00 00 00
[11:39:49.608] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:50.108] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:50.109] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:50.109] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:50.129] 发送: can0  707   [8]  FF 00 00 00 00 00 00 00
[11:39:50.609] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:50.659] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:50.679] 发送: can0  708   [8]  FF 00 00 00 00 00 00 00
[11:39:51.109] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:51.209] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:51.229] 发送: can0  709   [8]  FF 00 00 00 00 00 00 00
[11:39:51.609] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:51.759] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:51.780] 发送: can0  70A   [8]  FF 00 00 00 00 00 00 00
[11:39:52.109] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:52.310] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:52.330] 发送: can0  70B   [8]  FF 00 00 00 00 00 00 00
[11:39:52.609] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:52.860] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:52.880] 发送: can0  70C   [8]  FF 00 00 00 00 00 00 00
[11:39:53.109] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:53.410] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:53.430] 发送: can0  70D   [8]  FF 00 00 00 00 00 00 00
[11:39:53.609] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:53.960] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:53.981] 发送: can0  70E   [8]  FF 00 00 00 00 00 00 00
[11:39:54.110] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:54.511] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:54.531] 发送: can0  70F   [8]  FF 00 00 00 00 00 00 00
[11:39:54.610] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:55.061] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:55.081] 发送: can0  710   [8]  FF 00 00 00 00 00 00 00
[11:39:55.110] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:55.610] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:55.610] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:55.610] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:55.630] 发送: can0  711   [8]  FF 00 00 00 00 00 00 00
[11:39:56.110] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:56.160] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:56.181] 发送: can0  712   [8]  FF 00 00 00 00 00 00 00
[11:39:56.610] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:56.711] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:56.731] 发送: can0  713   [8]  FF 00 00 00 00 00 00 00
[11:39:57.110] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:57.261] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:57.281] 发送: can0  714   [8]  FF 00 00 00 00 00 00 00
[11:39:57.611] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:57.811] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:57.831] 发送: can0  715   [8]  FF 00 00 00 00 00 00 00
[11:39:58.111] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:58.361] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:58.382] 发送: can0  716   [8]  FF 00 00 00 00 00 00 00
[11:39:58.611] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:58.912] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:58.932] 发送: can0  717   [8]  FF 00 00 00 00 00 00 00
[11:39:59.111] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:39:59.462] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:39:59.482] 发送: can0  718   [8]  FF 00 00 00 00 00 00 00
[11:39:59.611] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:00.012] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:00.032] 发送: can0  719   [8]  FF 00 00 00 00 00 00 00
[11:40:00.111] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:00.562] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:00.583] 发送: can0  71A   [8]  FF 00 00 00 00 00 00 00
[11:40:00.612] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:01.112] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:01.112] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:01.112] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:01.132] 发送: can0  71B   [8]  FF 00 00 00 00 00 00 00
[11:40:01.612] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:01.662] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:01.682] 发送: can0  71C   [8]  FF 00 00 00 00 00 00 00
[11:40:02.112] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:02.212] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:02.233] 发送: can0  71D   [8]  FF 00 00 00 00 00 00 00
[11:40:02.612] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:02.762] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:02.783] 发送: can0  71E   [8]  FF 00 00 00 00 00 00 00
[11:40:03.112] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:03.313] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:03.333] 发送: can0  71F   [8]  FF 00 00 00 00 00 00 00
[11:40:03.612] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:03.863] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:03.883] 发送: can0  720   [8]  FF 00 00 00 00 00 00 00
[11:40:04.113] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:04.413] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:04.434] 发送: can0  721   [8]  FF 00 00 00 00 00 00 00
[11:40:04.613] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:04.964] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:04.984] 发送: can0  722   [8]  FF 00 00 00 00 00 00 00
[11:40:05.113] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:05.514] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:05.534] 发送: can0  723   [8]  FF 00 00 00 00 00 00 00
[11:40:05.613] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:06.064] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:06.084] 发送: can0  724   [8]  FF 00 00 00 00 00 00 00
[11:40:06.113] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:06.613] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:06.613] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:06.613] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:06.633] 发送: can0  725   [8]  FF 00 00 00 00 00 00 00
[11:40:07.113] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:07.164] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:07.184] 发送: can0  726   [8]  FF 00 00 00 00 00 00 00
[11:40:07.614] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:07.714] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:07.734] 发送: can0  727   [8]  FF 00 00 00 00 00 00 00
[11:40:08.114] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:08.264] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:08.284] 发送: can0  728   [8]  FF 00 00 00 00 00 00 00
[11:40:08.614] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:08.814] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:08.835] 发送: can0  729   [8]  FF 00 00 00 00 00 00 00
[11:40:09.114] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:09.365] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:09.385] 发送: can0  72A   [8]  FF 00 00 00 00 00 00 00
[11:40:09.614] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:09.915] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:09.935] 发送: can0  72B   [8]  FF 00 00 00 00 00 00 00
[11:40:10.114] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:10.465] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:10.485] 发送: can0  72C   [8]  FF 00 00 00 00 00 00 00
[11:40:10.614] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:11.015] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:11.035] 发送: can0  72D   [8]  FF 00 00 00 00 00 00 00
[11:40:11.115] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:11.566] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:11.586] 发送: can0  72E   [8]  FF 00 00 00 00 00 00 00
[11:40:11.615] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:12.115] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:12.115] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:12.115] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:12.135] 发送: can0  72F   [8]  FF 00 00 00 00 00 00 00
[11:40:12.615] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:12.665] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:12.685] 发送: can0  730   [8]  FF 00 00 00 00 00 00 00
[11:40:13.115] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:13.215] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:13.236] 发送: can0  731   [8]  FF 00 00 00 00 00 00 00
[11:40:13.615] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:13.766] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:13.786] 发送: can0  732   [8]  FF 00 00 00 00 00 00 00
[11:40:14.115] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:14.316] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:14.336] 发送: can0  733   [8]  FF 00 00 00 00 00 00 00
[11:40:14.616] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:14.866] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:14.886] 发送: can0  734   [8]  FF 00 00 00 00 00 00 00
[11:40:15.116] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:15.416] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:15.437] 发送: can0  735   [8]  FF 00 00 00 00 00 00 00
[11:40:15.616] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:15.967] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:15.987] 发送: can0  736   [8]  FF 00 00 00 00 00 00 00
[11:40:16.116] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:16.517] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:16.537] 发送: can0  737   [8]  FF 00 00 00 00 00 00 00
[11:40:16.616] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:17.067] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:17.087] 发送: can0  738   [8]  FF 00 00 00 00 00 00 00
[11:40:17.116] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:17.616] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:17.617] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:17.617] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:17.637] 发送: can0  739   [8]  FF 00 00 00 00 00 00 00
[11:40:18.117] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:18.167] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:18.187] 发送: can0  73A   [8]  FF 00 00 00 00 00 00 00
[11:40:18.617] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:18.717] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:18.737] 发送: can0  73B   [8]  FF 00 00 00 00 00 00 00
[11:40:19.117] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:19.267] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:19.288] 发送: can0  73C   [8]  FF 00 00 00 00 00 00 00
[11:40:19.617] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:19.818] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:19.838] 发送: can0  73D   [8]  FF 00 00 00 00 00 00 00
[11:40:20.117] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:20.368] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:20.388] 发送: can0  73E   [8]  FF 00 00 00 00 00 00 00
[11:40:20.617] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:20.918] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:20.938] 发送: can0  73F   [8]  FF 00 00 00 00 00 00 00
[11:40:21.117] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:21.468] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:21.489] 发送: can0  740   [8]  FF 00 00 00 00 00 00 00
[11:40:21.618] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:22.019] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
[11:40:22.039] 发送: can0  741   [8]  FF 00 00 00 00 00 00 00
[11:40:22.118] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:40:22.569] 忽略非XCP响应: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02
