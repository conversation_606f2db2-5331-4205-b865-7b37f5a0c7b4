UDS 2E服务写数据标识符扫描 - 2025-06-26 10:53:27.971285
请求CAN ID: 0x715
响应CAN ID: 0x795
测试DID数量: 95
测试数据模式: 12
============================================================

[10:53:27.971] 执行UDS 10 01 (默认会话)...
[10:53:27.971] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[10:53:27.972] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[10:53:27.972] ✅ UDS 10 01 成功
[10:53:28.473] 执行UDS 10 03 (扩展会话)...
[10:53:28.473] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[10:53:28.473] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:28.473] ❌ UDS 10 03 失败
[10:53:28.473] UDS 10 03失败，继续在默认会话中扫描
[10:53:28.973] 测试写DID 0x0001, 数据: 00...
[10:53:28.974] 发送: can0  715   [8]  04 2E 00 01 00 55 55 55
[10:53:28.974] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[10:53:28.974] ❌ DID 0x0001: 无效响应格式
[10:53:29.024] 测试写DID 0x0001, 数据: 01...
[10:53:29.024] 发送: can0  715   [8]  04 2E 00 01 01 55 55 55
[10:53:29.024] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:29.024] ❌ DID 0x0001: 无效响应格式
[10:53:29.074] 测试写DID 0x0001, 数据: FF...
[10:53:29.074] 发送: can0  715   [8]  04 2E 00 01 FF 55 55 55
[10:53:29.075] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.075] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.126] 测试写DID 0x0001, 数据: 00 00...
[10:53:29.126] 发送: can0  715   [8]  05 2E 00 01 00 00 55 55
[10:53:29.126] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.126] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.176] 测试写DID 0x0001, 数据: 00 01...
[10:53:29.176] 发送: can0  715   [8]  05 2E 00 01 00 01 55 55
[10:53:29.176] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.176] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.226] 测试写DID 0x0001, 数据: FF FF...
[10:53:29.226] 发送: can0  715   [8]  05 2E 00 01 FF FF 55 55
[10:53:29.226] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.226] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.277] 测试写DID 0x0001, 数据: 12 34...
[10:53:29.277] 发送: can0  715   [8]  05 2E 00 01 12 34 55 55
[10:53:29.277] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.277] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.327] 测试写DID 0x0001, 数据: 00 00 00...
[10:53:29.327] 发送: can0  715   [8]  06 2E 00 01 00 00 00 55
[10:53:29.327] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:29.327] ❌ DID 0x0001: 无效响应格式
[10:53:29.377] 测试写DID 0x0001, 数据: 01 02 03...
[10:53:29.377] 发送: can0  715   [8]  06 2E 00 01 01 02 03 55
[10:53:29.377] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.377] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.427] 测试写DID 0x0001, 数据: 00 00 00 00...
[10:53:29.428] 发送: can0  715   [8]  07 2E 00 01 00 00 00 00
[10:53:29.428] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.428] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.478] 测试写DID 0x0001, 数据: 12 34 56 78...
[10:53:29.478] 发送: can0  715   [8]  07 2E 00 01 12 34 56 78
[10:53:29.478] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.478] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.528] 测试写DID 0x0001, 数据: FF FF FF FF...
[10:53:29.528] 发送: can0  715   [8]  07 2E 00 01 FF FF FF FF
[10:53:29.528] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.528] ❌ DID 0x0001: 负响应 NRC=0x13
[10:53:29.578] 测试写DID 0x0002, 数据: 00...
[10:53:29.579] 发送: can0  715   [8]  04 2E 00 02 00 55 55 55
[10:53:29.579] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.579] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.629] 测试写DID 0x0002, 数据: 01...
[10:53:29.629] 发送: can0  715   [8]  04 2E 00 02 01 55 55 55
[10:53:29.629] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.629] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.679] 测试写DID 0x0002, 数据: FF...
[10:53:29.679] 发送: can0  715   [8]  04 2E 00 02 FF 55 55 55
[10:53:29.679] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.679] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.729] 测试写DID 0x0002, 数据: 00 00...
[10:53:29.729] 发送: can0  715   [8]  05 2E 00 02 00 00 55 55
[10:53:29.730] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.730] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.780] 测试写DID 0x0002, 数据: 00 01...
[10:53:29.780] 发送: can0  715   [8]  05 2E 00 02 00 01 55 55
[10:53:29.780] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.780] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.830] 测试写DID 0x0002, 数据: FF FF...
[10:53:29.830] 发送: can0  715   [8]  05 2E 00 02 FF FF 55 55
[10:53:29.830] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.830] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.880] 测试写DID 0x0002, 数据: 12 34...
[10:53:29.880] 发送: can0  715   [8]  05 2E 00 02 12 34 55 55
[10:53:29.880] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:29.880] ❌ DID 0x0002: 无效响应格式
[10:53:29.931] 测试写DID 0x0002, 数据: 00 00 00...
[10:53:29.931] 发送: can0  715   [8]  06 2E 00 02 00 00 00 55
[10:53:29.931] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.931] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:29.981] 测试写DID 0x0002, 数据: 01 02 03...
[10:53:29.981] 发送: can0  715   [8]  06 2E 00 02 01 02 03 55
[10:53:29.981] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:29.981] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:30.031] 测试写DID 0x0002, 数据: 00 00 00 00...
[10:53:30.031] 发送: can0  715   [8]  07 2E 00 02 00 00 00 00
[10:53:30.031] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.031] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:30.082] 测试写DID 0x0002, 数据: 12 34 56 78...
[10:53:30.082] 发送: can0  715   [8]  07 2E 00 02 12 34 56 78
[10:53:30.082] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.082] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:30.132] 测试写DID 0x0002, 数据: FF FF FF FF...
[10:53:30.132] 发送: can0  715   [8]  07 2E 00 02 FF FF FF FF
[10:53:30.132] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.132] ❌ DID 0x0002: 负响应 NRC=0x13
[10:53:30.182] 测试写DID 0x0003, 数据: 00...
[10:53:30.182] 发送: can0  715   [8]  04 2E 00 03 00 55 55 55
[10:53:30.182] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.182] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.232] 测试写DID 0x0003, 数据: 01...
[10:53:30.233] 发送: can0  715   [8]  04 2E 00 03 01 55 55 55
[10:53:30.233] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.233] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.283] 测试写DID 0x0003, 数据: FF...
[10:53:30.283] 发送: can0  715   [8]  04 2E 00 03 FF 55 55 55
[10:53:30.283] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.283] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.333] 测试写DID 0x0003, 数据: 00 00...
[10:53:30.333] 发送: can0  715   [8]  05 2E 00 03 00 00 55 55
[10:53:30.333] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.333] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.383] 测试写DID 0x0003, 数据: 00 01...
[10:53:30.384] 发送: can0  715   [8]  05 2E 00 03 00 01 55 55
[10:53:30.384] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.384] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.434] 测试写DID 0x0003, 数据: FF FF...
[10:53:30.434] 发送: can0  715   [8]  05 2E 00 03 FF FF 55 55
[10:53:30.434] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:30.434] ❌ DID 0x0003: 无效响应格式
[10:53:30.484] 测试写DID 0x0003, 数据: 12 34...
[10:53:30.484] 发送: can0  715   [8]  05 2E 00 03 12 34 55 55
[10:53:30.484] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.484] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.534] 测试写DID 0x0003, 数据: 00 00 00...
[10:53:30.535] 发送: can0  715   [8]  06 2E 00 03 00 00 00 55
[10:53:30.535] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.535] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.585] 测试写DID 0x0003, 数据: 01 02 03...
[10:53:30.585] 发送: can0  715   [8]  06 2E 00 03 01 02 03 55
[10:53:30.585] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.585] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.635] 测试写DID 0x0003, 数据: 00 00 00 00...
[10:53:30.635] 发送: can0  715   [8]  07 2E 00 03 00 00 00 00
[10:53:30.635] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.635] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.686] 测试写DID 0x0003, 数据: 12 34 56 78...
[10:53:30.686] 发送: can0  715   [8]  07 2E 00 03 12 34 56 78
[10:53:30.686] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.686] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.736] 测试写DID 0x0003, 数据: FF FF FF FF...
[10:53:30.736] 发送: can0  715   [8]  07 2E 00 03 FF FF FF FF
[10:53:30.736] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.736] ❌ DID 0x0003: 负响应 NRC=0x13
[10:53:30.786] 测试写DID 0x0004, 数据: 00...
[10:53:30.786] 发送: can0  715   [8]  04 2E 00 04 00 55 55 55
[10:53:30.786] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.786] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:30.836] 测试写DID 0x0004, 数据: 01...
[10:53:30.836] 发送: can0  715   [8]  04 2E 00 04 01 55 55 55
[10:53:30.837] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.837] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:30.887] 测试写DID 0x0004, 数据: FF...
[10:53:30.887] 发送: can0  715   [8]  04 2E 00 04 FF 55 55 55
[10:53:30.887] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.887] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:30.937] 测试写DID 0x0004, 数据: 00 00...
[10:53:30.937] 发送: can0  715   [8]  05 2E 00 04 00 00 55 55
[10:53:30.937] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:30.937] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:30.987] 测试写DID 0x0004, 数据: 00 01...
[10:53:30.987] 发送: can0  715   [8]  05 2E 00 04 00 01 55 55
[10:53:30.987] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:30.987] ❌ DID 0x0004: 无效响应格式
[10:53:31.038] 测试写DID 0x0004, 数据: FF FF...
[10:53:31.038] 发送: can0  715   [8]  05 2E 00 04 FF FF 55 55
[10:53:31.038] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.038] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.088] 测试写DID 0x0004, 数据: 12 34...
[10:53:31.088] 发送: can0  715   [8]  05 2E 00 04 12 34 55 55
[10:53:31.088] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.088] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.138] 测试写DID 0x0004, 数据: 00 00 00...
[10:53:31.138] 发送: can0  715   [8]  06 2E 00 04 00 00 00 55
[10:53:31.138] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.138] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.189] 测试写DID 0x0004, 数据: 01 02 03...
[10:53:31.189] 发送: can0  715   [8]  06 2E 00 04 01 02 03 55
[10:53:31.189] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.189] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.239] 测试写DID 0x0004, 数据: 00 00 00 00...
[10:53:31.239] 发送: can0  715   [8]  07 2E 00 04 00 00 00 00
[10:53:31.239] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.239] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.289] 测试写DID 0x0004, 数据: 12 34 56 78...
[10:53:31.289] 发送: can0  715   [8]  07 2E 00 04 12 34 56 78
[10:53:31.289] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.289] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.339] 测试写DID 0x0004, 数据: FF FF FF FF...
[10:53:31.339] 发送: can0  715   [8]  07 2E 00 04 FF FF FF FF
[10:53:31.340] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.340] ❌ DID 0x0004: 负响应 NRC=0x13
[10:53:31.390] 测试写DID 0x0005, 数据: 00...
[10:53:31.390] 发送: can0  715   [8]  04 2E 00 05 00 55 55 55
[10:53:31.390] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.390] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.440] 测试写DID 0x0005, 数据: 01...
[10:53:31.440] 发送: can0  715   [8]  04 2E 00 05 01 55 55 55
[10:53:31.440] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.440] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.490] 测试写DID 0x0005, 数据: FF...
[10:53:31.490] 发送: can0  715   [8]  04 2E 00 05 FF 55 55 55
[10:53:31.490] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.490] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.541] 测试写DID 0x0005, 数据: 00 00...
[10:53:31.541] 发送: can0  715   [8]  05 2E 00 05 00 00 55 55
[10:53:31.541] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:31.541] ❌ DID 0x0005: 无效响应格式
[10:53:31.591] 测试写DID 0x0005, 数据: 00 01...
[10:53:31.591] 发送: can0  715   [8]  05 2E 00 05 00 01 55 55
[10:53:31.591] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.591] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.641] 测试写DID 0x0005, 数据: FF FF...
[10:53:31.641] 发送: can0  715   [8]  05 2E 00 05 FF FF 55 55
[10:53:31.641] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.642] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.692] 测试写DID 0x0005, 数据: 12 34...
[10:53:31.692] 发送: can0  715   [8]  05 2E 00 05 12 34 55 55
[10:53:31.692] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.692] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.742] 测试写DID 0x0005, 数据: 00 00 00...
[10:53:31.742] 发送: can0  715   [8]  06 2E 00 05 00 00 00 55
[10:53:31.742] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.742] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.792] 测试写DID 0x0005, 数据: 01 02 03...
[10:53:31.792] 发送: can0  715   [8]  06 2E 00 05 01 02 03 55
[10:53:31.792] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.792] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.843] 测试写DID 0x0005, 数据: 00 00 00 00...
[10:53:31.843] 发送: can0  715   [8]  07 2E 00 05 00 00 00 00
[10:53:31.843] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.843] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.893] 测试写DID 0x0005, 数据: 12 34 56 78...
[10:53:31.893] 发送: can0  715   [8]  07 2E 00 05 12 34 56 78
[10:53:31.893] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.893] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.943] 测试写DID 0x0005, 数据: FF FF FF FF...
[10:53:31.944] 发送: can0  715   [8]  07 2E 00 05 FF FF FF FF
[10:53:31.944] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.944] ❌ DID 0x0005: 负响应 NRC=0x13
[10:53:31.994] 测试写DID 0x0010, 数据: 00...
[10:53:31.994] 发送: can0  715   [8]  04 2E 00 10 00 55 55 55
[10:53:31.994] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:31.994] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.044] 测试写DID 0x0010, 数据: 01...
[10:53:32.044] 发送: can0  715   [8]  04 2E 00 10 01 55 55 55
[10:53:32.044] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.044] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.094] 测试写DID 0x0010, 数据: FF...
[10:53:32.094] 发送: can0  715   [8]  04 2E 00 10 FF 55 55 55
[10:53:32.095] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:32.095] ❌ DID 0x0010: 无效响应格式
[10:53:32.145] 测试写DID 0x0010, 数据: 00 00...
[10:53:32.145] 发送: can0  715   [8]  05 2E 00 10 00 00 55 55
[10:53:32.145] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.145] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.195] 测试写DID 0x0010, 数据: 00 01...
[10:53:32.195] 发送: can0  715   [8]  05 2E 00 10 00 01 55 55
[10:53:32.195] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.195] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.245] 测试写DID 0x0010, 数据: FF FF...
[10:53:32.246] 发送: can0  715   [8]  05 2E 00 10 FF FF 55 55
[10:53:32.246] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.246] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.296] 测试写DID 0x0010, 数据: 12 34...
[10:53:32.296] 发送: can0  715   [8]  05 2E 00 10 12 34 55 55
[10:53:32.296] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.296] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.346] 测试写DID 0x0010, 数据: 00 00 00...
[10:53:32.346] 发送: can0  715   [8]  06 2E 00 10 00 00 00 55
[10:53:32.346] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.346] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.396] 测试写DID 0x0010, 数据: 01 02 03...
[10:53:32.396] 发送: can0  715   [8]  06 2E 00 10 01 02 03 55
[10:53:32.397] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.397] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.447] 测试写DID 0x0010, 数据: 00 00 00 00...
[10:53:32.447] 发送: can0  715   [8]  07 2E 00 10 00 00 00 00
[10:53:32.447] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.447] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.497] 测试写DID 0x0010, 数据: 12 34 56 78...
[10:53:32.497] 发送: can0  715   [8]  07 2E 00 10 12 34 56 78
[10:53:32.497] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.497] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.547] 测试写DID 0x0010, 数据: FF FF FF FF...
[10:53:32.547] 发送: can0  715   [8]  07 2E 00 10 FF FF FF FF
[10:53:32.547] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.547] ❌ DID 0x0010: 负响应 NRC=0x13
[10:53:32.598] 测试写DID 0x0011, 数据: 00...
[10:53:32.598] 发送: can0  715   [8]  04 2E 00 11 00 55 55 55
[10:53:32.598] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.598] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.648] 测试写DID 0x0011, 数据: 01...
[10:53:32.648] 发送: can0  715   [8]  04 2E 00 11 01 55 55 55
[10:53:32.648] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:32.648] ❌ DID 0x0011: 无效响应格式
[10:53:32.698] 测试写DID 0x0011, 数据: FF...
[10:53:32.698] 发送: can0  715   [8]  04 2E 00 11 FF 55 55 55
[10:53:32.698] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.698] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.748] 测试写DID 0x0011, 数据: 00 00...
[10:53:32.749] 发送: can0  715   [8]  05 2E 00 11 00 00 55 55
[10:53:32.749] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.749] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.799] 测试写DID 0x0011, 数据: 00 01...
[10:53:32.799] 发送: can0  715   [8]  05 2E 00 11 00 01 55 55
[10:53:32.799] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.799] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.849] 测试写DID 0x0011, 数据: FF FF...
[10:53:32.849] 发送: can0  715   [8]  05 2E 00 11 FF FF 55 55
[10:53:32.849] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.849] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.899] 测试写DID 0x0011, 数据: 12 34...
[10:53:32.899] 发送: can0  715   [8]  05 2E 00 11 12 34 55 55
[10:53:32.899] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.900] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:32.950] 测试写DID 0x0011, 数据: 00 00 00...
[10:53:32.950] 发送: can0  715   [8]  06 2E 00 11 00 00 00 55
[10:53:32.950] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:32.950] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:33.000] 测试写DID 0x0011, 数据: 01 02 03...
[10:53:33.000] 发送: can0  715   [8]  06 2E 00 11 01 02 03 55
[10:53:33.000] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.000] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:33.050] 测试写DID 0x0011, 数据: 00 00 00 00...
[10:53:33.050] 发送: can0  715   [8]  07 2E 00 11 00 00 00 00
[10:53:33.050] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.050] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:33.100] 测试写DID 0x0011, 数据: 12 34 56 78...
[10:53:33.101] 发送: can0  715   [8]  07 2E 00 11 12 34 56 78
[10:53:33.101] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.101] ❌ DID 0x0011: 负响应 NRC=0x13
[10:53:33.151] 测试写DID 0x0011, 数据: FF FF FF FF...
[10:53:33.151] 发送: can0  715   [8]  07 2E 00 11 FF FF FF FF
[10:53:33.151] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:33.151] ❌ DID 0x0011: 无效响应格式
[10:53:33.201] 测试写DID 0x0012, 数据: 00...
[10:53:33.201] 发送: can0  715   [8]  04 2E 00 12 00 55 55 55
[10:53:33.201] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.201] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.252] 测试写DID 0x0012, 数据: 01...
[10:53:33.252] 发送: can0  715   [8]  04 2E 00 12 01 55 55 55
[10:53:33.252] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.252] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.302] 测试写DID 0x0012, 数据: FF...
[10:53:33.302] 发送: can0  715   [8]  04 2E 00 12 FF 55 55 55
[10:53:33.302] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.302] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.352] 测试写DID 0x0012, 数据: 00 00...
[10:53:33.352] 发送: can0  715   [8]  05 2E 00 12 00 00 55 55
[10:53:33.352] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.352] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.403] 测试写DID 0x0012, 数据: 00 01...
[10:53:33.403] 发送: can0  715   [8]  05 2E 00 12 00 01 55 55
[10:53:33.403] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.403] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.453] 测试写DID 0x0012, 数据: FF FF...
[10:53:33.453] 发送: can0  715   [8]  05 2E 00 12 FF FF 55 55
[10:53:33.453] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.453] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.503] 测试写DID 0x0012, 数据: 12 34...
[10:53:33.503] 发送: can0  715   [8]  05 2E 00 12 12 34 55 55
[10:53:33.503] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.503] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.553] 测试写DID 0x0012, 数据: 00 00 00...
[10:53:33.554] 发送: can0  715   [8]  06 2E 00 12 00 00 00 55
[10:53:33.554] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.554] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.604] 测试写DID 0x0012, 数据: 01 02 03...
[10:53:33.604] 发送: can0  715   [8]  06 2E 00 12 01 02 03 55
[10:53:33.604] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.604] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.654] 测试写DID 0x0012, 数据: 00 00 00 00...
[10:53:33.654] 发送: can0  715   [8]  07 2E 00 12 00 00 00 00
[10:53:33.654] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.654] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.704] 测试写DID 0x0012, 数据: 12 34 56 78...
[10:53:33.704] 发送: can0  715   [8]  07 2E 00 12 12 34 56 78
[10:53:33.704] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:33.705] ❌ DID 0x0012: 无效响应格式
[10:53:33.755] 测试写DID 0x0012, 数据: FF FF FF FF...
[10:53:33.755] 发送: can0  715   [8]  07 2E 00 12 FF FF FF FF
[10:53:33.755] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.755] ❌ DID 0x0012: 负响应 NRC=0x13
[10:53:33.805] 测试写DID 0x0013, 数据: 00...
[10:53:33.805] 发送: can0  715   [8]  04 2E 00 13 00 55 55 55
[10:53:33.805] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.805] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:33.855] 测试写DID 0x0013, 数据: 01...
[10:53:33.855] 发送: can0  715   [8]  04 2E 00 13 01 55 55 55
[10:53:33.855] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.855] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:33.905] 测试写DID 0x0013, 数据: FF...
[10:53:33.906] 发送: can0  715   [8]  04 2E 00 13 FF 55 55 55
[10:53:33.906] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.906] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:33.956] 测试写DID 0x0013, 数据: 00 00...
[10:53:33.956] 发送: can0  715   [8]  05 2E 00 13 00 00 55 55
[10:53:33.956] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:33.956] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.006] 测试写DID 0x0013, 数据: 00 01...
[10:53:34.006] 发送: can0  715   [8]  05 2E 00 13 00 01 55 55
[10:53:34.006] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.006] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.056] 测试写DID 0x0013, 数据: FF FF...
[10:53:34.057] 发送: can0  715   [8]  05 2E 00 13 FF FF 55 55
[10:53:34.057] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.057] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.107] 测试写DID 0x0013, 数据: 12 34...
[10:53:34.107] 发送: can0  715   [8]  05 2E 00 13 12 34 55 55
[10:53:34.107] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.107] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.157] 测试写DID 0x0013, 数据: 00 00 00...
[10:53:34.157] 发送: can0  715   [8]  06 2E 00 13 00 00 00 55
[10:53:34.157] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.157] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.207] 测试写DID 0x0013, 数据: 01 02 03...
[10:53:34.207] 发送: can0  715   [8]  06 2E 00 13 01 02 03 55
[10:53:34.208] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.208] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.258] 测试写DID 0x0013, 数据: 00 00 00 00...
[10:53:34.258] 发送: can0  715   [8]  07 2E 00 13 00 00 00 00
[10:53:34.258] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:34.258] ❌ DID 0x0013: 无效响应格式
[10:53:34.308] 测试写DID 0x0013, 数据: 12 34 56 78...
[10:53:34.308] 发送: can0  715   [8]  07 2E 00 13 12 34 56 78
[10:53:34.308] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.308] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.358] 测试写DID 0x0013, 数据: FF FF FF FF...
[10:53:34.358] 发送: can0  715   [8]  07 2E 00 13 FF FF FF FF
[10:53:34.359] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.359] ❌ DID 0x0013: 负响应 NRC=0x13
[10:53:34.409] 测试写DID 0x0014, 数据: 00...
[10:53:34.409] 发送: can0  715   [8]  04 2E 00 14 00 55 55 55
[10:53:34.409] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.409] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.459] 测试写DID 0x0014, 数据: 01...
[10:53:34.459] 发送: can0  715   [8]  04 2E 00 14 01 55 55 55
[10:53:34.459] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.459] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.509] 测试写DID 0x0014, 数据: FF...
[10:53:34.509] 发送: can0  715   [8]  04 2E 00 14 FF 55 55 55
[10:53:34.509] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.509] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.560] 测试写DID 0x0014, 数据: 00 00...
[10:53:34.560] 发送: can0  715   [8]  05 2E 00 14 00 00 55 55
[10:53:34.560] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.560] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.610] 测试写DID 0x0014, 数据: 00 01...
[10:53:34.610] 发送: can0  715   [8]  05 2E 00 14 00 01 55 55
[10:53:34.610] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.610] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.660] 测试写DID 0x0014, 数据: FF FF...
[10:53:34.661] 发送: can0  715   [8]  05 2E 00 14 FF FF 55 55
[10:53:34.661] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.661] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.711] 测试写DID 0x0014, 数据: 12 34...
[10:53:34.711] 发送: can0  715   [8]  05 2E 00 14 12 34 55 55
[10:53:34.711] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.711] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.761] 测试写DID 0x0014, 数据: 00 00 00...
[10:53:34.761] 发送: can0  715   [8]  06 2E 00 14 00 00 00 55
[10:53:34.761] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.761] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.812] 测试写DID 0x0014, 数据: 01 02 03...
[10:53:34.812] 发送: can0  715   [8]  06 2E 00 14 01 02 03 55
[10:53:34.812] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:34.812] ❌ DID 0x0014: 无效响应格式
[10:53:34.862] 测试写DID 0x0014, 数据: 00 00 00 00...
[10:53:34.862] 发送: can0  715   [8]  07 2E 00 14 00 00 00 00
[10:53:34.862] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.862] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.912] 测试写DID 0x0014, 数据: 12 34 56 78...
[10:53:34.912] 发送: can0  715   [8]  07 2E 00 14 12 34 56 78
[10:53:34.912] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.912] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:34.963] 测试写DID 0x0014, 数据: FF FF FF FF...
[10:53:34.963] 发送: can0  715   [8]  07 2E 00 14 FF FF FF FF
[10:53:34.963] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:34.963] ❌ DID 0x0014: 负响应 NRC=0x13
[10:53:35.013] 测试写DID 0x0100, 数据: 00...
[10:53:35.013] 发送: can0  715   [8]  04 2E 01 00 00 55 55 55
[10:53:35.013] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.013] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.063] 测试写DID 0x0100, 数据: 01...
[10:53:35.063] 发送: can0  715   [8]  04 2E 01 00 01 55 55 55
[10:53:35.063] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.063] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.114] 测试写DID 0x0100, 数据: FF...
[10:53:35.114] 发送: can0  715   [8]  04 2E 01 00 FF 55 55 55
[10:53:35.114] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.114] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.164] 测试写DID 0x0100, 数据: 00 00...
[10:53:35.164] 发送: can0  715   [8]  05 2E 01 00 00 00 55 55
[10:53:35.164] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.164] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.214] 测试写DID 0x0100, 数据: 00 01...
[10:53:35.214] 发送: can0  715   [8]  05 2E 01 00 00 01 55 55
[10:53:35.214] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.214] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.265] 测试写DID 0x0100, 数据: FF FF...
[10:53:35.265] 发送: can0  715   [8]  05 2E 01 00 FF FF 55 55
[10:53:35.265] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.265] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.315] 测试写DID 0x0100, 数据: 12 34...
[10:53:35.315] 发送: can0  715   [8]  05 2E 01 00 12 34 55 55
[10:53:35.315] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.315] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.365] 测试写DID 0x0100, 数据: 00 00 00...
[10:53:35.366] 发送: can0  715   [8]  06 2E 01 00 00 00 00 55
[10:53:35.366] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:35.366] ❌ DID 0x0100: 无效响应格式
[10:53:35.416] 测试写DID 0x0100, 数据: 01 02 03...
[10:53:35.416] 发送: can0  715   [8]  06 2E 01 00 01 02 03 55
[10:53:35.416] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.416] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.466] 测试写DID 0x0100, 数据: 00 00 00 00...
[10:53:35.466] 发送: can0  715   [8]  07 2E 01 00 00 00 00 00
[10:53:35.466] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.466] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.516] 测试写DID 0x0100, 数据: 12 34 56 78...
[10:53:35.516] 发送: can0  715   [8]  07 2E 01 00 12 34 56 78
[10:53:35.516] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.517] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.567] 测试写DID 0x0100, 数据: FF FF FF FF...
[10:53:35.567] 发送: can0  715   [8]  07 2E 01 00 FF FF FF FF
[10:53:35.567] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.567] ❌ DID 0x0100: 负响应 NRC=0x13
[10:53:35.617] 测试写DID 0x0101, 数据: 00...
[10:53:35.617] 发送: can0  715   [8]  04 2E 01 01 00 55 55 55
[10:53:35.617] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.617] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.667] 测试写DID 0x0101, 数据: 01...
[10:53:35.667] 发送: can0  715   [8]  04 2E 01 01 01 55 55 55
[10:53:35.667] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.667] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.718] 测试写DID 0x0101, 数据: FF...
[10:53:35.718] 发送: can0  715   [8]  04 2E 01 01 FF 55 55 55
[10:53:35.718] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.718] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.768] 测试写DID 0x0101, 数据: 00 00...
[10:53:35.768] 发送: can0  715   [8]  05 2E 01 01 00 00 55 55
[10:53:35.768] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.768] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.818] 测试写DID 0x0101, 数据: 00 01...
[10:53:35.818] 发送: can0  715   [8]  05 2E 01 01 00 01 55 55
[10:53:35.818] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.818] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.869] 测试写DID 0x0101, 数据: FF FF...
[10:53:35.869] 发送: can0  715   [8]  05 2E 01 01 FF FF 55 55
[10:53:35.869] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.869] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:35.919] 测试写DID 0x0101, 数据: 12 34...
[10:53:35.919] 发送: can0  715   [8]  05 2E 01 01 12 34 55 55
[10:53:35.919] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:35.919] ❌ DID 0x0101: 无效响应格式
[10:53:35.969] 测试写DID 0x0101, 数据: 00 00 00...
[10:53:35.969] 发送: can0  715   [8]  06 2E 01 01 00 00 00 55
[10:53:35.969] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:35.969] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:36.020] 测试写DID 0x0101, 数据: 01 02 03...
[10:53:36.020] 发送: can0  715   [8]  06 2E 01 01 01 02 03 55
[10:53:36.020] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.020] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:36.070] 测试写DID 0x0101, 数据: 00 00 00 00...
[10:53:36.070] 发送: can0  715   [8]  07 2E 01 01 00 00 00 00
[10:53:36.070] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.070] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:36.120] 测试写DID 0x0101, 数据: 12 34 56 78...
[10:53:36.120] 发送: can0  715   [8]  07 2E 01 01 12 34 56 78
[10:53:36.120] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.120] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:36.171] 测试写DID 0x0101, 数据: FF FF FF FF...
[10:53:36.171] 发送: can0  715   [8]  07 2E 01 01 FF FF FF FF
[10:53:36.171] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.171] ❌ DID 0x0101: 负响应 NRC=0x13
[10:53:36.221] 测试写DID 0x0102, 数据: 00...
[10:53:36.221] 发送: can0  715   [8]  04 2E 01 02 00 55 55 55
[10:53:36.221] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.221] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.271] 测试写DID 0x0102, 数据: 01...
[10:53:36.271] 发送: can0  715   [8]  04 2E 01 02 01 55 55 55
[10:53:36.271] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.271] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.321] 测试写DID 0x0102, 数据: FF...
[10:53:36.321] 发送: can0  715   [8]  04 2E 01 02 FF 55 55 55
[10:53:36.322] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.322] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.372] 测试写DID 0x0102, 数据: 00 00...
[10:53:36.372] 发送: can0  715   [8]  05 2E 01 02 00 00 55 55
[10:53:36.372] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.372] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.422] 测试写DID 0x0102, 数据: 00 01...
[10:53:36.422] 发送: can0  715   [8]  05 2E 01 02 00 01 55 55
[10:53:36.422] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.422] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.472] 测试写DID 0x0102, 数据: FF FF...
[10:53:36.472] 发送: can0  715   [8]  05 2E 01 02 FF FF 55 55
[10:53:36.472] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:36.472] ❌ DID 0x0102: 无效响应格式
[10:53:36.523] 测试写DID 0x0102, 数据: 12 34...
[10:53:36.523] 发送: can0  715   [8]  05 2E 01 02 12 34 55 55
[10:53:36.523] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.523] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.573] 测试写DID 0x0102, 数据: 00 00 00...
[10:53:36.573] 发送: can0  715   [8]  06 2E 01 02 00 00 00 55
[10:53:36.573] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.573] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.623] 测试写DID 0x0102, 数据: 01 02 03...
[10:53:36.623] 发送: can0  715   [8]  06 2E 01 02 01 02 03 55
[10:53:36.623] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.623] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.674] 测试写DID 0x0102, 数据: 00 00 00 00...
[10:53:36.674] 发送: can0  715   [8]  07 2E 01 02 00 00 00 00
[10:53:36.674] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.674] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.724] 测试写DID 0x0102, 数据: 12 34 56 78...
[10:53:36.724] 发送: can0  715   [8]  07 2E 01 02 12 34 56 78
[10:53:36.724] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.724] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.774] 测试写DID 0x0102, 数据: FF FF FF FF...
[10:53:36.774] 发送: can0  715   [8]  07 2E 01 02 FF FF FF FF
[10:53:36.775] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.775] ❌ DID 0x0102: 负响应 NRC=0x13
[10:53:36.825] 测试写DID 0x0103, 数据: 00...
[10:53:36.825] 发送: can0  715   [8]  04 2E 01 03 00 55 55 55
[10:53:36.825] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.825] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:36.875] 测试写DID 0x0103, 数据: 01...
[10:53:36.875] 发送: can0  715   [8]  04 2E 01 03 01 55 55 55
[10:53:36.875] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.875] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:36.925] 测试写DID 0x0103, 数据: FF...
[10:53:36.925] 发送: can0  715   [8]  04 2E 01 03 FF 55 55 55
[10:53:36.925] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.925] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:36.976] 测试写DID 0x0103, 数据: 00 00...
[10:53:36.976] 发送: can0  715   [8]  05 2E 01 03 00 00 55 55
[10:53:36.976] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:36.976] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.026] 测试写DID 0x0103, 数据: 00 01...
[10:53:37.026] 发送: can0  715   [8]  05 2E 01 03 00 01 55 55
[10:53:37.026] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:37.026] ❌ DID 0x0103: 无效响应格式
[10:53:37.076] 测试写DID 0x0103, 数据: FF FF...
[10:53:37.076] 发送: can0  715   [8]  05 2E 01 03 FF FF 55 55
[10:53:37.076] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.076] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.127] 测试写DID 0x0103, 数据: 12 34...
[10:53:37.127] 发送: can0  715   [8]  05 2E 01 03 12 34 55 55
[10:53:37.127] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.127] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.177] 测试写DID 0x0103, 数据: 00 00 00...
[10:53:37.177] 发送: can0  715   [8]  06 2E 01 03 00 00 00 55
[10:53:37.177] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.177] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.227] 测试写DID 0x0103, 数据: 01 02 03...
[10:53:37.227] 发送: can0  715   [8]  06 2E 01 03 01 02 03 55
[10:53:37.227] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.227] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.277] 测试写DID 0x0103, 数据: 00 00 00 00...
[10:53:37.278] 发送: can0  715   [8]  07 2E 01 03 00 00 00 00
[10:53:37.278] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.278] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.328] 测试写DID 0x0103, 数据: 12 34 56 78...
[10:53:37.328] 发送: can0  715   [8]  07 2E 01 03 12 34 56 78
[10:53:37.328] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.328] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.378] 测试写DID 0x0103, 数据: FF FF FF FF...
[10:53:37.378] 发送: can0  715   [8]  07 2E 01 03 FF FF FF FF
[10:53:37.378] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.378] ❌ DID 0x0103: 负响应 NRC=0x13
[10:53:37.428] 测试写DID 0x0104, 数据: 00...
[10:53:37.429] 发送: can0  715   [8]  04 2E 01 04 00 55 55 55
[10:53:37.429] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.429] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.479] 测试写DID 0x0104, 数据: 01...
[10:53:37.479] 发送: can0  715   [8]  04 2E 01 04 01 55 55 55
[10:53:37.479] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.479] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.529] 测试写DID 0x0104, 数据: FF...
[10:53:37.529] 发送: can0  715   [8]  04 2E 01 04 FF 55 55 55
[10:53:37.529] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.529] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.579] 测试写DID 0x0104, 数据: 00 00...
[10:53:37.580] 发送: can0  715   [8]  05 2E 01 04 00 00 55 55
[10:53:37.580] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:37.580] ❌ DID 0x0104: 无效响应格式
[10:53:37.630] 测试写DID 0x0104, 数据: 00 01...
[10:53:37.630] 发送: can0  715   [8]  05 2E 01 04 00 01 55 55
[10:53:37.630] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.630] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.680] 测试写DID 0x0104, 数据: FF FF...
[10:53:37.680] 发送: can0  715   [8]  05 2E 01 04 FF FF 55 55
[10:53:37.680] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.680] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.730] 测试写DID 0x0104, 数据: 12 34...
[10:53:37.730] 发送: can0  715   [8]  05 2E 01 04 12 34 55 55
[10:53:37.730] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.730] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.781] 测试写DID 0x0104, 数据: 00 00 00...
[10:53:37.781] 发送: can0  715   [8]  06 2E 01 04 00 00 00 55
[10:53:37.781] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.781] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.831] 测试写DID 0x0104, 数据: 01 02 03...
[10:53:37.831] 发送: can0  715   [8]  06 2E 01 04 01 02 03 55
[10:53:37.831] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.831] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.881] 测试写DID 0x0104, 数据: 00 00 00 00...
[10:53:37.881] 发送: can0  715   [8]  07 2E 01 04 00 00 00 00
[10:53:37.881] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.881] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.932] 测试写DID 0x0104, 数据: 12 34 56 78...
[10:53:37.932] 发送: can0  715   [8]  07 2E 01 04 12 34 56 78
[10:53:37.932] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.932] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:37.982] 测试写DID 0x0104, 数据: FF FF FF FF...
[10:53:37.982] 发送: can0  715   [8]  07 2E 01 04 FF FF FF FF
[10:53:37.982] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:37.982] ❌ DID 0x0104: 负响应 NRC=0x13
[10:53:38.032] 测试写DID 0x0200, 数据: 00...
[10:53:38.032] 发送: can0  715   [8]  04 2E 02 00 00 55 55 55
[10:53:38.032] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.033] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.083] 测试写DID 0x0200, 数据: 01...
[10:53:38.083] 发送: can0  715   [8]  04 2E 02 00 01 55 55 55
[10:53:38.083] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.083] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.133] 测试写DID 0x0200, 数据: FF...
[10:53:38.133] 发送: can0  715   [8]  04 2E 02 00 FF 55 55 55
[10:53:38.133] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:38.133] ❌ DID 0x0200: 无效响应格式
[10:53:38.183] 测试写DID 0x0200, 数据: 00 00...
[10:53:38.183] 发送: can0  715   [8]  05 2E 02 00 00 00 55 55
[10:53:38.184] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.184] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.234] 测试写DID 0x0200, 数据: 00 01...
[10:53:38.234] 发送: can0  715   [8]  05 2E 02 00 00 01 55 55
[10:53:38.234] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.234] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.284] 测试写DID 0x0200, 数据: FF FF...
[10:53:38.284] 发送: can0  715   [8]  05 2E 02 00 FF FF 55 55
[10:53:38.284] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.284] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.334] 测试写DID 0x0200, 数据: 12 34...
[10:53:38.334] 发送: can0  715   [8]  05 2E 02 00 12 34 55 55
[10:53:38.334] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.334] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.385] 测试写DID 0x0200, 数据: 00 00 00...
[10:53:38.385] 发送: can0  715   [8]  06 2E 02 00 00 00 00 55
[10:53:38.385] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.385] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.435] 测试写DID 0x0200, 数据: 01 02 03...
[10:53:38.435] 发送: can0  715   [8]  06 2E 02 00 01 02 03 55
[10:53:38.435] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.435] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.485] 测试写DID 0x0200, 数据: 00 00 00 00...
[10:53:38.485] 发送: can0  715   [8]  07 2E 02 00 00 00 00 00
[10:53:38.486] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.486] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.536] 测试写DID 0x0200, 数据: 12 34 56 78...
[10:53:38.536] 发送: can0  715   [8]  07 2E 02 00 12 34 56 78
[10:53:38.536] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.536] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.586] 测试写DID 0x0200, 数据: FF FF FF FF...
[10:53:38.586] 发送: can0  715   [8]  07 2E 02 00 FF FF FF FF
[10:53:38.586] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.586] ❌ DID 0x0200: 负响应 NRC=0x13
[10:53:38.636] 测试写DID 0x0201, 数据: 00...
[10:53:38.636] 发送: can0  715   [8]  04 2E 02 01 00 55 55 55
[10:53:38.636] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.636] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.687] 测试写DID 0x0201, 数据: 01...
[10:53:38.687] 发送: can0  715   [8]  04 2E 02 01 01 55 55 55
[10:53:38.687] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:38.687] ❌ DID 0x0201: 无效响应格式
[10:53:38.737] 测试写DID 0x0201, 数据: FF...
[10:53:38.737] 发送: can0  715   [8]  04 2E 02 01 FF 55 55 55
[10:53:38.737] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.737] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.787] 测试写DID 0x0201, 数据: 00 00...
[10:53:38.787] 发送: can0  715   [8]  05 2E 02 01 00 00 55 55
[10:53:38.787] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.787] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.837] 测试写DID 0x0201, 数据: 00 01...
[10:53:38.838] 发送: can0  715   [8]  05 2E 02 01 00 01 55 55
[10:53:38.838] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.838] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.888] 测试写DID 0x0201, 数据: FF FF...
[10:53:38.888] 发送: can0  715   [8]  05 2E 02 01 FF FF 55 55
[10:53:38.888] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.888] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.938] 测试写DID 0x0201, 数据: 12 34...
[10:53:38.938] 发送: can0  715   [8]  05 2E 02 01 12 34 55 55
[10:53:38.938] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.938] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:38.988] 测试写DID 0x0201, 数据: 00 00 00...
[10:53:38.988] 发送: can0  715   [8]  06 2E 02 01 00 00 00 55
[10:53:38.988] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:38.989] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:39.039] 测试写DID 0x0201, 数据: 01 02 03...
[10:53:39.039] 发送: can0  715   [8]  06 2E 02 01 01 02 03 55
[10:53:39.039] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.039] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:39.089] 测试写DID 0x0201, 数据: 00 00 00 00...
[10:53:39.089] 发送: can0  715   [8]  07 2E 02 01 00 00 00 00
[10:53:39.089] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.089] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:39.139] 测试写DID 0x0201, 数据: 12 34 56 78...
[10:53:39.139] 发送: can0  715   [8]  07 2E 02 01 12 34 56 78
[10:53:39.139] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.139] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:39.189] 测试写DID 0x0201, 数据: FF FF FF FF...
[10:53:39.190] 发送: can0  715   [8]  07 2E 02 01 FF FF FF FF
[10:53:39.190] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.190] ❌ DID 0x0201: 负响应 NRC=0x13
[10:53:39.240] 测试写DID 0x0202, 数据: 00...
[10:53:39.240] 发送: can0  715   [8]  04 2E 02 02 00 55 55 55
[10:53:39.240] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:39.240] ❌ DID 0x0202: 无效响应格式
[10:53:39.290] 测试写DID 0x0202, 数据: 01...
[10:53:39.290] 发送: can0  715   [8]  04 2E 02 02 01 55 55 55
[10:53:39.290] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.290] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.340] 测试写DID 0x0202, 数据: FF...
[10:53:39.341] 发送: can0  715   [8]  04 2E 02 02 FF 55 55 55
[10:53:39.341] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.341] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.391] 测试写DID 0x0202, 数据: 00 00...
[10:53:39.391] 发送: can0  715   [8]  05 2E 02 02 00 00 55 55
[10:53:39.391] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.391] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.441] 测试写DID 0x0202, 数据: 00 01...
[10:53:39.441] 发送: can0  715   [8]  05 2E 02 02 00 01 55 55
[10:53:39.441] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.441] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.492] 测试写DID 0x0202, 数据: FF FF...
[10:53:39.492] 发送: can0  715   [8]  05 2E 02 02 FF FF 55 55
[10:53:39.492] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.492] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.542] 测试写DID 0x0202, 数据: 12 34...
[10:53:39.542] 发送: can0  715   [8]  05 2E 02 02 12 34 55 55
[10:53:39.542] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.542] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.592] 测试写DID 0x0202, 数据: 00 00 00...
[10:53:39.593] 发送: can0  715   [8]  06 2E 02 02 00 00 00 55
[10:53:39.593] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.593] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.643] 测试写DID 0x0202, 数据: 01 02 03...
[10:53:39.643] 发送: can0  715   [8]  06 2E 02 02 01 02 03 55
[10:53:39.643] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.643] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.693] 测试写DID 0x0202, 数据: 00 00 00 00...
[10:53:39.693] 发送: can0  715   [8]  07 2E 02 02 00 00 00 00
[10:53:39.693] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.693] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.743] 测试写DID 0x0202, 数据: 12 34 56 78...
[10:53:39.744] 发送: can0  715   [8]  07 2E 02 02 12 34 56 78
[10:53:39.744] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.744] ❌ DID 0x0202: 负响应 NRC=0x13
[10:53:39.794] 测试写DID 0x0202, 数据: FF FF FF FF...
[10:53:39.794] 发送: can0  715   [8]  07 2E 02 02 FF FF FF FF
[10:53:39.794] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:39.794] ❌ DID 0x0202: 无效响应格式
[10:53:39.844] 测试写DID 0x0203, 数据: 00...
[10:53:39.844] 发送: can0  715   [8]  04 2E 02 03 00 55 55 55
[10:53:39.844] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.844] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:39.894] 测试写DID 0x0203, 数据: 01...
[10:53:39.894] 发送: can0  715   [8]  04 2E 02 03 01 55 55 55
[10:53:39.895] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.895] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:39.945] 测试写DID 0x0203, 数据: FF...
[10:53:39.945] 发送: can0  715   [8]  04 2E 02 03 FF 55 55 55
[10:53:39.945] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.945] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:39.995] 测试写DID 0x0203, 数据: 00 00...
[10:53:39.995] 发送: can0  715   [8]  05 2E 02 03 00 00 55 55
[10:53:39.995] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:39.995] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.045] 测试写DID 0x0203, 数据: 00 01...
[10:53:40.045] 发送: can0  715   [8]  05 2E 02 03 00 01 55 55
[10:53:40.045] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.045] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.096] 测试写DID 0x0203, 数据: FF FF...
[10:53:40.096] 发送: can0  715   [8]  05 2E 02 03 FF FF 55 55
[10:53:40.096] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.096] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.146] 测试写DID 0x0203, 数据: 12 34...
[10:53:40.146] 发送: can0  715   [8]  05 2E 02 03 12 34 55 55
[10:53:40.146] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.146] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.196] 测试写DID 0x0203, 数据: 00 00 00...
[10:53:40.196] 发送: can0  715   [8]  06 2E 02 03 00 00 00 55
[10:53:40.196] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.196] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.246] 测试写DID 0x0203, 数据: 01 02 03...
[10:53:40.247] 发送: can0  715   [8]  06 2E 02 03 01 02 03 55
[10:53:40.247] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.247] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.297] 测试写DID 0x0203, 数据: 00 00 00 00...
[10:53:40.297] 发送: can0  715   [8]  07 2E 02 03 00 00 00 00
[10:53:40.297] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.297] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.347] 测试写DID 0x0203, 数据: 12 34 56 78...
[10:53:40.347] 发送: can0  715   [8]  07 2E 02 03 12 34 56 78
[10:53:40.347] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:40.347] ❌ DID 0x0203: 无效响应格式
[10:53:40.397] 测试写DID 0x0203, 数据: FF FF FF FF...
[10:53:40.398] 发送: can0  715   [8]  07 2E 02 03 FF FF FF FF
[10:53:40.398] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.398] ❌ DID 0x0203: 负响应 NRC=0x13
[10:53:40.448] 测试写DID 0x0204, 数据: 00...
[10:53:40.448] 发送: can0  715   [8]  04 2E 02 04 00 55 55 55
[10:53:40.448] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.448] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.498] 测试写DID 0x0204, 数据: 01...
[10:53:40.498] 发送: can0  715   [8]  04 2E 02 04 01 55 55 55
[10:53:40.498] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.498] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.548] 测试写DID 0x0204, 数据: FF...
[10:53:40.548] 发送: can0  715   [8]  04 2E 02 04 FF 55 55 55
[10:53:40.549] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.549] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.599] 测试写DID 0x0204, 数据: 00 00...
[10:53:40.599] 发送: can0  715   [8]  05 2E 02 04 00 00 55 55
[10:53:40.599] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.599] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.649] 测试写DID 0x0204, 数据: 00 01...
[10:53:40.649] 发送: can0  715   [8]  05 2E 02 04 00 01 55 55
[10:53:40.649] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.649] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.699] 测试写DID 0x0204, 数据: FF FF...
[10:53:40.700] 发送: can0  715   [8]  05 2E 02 04 FF FF 55 55
[10:53:40.700] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.700] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.750] 测试写DID 0x0204, 数据: 12 34...
[10:53:40.750] 发送: can0  715   [8]  05 2E 02 04 12 34 55 55
[10:53:40.750] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.750] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.800] 测试写DID 0x0204, 数据: 00 00 00...
[10:53:40.800] 发送: can0  715   [8]  06 2E 02 04 00 00 00 55
[10:53:40.800] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.800] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.850] 测试写DID 0x0204, 数据: 01 02 03...
[10:53:40.851] 发送: can0  715   [8]  06 2E 02 04 01 02 03 55
[10:53:40.851] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.851] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:40.901] 测试写DID 0x0204, 数据: 00 00 00 00...
[10:53:40.901] 发送: can0  715   [8]  07 2E 02 04 00 00 00 00
[10:53:40.901] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:40.901] ❌ DID 0x0204: 无效响应格式
[10:53:40.951] 测试写DID 0x0204, 数据: 12 34 56 78...
[10:53:40.951] 发送: can0  715   [8]  07 2E 02 04 12 34 56 78
[10:53:40.951] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:40.951] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:41.002] 测试写DID 0x0204, 数据: FF FF FF FF...
[10:53:41.002] 发送: can0  715   [8]  07 2E 02 04 FF FF FF FF
[10:53:41.002] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.002] ❌ DID 0x0204: 负响应 NRC=0x13
[10:53:41.052] 测试写DID 0x1000, 数据: 00...
[10:53:41.052] 发送: can0  715   [8]  04 2E 10 00 00 55 55 55
[10:53:41.052] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.052] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.102] 测试写DID 0x1000, 数据: 01...
[10:53:41.102] 发送: can0  715   [8]  04 2E 10 00 01 55 55 55
[10:53:41.102] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.102] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.153] 测试写DID 0x1000, 数据: FF...
[10:53:41.153] 发送: can0  715   [8]  04 2E 10 00 FF 55 55 55
[10:53:41.153] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.153] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.203] 测试写DID 0x1000, 数据: 00 00...
[10:53:41.203] 发送: can0  715   [8]  05 2E 10 00 00 00 55 55
[10:53:41.203] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.203] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.253] 测试写DID 0x1000, 数据: 00 01...
[10:53:41.253] 发送: can0  715   [8]  05 2E 10 00 00 01 55 55
[10:53:41.253] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.253] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.303] 测试写DID 0x1000, 数据: FF FF...
[10:53:41.304] 发送: can0  715   [8]  05 2E 10 00 FF FF 55 55
[10:53:41.304] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.304] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.354] 测试写DID 0x1000, 数据: 12 34...
[10:53:41.354] 发送: can0  715   [8]  05 2E 10 00 12 34 55 55
[10:53:41.354] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.354] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.404] 测试写DID 0x1000, 数据: 00 00 00...
[10:53:41.404] 发送: can0  715   [8]  06 2E 10 00 00 00 00 55
[10:53:41.404] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.404] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.455] 测试写DID 0x1000, 数据: 01 02 03...
[10:53:41.455] 发送: can0  715   [8]  06 2E 10 00 01 02 03 55
[10:53:41.455] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:41.455] ❌ DID 0x1000: 无效响应格式
[10:53:41.505] 测试写DID 0x1000, 数据: 00 00 00 00...
[10:53:41.505] 发送: can0  715   [8]  07 2E 10 00 00 00 00 00
[10:53:41.505] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.505] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.555] 测试写DID 0x1000, 数据: 12 34 56 78...
[10:53:41.555] 发送: can0  715   [8]  07 2E 10 00 12 34 56 78
[10:53:41.555] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.555] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.606] 测试写DID 0x1000, 数据: FF FF FF FF...
[10:53:41.606] 发送: can0  715   [8]  07 2E 10 00 FF FF FF FF
[10:53:41.606] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.606] ❌ DID 0x1000: 负响应 NRC=0x13
[10:53:41.656] 测试写DID 0x1001, 数据: 00...
[10:53:41.656] 发送: can0  715   [8]  04 2E 10 01 00 55 55 55
[10:53:41.656] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.656] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.706] 测试写DID 0x1001, 数据: 01...
[10:53:41.706] 发送: can0  715   [8]  04 2E 10 01 01 55 55 55
[10:53:41.707] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.707] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.757] 测试写DID 0x1001, 数据: FF...
[10:53:41.757] 发送: can0  715   [8]  04 2E 10 01 FF 55 55 55
[10:53:41.757] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.757] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.807] 测试写DID 0x1001, 数据: 00 00...
[10:53:41.807] 发送: can0  715   [8]  05 2E 10 01 00 00 55 55
[10:53:41.807] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.807] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.857] 测试写DID 0x1001, 数据: 00 01...
[10:53:41.858] 发送: can0  715   [8]  05 2E 10 01 00 01 55 55
[10:53:41.858] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.858] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.908] 测试写DID 0x1001, 数据: FF FF...
[10:53:41.908] 发送: can0  715   [8]  05 2E 10 01 FF FF 55 55
[10:53:41.908] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.908] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:41.958] 测试写DID 0x1001, 数据: 12 34...
[10:53:41.958] 发送: can0  715   [8]  05 2E 10 01 12 34 55 55
[10:53:41.958] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:41.958] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:42.008] 测试写DID 0x1001, 数据: 00 00 00...
[10:53:42.008] 发送: can0  715   [8]  06 2E 10 01 00 00 00 55
[10:53:42.009] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:42.009] ❌ DID 0x1001: 无效响应格式
[10:53:42.059] 测试写DID 0x1001, 数据: 01 02 03...
[10:53:42.059] 发送: can0  715   [8]  06 2E 10 01 01 02 03 55
[10:53:42.059] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.059] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:42.109] 测试写DID 0x1001, 数据: 00 00 00 00...
[10:53:42.109] 发送: can0  715   [8]  07 2E 10 01 00 00 00 00
[10:53:42.109] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.109] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:42.159] 测试写DID 0x1001, 数据: 12 34 56 78...
[10:53:42.159] 发送: can0  715   [8]  07 2E 10 01 12 34 56 78
[10:53:42.160] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.160] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:42.210] 测试写DID 0x1001, 数据: FF FF FF FF...
[10:53:42.210] 发送: can0  715   [8]  07 2E 10 01 FF FF FF FF
[10:53:42.210] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.210] ❌ DID 0x1001: 负响应 NRC=0x13
[10:53:42.260] 测试写DID 0x1002, 数据: 00...
[10:53:42.260] 发送: can0  715   [8]  04 2E 10 02 00 55 55 55
[10:53:42.260] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.260] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.310] 测试写DID 0x1002, 数据: 01...
[10:53:42.311] 发送: can0  715   [8]  04 2E 10 02 01 55 55 55
[10:53:42.311] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.311] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.361] 测试写DID 0x1002, 数据: FF...
[10:53:42.361] 发送: can0  715   [8]  04 2E 10 02 FF 55 55 55
[10:53:42.361] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.361] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.411] 测试写DID 0x1002, 数据: 00 00...
[10:53:42.411] 发送: can0  715   [8]  05 2E 10 02 00 00 55 55
[10:53:42.411] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.411] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.461] 测试写DID 0x1002, 数据: 00 01...
[10:53:42.462] 发送: can0  715   [8]  05 2E 10 02 00 01 55 55
[10:53:42.462] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.462] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.512] 测试写DID 0x1002, 数据: FF FF...
[10:53:42.512] 发送: can0  715   [8]  05 2E 10 02 FF FF 55 55
[10:53:42.512] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:42.512] ❌ DID 0x1002: 无效响应格式
[10:53:42.562] 测试写DID 0x1002, 数据: 12 34...
[10:53:42.562] 发送: can0  715   [8]  05 2E 10 02 12 34 55 55
[10:53:42.562] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.562] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.612] 测试写DID 0x1002, 数据: 00 00 00...
[10:53:42.612] 发送: can0  715   [8]  06 2E 10 02 00 00 00 55
[10:53:42.612] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.612] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.663] 测试写DID 0x1002, 数据: 01 02 03...
[10:53:42.663] 发送: can0  715   [8]  06 2E 10 02 01 02 03 55
[10:53:42.663] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.663] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.713] 测试写DID 0x1002, 数据: 00 00 00 00...
[10:53:42.713] 发送: can0  715   [8]  07 2E 10 02 00 00 00 00
[10:53:42.713] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.713] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.763] 测试写DID 0x1002, 数据: 12 34 56 78...
[10:53:42.763] 发送: can0  715   [8]  07 2E 10 02 12 34 56 78
[10:53:42.763] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.763] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.813] 测试写DID 0x1002, 数据: FF FF FF FF...
[10:53:42.814] 发送: can0  715   [8]  07 2E 10 02 FF FF FF FF
[10:53:42.814] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.814] ❌ DID 0x1002: 负响应 NRC=0x13
[10:53:42.864] 测试写DID 0x1003, 数据: 00...
[10:53:42.864] 发送: can0  715   [8]  04 2E 10 03 00 55 55 55
[10:53:42.864] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.864] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:42.914] 测试写DID 0x1003, 数据: 01...
[10:53:42.914] 发送: can0  715   [8]  04 2E 10 03 01 55 55 55
[10:53:42.914] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.914] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:42.964] 测试写DID 0x1003, 数据: FF...
[10:53:42.964] 发送: can0  715   [8]  04 2E 10 03 FF 55 55 55
[10:53:42.964] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:42.965] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.015] 测试写DID 0x1003, 数据: 00 00...
[10:53:43.015] 发送: can0  715   [8]  05 2E 10 03 00 00 55 55
[10:53:43.015] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.015] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.065] 测试写DID 0x1003, 数据: 00 01...
[10:53:43.065] 发送: can0  715   [8]  05 2E 10 03 00 01 55 55
[10:53:43.065] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:43.065] ❌ DID 0x1003: 无效响应格式
[10:53:43.115] 测试写DID 0x1003, 数据: FF FF...
[10:53:43.115] 发送: can0  715   [8]  05 2E 10 03 FF FF 55 55
[10:53:43.116] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.116] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.166] 测试写DID 0x1003, 数据: 12 34...
[10:53:43.166] 发送: can0  715   [8]  05 2E 10 03 12 34 55 55
[10:53:43.166] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.166] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.216] 测试写DID 0x1003, 数据: 00 00 00...
[10:53:43.216] 发送: can0  715   [8]  06 2E 10 03 00 00 00 55
[10:53:43.216] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.216] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.267] 测试写DID 0x1003, 数据: 01 02 03...
[10:53:43.267] 发送: can0  715   [8]  06 2E 10 03 01 02 03 55
[10:53:43.267] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.267] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.317] 测试写DID 0x1003, 数据: 00 00 00 00...
[10:53:43.317] 发送: can0  715   [8]  07 2E 10 03 00 00 00 00
[10:53:43.317] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.317] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.367] 测试写DID 0x1003, 数据: 12 34 56 78...
[10:53:43.367] 发送: can0  715   [8]  07 2E 10 03 12 34 56 78
[10:53:43.367] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.367] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.418] 测试写DID 0x1003, 数据: FF FF FF FF...
[10:53:43.418] 发送: can0  715   [8]  07 2E 10 03 FF FF FF FF
[10:53:43.418] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.418] ❌ DID 0x1003: 负响应 NRC=0x13
[10:53:43.468] 测试写DID 0x1004, 数据: 00...
[10:53:43.468] 发送: can0  715   [8]  04 2E 10 04 00 55 55 55
[10:53:43.468] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.468] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.518] 测试写DID 0x1004, 数据: 01...
[10:53:43.518] 发送: can0  715   [8]  04 2E 10 04 01 55 55 55
[10:53:43.518] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.518] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.569] 测试写DID 0x1004, 数据: FF...
[10:53:43.569] 发送: can0  715   [8]  04 2E 10 04 FF 55 55 55
[10:53:43.569] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.569] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.619] 测试写DID 0x1004, 数据: 00 00...
[10:53:43.619] 发送: can0  715   [8]  05 2E 10 04 00 00 55 55
[10:53:43.619] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:43.619] ❌ DID 0x1004: 无效响应格式
[10:53:43.669] 测试写DID 0x1004, 数据: 00 01...
[10:53:43.669] 发送: can0  715   [8]  05 2E 10 04 00 01 55 55
[10:53:43.669] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.669] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.720] 测试写DID 0x1004, 数据: FF FF...
[10:53:43.720] 发送: can0  715   [8]  05 2E 10 04 FF FF 55 55
[10:53:43.720] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.720] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.770] 测试写DID 0x1004, 数据: 12 34...
[10:53:43.770] 发送: can0  715   [8]  05 2E 10 04 12 34 55 55
[10:53:43.770] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.770] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.820] 测试写DID 0x1004, 数据: 00 00 00...
[10:53:43.820] 发送: can0  715   [8]  06 2E 10 04 00 00 00 55
[10:53:43.820] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.820] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.871] 测试写DID 0x1004, 数据: 01 02 03...
[10:53:43.871] 发送: can0  715   [8]  06 2E 10 04 01 02 03 55
[10:53:43.871] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.871] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.921] 测试写DID 0x1004, 数据: 00 00 00 00...
[10:53:43.921] 发送: can0  715   [8]  07 2E 10 04 00 00 00 00
[10:53:43.921] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.921] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:43.971] 测试写DID 0x1004, 数据: 12 34 56 78...
[10:53:43.971] 发送: can0  715   [8]  07 2E 10 04 12 34 56 78
[10:53:43.971] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:43.971] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:44.021] 测试写DID 0x1004, 数据: FF FF FF FF...
[10:53:44.022] 发送: can0  715   [8]  07 2E 10 04 FF FF FF FF
[10:53:44.022] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.022] ❌ DID 0x1004: 负响应 NRC=0x13
[10:53:44.072] 测试写DID 0x1010, 数据: 00...
[10:53:44.072] 发送: can0  715   [8]  04 2E 10 10 00 55 55 55
[10:53:44.072] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.072] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.122] 测试写DID 0x1010, 数据: 01...
[10:53:44.122] 发送: can0  715   [8]  04 2E 10 10 01 55 55 55
[10:53:44.122] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.122] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.172] 测试写DID 0x1010, 数据: FF...
[10:53:44.172] 发送: can0  715   [8]  04 2E 10 10 FF 55 55 55
[10:53:44.173] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:44.173] ❌ DID 0x1010: 无效响应格式
[10:53:44.223] 测试写DID 0x1010, 数据: 00 00...
[10:53:44.223] 发送: can0  715   [8]  05 2E 10 10 00 00 55 55
[10:53:44.223] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.223] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.273] 测试写DID 0x1010, 数据: 00 01...
[10:53:44.273] 发送: can0  715   [8]  05 2E 10 10 00 01 55 55
[10:53:44.273] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.273] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.323] 测试写DID 0x1010, 数据: FF FF...
[10:53:44.323] 发送: can0  715   [8]  05 2E 10 10 FF FF 55 55
[10:53:44.323] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.323] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.374] 测试写DID 0x1010, 数据: 12 34...
[10:53:44.374] 发送: can0  715   [8]  05 2E 10 10 12 34 55 55
[10:53:44.374] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.374] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.424] 测试写DID 0x1010, 数据: 00 00 00...
[10:53:44.424] 发送: can0  715   [8]  06 2E 10 10 00 00 00 55
[10:53:44.424] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.424] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.474] 测试写DID 0x1010, 数据: 01 02 03...
[10:53:44.475] 发送: can0  715   [8]  06 2E 10 10 01 02 03 55
[10:53:44.475] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.475] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.525] 测试写DID 0x1010, 数据: 00 00 00 00...
[10:53:44.525] 发送: can0  715   [8]  07 2E 10 10 00 00 00 00
[10:53:44.525] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.525] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.575] 测试写DID 0x1010, 数据: 12 34 56 78...
[10:53:44.575] 发送: can0  715   [8]  07 2E 10 10 12 34 56 78
[10:53:44.575] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.575] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.625] 测试写DID 0x1010, 数据: FF FF FF FF...
[10:53:44.626] 发送: can0  715   [8]  07 2E 10 10 FF FF FF FF
[10:53:44.626] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.626] ❌ DID 0x1010: 负响应 NRC=0x13
[10:53:44.676] 测试写DID 0x1011, 数据: 00...
[10:53:44.676] 发送: can0  715   [8]  04 2E 10 11 00 55 55 55
[10:53:44.676] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.676] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:44.726] 测试写DID 0x1011, 数据: 01...
[10:53:44.726] 发送: can0  715   [8]  04 2E 10 11 01 55 55 55
[10:53:44.726] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:44.726] ❌ DID 0x1011: 无效响应格式
[10:53:44.777] 测试写DID 0x1011, 数据: FF...
[10:53:44.777] 发送: can0  715   [8]  04 2E 10 11 FF 55 55 55
[10:53:44.777] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.777] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:44.827] 测试写DID 0x1011, 数据: 00 00...
[10:53:44.827] 发送: can0  715   [8]  05 2E 10 11 00 00 55 55
[10:53:44.827] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.827] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:44.877] 测试写DID 0x1011, 数据: 00 01...
[10:53:44.877] 发送: can0  715   [8]  05 2E 10 11 00 01 55 55
[10:53:44.877] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.877] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:44.928] 测试写DID 0x1011, 数据: FF FF...
[10:53:44.928] 发送: can0  715   [8]  05 2E 10 11 FF FF 55 55
[10:53:44.928] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.928] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:44.978] 测试写DID 0x1011, 数据: 12 34...
[10:53:44.978] 发送: can0  715   [8]  05 2E 10 11 12 34 55 55
[10:53:44.978] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:44.978] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.028] 测试写DID 0x1011, 数据: 00 00 00...
[10:53:45.028] 发送: can0  715   [8]  06 2E 10 11 00 00 00 55
[10:53:45.028] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.028] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.079] 测试写DID 0x1011, 数据: 01 02 03...
[10:53:45.079] 发送: can0  715   [8]  06 2E 10 11 01 02 03 55
[10:53:45.079] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.079] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.129] 测试写DID 0x1011, 数据: 00 00 00 00...
[10:53:45.129] 发送: can0  715   [8]  07 2E 10 11 00 00 00 00
[10:53:45.129] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.129] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.179] 测试写DID 0x1011, 数据: 12 34 56 78...
[10:53:45.179] 发送: can0  715   [8]  07 2E 10 11 12 34 56 78
[10:53:45.179] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.179] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.229] 测试写DID 0x1011, 数据: FF FF FF FF...
[10:53:45.230] 发送: can0  715   [8]  07 2E 10 11 FF FF FF FF
[10:53:45.230] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.230] ❌ DID 0x1011: 负响应 NRC=0x13
[10:53:45.280] 测试写DID 0x1012, 数据: 00...
[10:53:45.280] 发送: can0  715   [8]  04 2E 10 12 00 55 55 55
[10:53:45.280] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:45.280] ❌ DID 0x1012: 无效响应格式
[10:53:45.330] 测试写DID 0x1012, 数据: 01...
[10:53:45.330] 发送: can0  715   [8]  04 2E 10 12 01 55 55 55
[10:53:45.330] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.330] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.381] 测试写DID 0x1012, 数据: FF...
[10:53:45.381] 发送: can0  715   [8]  04 2E 10 12 FF 55 55 55
[10:53:45.381] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.381] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.431] 测试写DID 0x1012, 数据: 00 00...
[10:53:45.431] 发送: can0  715   [8]  05 2E 10 12 00 00 55 55
[10:53:45.431] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.431] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.481] 测试写DID 0x1012, 数据: 00 01...
[10:53:45.481] 发送: can0  715   [8]  05 2E 10 12 00 01 55 55
[10:53:45.481] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.481] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.532] 测试写DID 0x1012, 数据: FF FF...
[10:53:45.532] 发送: can0  715   [8]  05 2E 10 12 FF FF 55 55
[10:53:45.532] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.532] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.582] 测试写DID 0x1012, 数据: 12 34...
[10:53:45.582] 发送: can0  715   [8]  05 2E 10 12 12 34 55 55
[10:53:45.582] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.582] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.632] 测试写DID 0x1012, 数据: 00 00 00...
[10:53:45.632] 发送: can0  715   [8]  06 2E 10 12 00 00 00 55
[10:53:45.632] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.632] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.683] 测试写DID 0x1012, 数据: 01 02 03...
[10:53:45.683] 发送: can0  715   [8]  06 2E 10 12 01 02 03 55
[10:53:45.683] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.683] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.733] 测试写DID 0x1012, 数据: 00 00 00 00...
[10:53:45.733] 发送: can0  715   [8]  07 2E 10 12 00 00 00 00
[10:53:45.733] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.733] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.783] 测试写DID 0x1012, 数据: 12 34 56 78...
[10:53:45.783] 发送: can0  715   [8]  07 2E 10 12 12 34 56 78
[10:53:45.784] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.784] ❌ DID 0x1012: 负响应 NRC=0x13
[10:53:45.834] 测试写DID 0x1012, 数据: FF FF FF FF...
[10:53:45.834] 发送: can0  715   [8]  07 2E 10 12 FF FF FF FF
[10:53:45.834] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:45.834] ❌ DID 0x1012: 无效响应格式
[10:53:45.884] 测试写DID 0x1013, 数据: 00...
[10:53:45.884] 发送: can0  715   [8]  04 2E 10 13 00 55 55 55
[10:53:45.884] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.884] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:45.934] 测试写DID 0x1013, 数据: 01...
[10:53:45.934] 发送: can0  715   [8]  04 2E 10 13 01 55 55 55
[10:53:45.934] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.935] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:45.985] 测试写DID 0x1013, 数据: FF...
[10:53:45.985] 发送: can0  715   [8]  04 2E 10 13 FF 55 55 55
[10:53:45.985] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:45.985] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.035] 测试写DID 0x1013, 数据: 00 00...
[10:53:46.035] 发送: can0  715   [8]  05 2E 10 13 00 00 55 55
[10:53:46.035] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.035] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.085] 测试写DID 0x1013, 数据: 00 01...
[10:53:46.086] 发送: can0  715   [8]  05 2E 10 13 00 01 55 55
[10:53:46.086] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.086] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.136] 测试写DID 0x1013, 数据: FF FF...
[10:53:46.136] 发送: can0  715   [8]  05 2E 10 13 FF FF 55 55
[10:53:46.136] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.136] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.186] 测试写DID 0x1013, 数据: 12 34...
[10:53:46.186] 发送: can0  715   [8]  05 2E 10 13 12 34 55 55
[10:53:46.186] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.186] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.236] 测试写DID 0x1013, 数据: 00 00 00...
[10:53:46.237] 发送: can0  715   [8]  06 2E 10 13 00 00 00 55
[10:53:46.237] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.237] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.287] 测试写DID 0x1013, 数据: 01 02 03...
[10:53:46.287] 发送: can0  715   [8]  06 2E 10 13 01 02 03 55
[10:53:46.287] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.287] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.337] 测试写DID 0x1013, 数据: 00 00 00 00...
[10:53:46.337] 发送: can0  715   [8]  07 2E 10 13 00 00 00 00
[10:53:46.337] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.337] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.387] 测试写DID 0x1013, 数据: 12 34 56 78...
[10:53:46.388] 发送: can0  715   [8]  07 2E 10 13 12 34 56 78
[10:53:46.388] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:46.388] ❌ DID 0x1013: 无效响应格式
[10:53:46.438] 测试写DID 0x1013, 数据: FF FF FF FF...
[10:53:46.438] 发送: can0  715   [8]  07 2E 10 13 FF FF FF FF
[10:53:46.438] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.438] ❌ DID 0x1013: 负响应 NRC=0x13
[10:53:46.488] 测试写DID 0x1014, 数据: 00...
[10:53:46.488] 发送: can0  715   [8]  04 2E 10 14 00 55 55 55
[10:53:46.488] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.488] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.538] 测试写DID 0x1014, 数据: 01...
[10:53:46.539] 发送: can0  715   [8]  04 2E 10 14 01 55 55 55
[10:53:46.539] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.539] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.589] 测试写DID 0x1014, 数据: FF...
[10:53:46.589] 发送: can0  715   [8]  04 2E 10 14 FF 55 55 55
[10:53:46.589] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.589] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.639] 测试写DID 0x1014, 数据: 00 00...
[10:53:46.639] 发送: can0  715   [8]  05 2E 10 14 00 00 55 55
[10:53:46.639] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.639] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.689] 测试写DID 0x1014, 数据: 00 01...
[10:53:46.690] 发送: can0  715   [8]  05 2E 10 14 00 01 55 55
[10:53:46.690] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.690] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.740] 测试写DID 0x1014, 数据: FF FF...
[10:53:46.740] 发送: can0  715   [8]  05 2E 10 14 FF FF 55 55
[10:53:46.740] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.740] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.790] 测试写DID 0x1014, 数据: 12 34...
[10:53:46.790] 发送: can0  715   [8]  05 2E 10 14 12 34 55 55
[10:53:46.790] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.790] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.840] 测试写DID 0x1014, 数据: 00 00 00...
[10:53:46.840] 发送: can0  715   [8]  06 2E 10 14 00 00 00 55
[10:53:46.841] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.841] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.891] 测试写DID 0x1014, 数据: 01 02 03...
[10:53:46.891] 发送: can0  715   [8]  06 2E 10 14 01 02 03 55
[10:53:46.891] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.891] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:46.941] 测试写DID 0x1014, 数据: 00 00 00 00...
[10:53:46.941] 发送: can0  715   [8]  07 2E 10 14 00 00 00 00
[10:53:46.941] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:46.941] ❌ DID 0x1014: 无效响应格式
[10:53:46.991] 测试写DID 0x1014, 数据: 12 34 56 78...
[10:53:46.992] 发送: can0  715   [8]  07 2E 10 14 12 34 56 78
[10:53:46.992] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:46.992] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:47.042] 测试写DID 0x1014, 数据: FF FF FF FF...
[10:53:47.042] 发送: can0  715   [8]  07 2E 10 14 FF FF FF FF
[10:53:47.042] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.042] ❌ DID 0x1014: 负响应 NRC=0x13
[10:53:47.092] 测试写DID 0x1100, 数据: 00...
[10:53:47.092] 发送: can0  715   [8]  04 2E 11 00 00 55 55 55
[10:53:47.092] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.092] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.143] 测试写DID 0x1100, 数据: 01...
[10:53:47.143] 发送: can0  715   [8]  04 2E 11 00 01 55 55 55
[10:53:47.143] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.143] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.193] 测试写DID 0x1100, 数据: FF...
[10:53:47.193] 发送: can0  715   [8]  04 2E 11 00 FF 55 55 55
[10:53:47.193] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.193] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.243] 测试写DID 0x1100, 数据: 00 00...
[10:53:47.243] 发送: can0  715   [8]  05 2E 11 00 00 00 55 55
[10:53:47.243] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.244] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.294] 测试写DID 0x1100, 数据: 00 01...
[10:53:47.294] 发送: can0  715   [8]  05 2E 11 00 00 01 55 55
[10:53:47.294] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.294] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.344] 测试写DID 0x1100, 数据: FF FF...
[10:53:47.344] 发送: can0  715   [8]  05 2E 11 00 FF FF 55 55
[10:53:47.344] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.344] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.394] 测试写DID 0x1100, 数据: 12 34...
[10:53:47.394] 发送: can0  715   [8]  05 2E 11 00 12 34 55 55
[10:53:47.394] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.394] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.444] 测试写DID 0x1100, 数据: 00 00 00...
[10:53:47.445] 发送: can0  715   [8]  06 2E 11 00 00 00 00 55
[10:53:47.445] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.445] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.495] 测试写DID 0x1100, 数据: 01 02 03...
[10:53:47.495] 发送: can0  715   [8]  06 2E 11 00 01 02 03 55
[10:53:47.495] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:47.495] ❌ DID 0x1100: 无效响应格式
[10:53:47.545] 测试写DID 0x1100, 数据: 00 00 00 00...
[10:53:47.545] 发送: can0  715   [8]  07 2E 11 00 00 00 00 00
[10:53:47.545] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.545] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.595] 测试写DID 0x1100, 数据: 12 34 56 78...
[10:53:47.596] 发送: can0  715   [8]  07 2E 11 00 12 34 56 78
[10:53:47.596] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.596] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.646] 测试写DID 0x1100, 数据: FF FF FF FF...
[10:53:47.646] 发送: can0  715   [8]  07 2E 11 00 FF FF FF FF
[10:53:47.646] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.646] ❌ DID 0x1100: 负响应 NRC=0x13
[10:53:47.696] 测试写DID 0x1101, 数据: 00...
[10:53:47.696] 发送: can0  715   [8]  04 2E 11 01 00 55 55 55
[10:53:47.696] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.696] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.746] 测试写DID 0x1101, 数据: 01...
[10:53:47.746] 发送: can0  715   [8]  04 2E 11 01 01 55 55 55
[10:53:47.746] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.746] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.797] 测试写DID 0x1101, 数据: FF...
[10:53:47.797] 发送: can0  715   [8]  04 2E 11 01 FF 55 55 55
[10:53:47.797] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.797] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.847] 测试写DID 0x1101, 数据: 00 00...
[10:53:47.847] 发送: can0  715   [8]  05 2E 11 01 00 00 55 55
[10:53:47.847] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.847] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.897] 测试写DID 0x1101, 数据: 00 01...
[10:53:47.897] 发送: can0  715   [8]  05 2E 11 01 00 01 55 55
[10:53:47.897] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.897] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.948] 测试写DID 0x1101, 数据: FF FF...
[10:53:47.948] 发送: can0  715   [8]  05 2E 11 01 FF FF 55 55
[10:53:47.948] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.948] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:47.998] 测试写DID 0x1101, 数据: 12 34...
[10:53:47.998] 发送: can0  715   [8]  05 2E 11 01 12 34 55 55
[10:53:47.998] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:47.998] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:48.048] 测试写DID 0x1101, 数据: 00 00 00...
[10:53:48.048] 发送: can0  715   [8]  06 2E 11 01 00 00 00 55
[10:53:48.048] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:48.048] ❌ DID 0x1101: 无效响应格式
[10:53:48.099] 测试写DID 0x1101, 数据: 01 02 03...
[10:53:48.099] 发送: can0  715   [8]  06 2E 11 01 01 02 03 55
[10:53:48.099] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.099] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:48.149] 测试写DID 0x1101, 数据: 00 00 00 00...
[10:53:48.149] 发送: can0  715   [8]  07 2E 11 01 00 00 00 00
[10:53:48.149] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.149] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:48.199] 测试写DID 0x1101, 数据: 12 34 56 78...
[10:53:48.200] 发送: can0  715   [8]  07 2E 11 01 12 34 56 78
[10:53:48.200] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.200] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:48.250] 测试写DID 0x1101, 数据: FF FF FF FF...
[10:53:48.250] 发送: can0  715   [8]  07 2E 11 01 FF FF FF FF
[10:53:48.253] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.253] ❌ DID 0x1101: 负响应 NRC=0x13
[10:53:48.303] 测试写DID 0x1102, 数据: 00...
[10:53:48.303] 发送: can0  715   [8]  04 2E 11 02 00 55 55 55
[10:53:48.303] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.303] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.354] 测试写DID 0x1102, 数据: 01...
[10:53:48.354] 发送: can0  715   [8]  04 2E 11 02 01 55 55 55
[10:53:48.354] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.354] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.404] 测试写DID 0x1102, 数据: FF...
[10:53:48.404] 发送: can0  715   [8]  04 2E 11 02 FF 55 55 55
[10:53:48.404] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.404] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.454] 测试写DID 0x1102, 数据: 00 00...
[10:53:48.454] 发送: can0  715   [8]  05 2E 11 02 00 00 55 55
[10:53:48.455] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.455] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.505] 测试写DID 0x1102, 数据: 00 01...
[10:53:48.505] 发送: can0  715   [8]  05 2E 11 02 00 01 55 55
[10:53:48.505] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.505] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.555] 测试写DID 0x1102, 数据: FF FF...
[10:53:48.555] 发送: can0  715   [8]  05 2E 11 02 FF FF 55 55
[10:53:48.555] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.555] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.605] 测试写DID 0x1102, 数据: 12 34...
[10:53:48.605] 发送: can0  715   [8]  05 2E 11 02 12 34 55 55
[10:53:48.605] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:48.605] ❌ DID 0x1102: 无效响应格式
[10:53:48.656] 测试写DID 0x1102, 数据: 00 00 00...
[10:53:48.656] 发送: can0  715   [8]  06 2E 11 02 00 00 00 55
[10:53:48.656] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.656] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.706] 测试写DID 0x1102, 数据: 01 02 03...
[10:53:48.706] 发送: can0  715   [8]  06 2E 11 02 01 02 03 55
[10:53:48.706] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.706] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.756] 测试写DID 0x1102, 数据: 00 00 00 00...
[10:53:48.756] 发送: can0  715   [8]  07 2E 11 02 00 00 00 00
[10:53:48.756] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.756] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.806] 测试写DID 0x1102, 数据: 12 34 56 78...
[10:53:48.807] 发送: can0  715   [8]  07 2E 11 02 12 34 56 78
[10:53:48.807] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.807] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.857] 测试写DID 0x1102, 数据: FF FF FF FF...
[10:53:48.857] 发送: can0  715   [8]  07 2E 11 02 FF FF FF FF
[10:53:48.857] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.857] ❌ DID 0x1102: 负响应 NRC=0x13
[10:53:48.907] 测试写DID 0x1103, 数据: 00...
[10:53:48.907] 发送: can0  715   [8]  04 2E 11 03 00 55 55 55
[10:53:48.907] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.907] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:48.958] 测试写DID 0x1103, 数据: 01...
[10:53:48.958] 发送: can0  715   [8]  04 2E 11 03 01 55 55 55
[10:53:48.958] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:48.958] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.008] 测试写DID 0x1103, 数据: FF...
[10:53:49.008] 发送: can0  715   [8]  04 2E 11 03 FF 55 55 55
[10:53:49.008] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.008] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.058] 测试写DID 0x1103, 数据: 00 00...
[10:53:49.059] 发送: can0  715   [8]  05 2E 11 03 00 00 55 55
[10:53:49.059] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.059] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.109] 测试写DID 0x1103, 数据: 00 01...
[10:53:49.109] 发送: can0  715   [8]  05 2E 11 03 00 01 55 55
[10:53:49.109] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.109] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.159] 测试写DID 0x1103, 数据: FF FF...
[10:53:49.159] 发送: can0  715   [8]  05 2E 11 03 FF FF 55 55
[10:53:49.159] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:49.159] ❌ DID 0x1103: 无效响应格式
[10:53:49.209] 测试写DID 0x1103, 数据: 12 34...
[10:53:49.210] 发送: can0  715   [8]  05 2E 11 03 12 34 55 55
[10:53:49.210] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.210] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.260] 测试写DID 0x1103, 数据: 00 00 00...
[10:53:49.260] 发送: can0  715   [8]  06 2E 11 03 00 00 00 55
[10:53:49.260] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.260] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.310] 测试写DID 0x1103, 数据: 01 02 03...
[10:53:49.310] 发送: can0  715   [8]  06 2E 11 03 01 02 03 55
[10:53:49.310] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.310] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.360] 测试写DID 0x1103, 数据: 00 00 00 00...
[10:53:49.361] 发送: can0  715   [8]  07 2E 11 03 00 00 00 00
[10:53:49.361] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.361] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.411] 测试写DID 0x1103, 数据: 12 34 56 78...
[10:53:49.411] 发送: can0  715   [8]  07 2E 11 03 12 34 56 78
[10:53:49.411] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.411] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.461] 测试写DID 0x1103, 数据: FF FF FF FF...
[10:53:49.461] 发送: can0  715   [8]  07 2E 11 03 FF FF FF FF
[10:53:49.461] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.461] ❌ DID 0x1103: 负响应 NRC=0x13
[10:53:49.511] 测试写DID 0x1104, 数据: 00...
[10:53:49.512] 发送: can0  715   [8]  04 2E 11 04 00 55 55 55
[10:53:49.512] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.512] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.562] 测试写DID 0x1104, 数据: 01...
[10:53:49.562] 发送: can0  715   [8]  04 2E 11 04 01 55 55 55
[10:53:49.562] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.562] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.612] 测试写DID 0x1104, 数据: FF...
[10:53:49.612] 发送: can0  715   [8]  04 2E 11 04 FF 55 55 55
[10:53:49.612] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.612] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.662] 测试写DID 0x1104, 数据: 00 00...
[10:53:49.663] 发送: can0  715   [8]  05 2E 11 04 00 00 55 55
[10:53:49.663] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.663] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.713] 测试写DID 0x1104, 数据: 00 01...
[10:53:49.713] 发送: can0  715   [8]  05 2E 11 04 00 01 55 55
[10:53:49.713] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:49.713] ❌ DID 0x1104: 无效响应格式
[10:53:49.763] 测试写DID 0x1104, 数据: FF FF...
[10:53:49.763] 发送: can0  715   [8]  05 2E 11 04 FF FF 55 55
[10:53:49.763] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.763] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.813] 测试写DID 0x1104, 数据: 12 34...
[10:53:49.814] 发送: can0  715   [8]  05 2E 11 04 12 34 55 55
[10:53:49.814] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.814] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.864] 测试写DID 0x1104, 数据: 00 00 00...
[10:53:49.864] 发送: can0  715   [8]  06 2E 11 04 00 00 00 55
[10:53:49.864] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.864] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.914] 测试写DID 0x1104, 数据: 01 02 03...
[10:53:49.914] 发送: can0  715   [8]  06 2E 11 04 01 02 03 55
[10:53:49.914] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.914] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:49.964] 测试写DID 0x1104, 数据: 00 00 00 00...
[10:53:49.964] 发送: can0  715   [8]  07 2E 11 04 00 00 00 00
[10:53:49.964] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:49.964] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:50.015] 测试写DID 0x1104, 数据: 12 34 56 78...
[10:53:50.015] 发送: can0  715   [8]  07 2E 11 04 12 34 56 78
[10:53:50.015] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.015] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:50.065] 测试写DID 0x1104, 数据: FF FF FF FF...
[10:53:50.065] 发送: can0  715   [8]  07 2E 11 04 FF FF FF FF
[10:53:50.065] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.065] ❌ DID 0x1104: 负响应 NRC=0x13
[10:53:50.115] 测试写DID 0x2000, 数据: 00...
[10:53:50.115] 发送: can0  715   [8]  04 2E 20 00 00 55 55 55
[10:53:50.115] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.116] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.166] 测试写DID 0x2000, 数据: 01...
[10:53:50.166] 发送: can0  715   [8]  04 2E 20 00 01 55 55 55
[10:53:50.166] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.166] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.216] 测试写DID 0x2000, 数据: FF...
[10:53:50.216] 发送: can0  715   [8]  04 2E 20 00 FF 55 55 55
[10:53:50.216] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.216] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.266] 测试写DID 0x2000, 数据: 00 00...
[10:53:50.266] 发送: can0  715   [8]  05 2E 20 00 00 00 55 55
[10:53:50.267] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:50.267] ❌ DID 0x2000: 无效响应格式
[10:53:50.317] 测试写DID 0x2000, 数据: 00 01...
[10:53:50.317] 发送: can0  715   [8]  05 2E 20 00 00 01 55 55
[10:53:50.317] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.317] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.367] 测试写DID 0x2000, 数据: FF FF...
[10:53:50.367] 发送: can0  715   [8]  05 2E 20 00 FF FF 55 55
[10:53:50.367] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.367] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.417] 测试写DID 0x2000, 数据: 12 34...
[10:53:50.418] 发送: can0  715   [8]  05 2E 20 00 12 34 55 55
[10:53:50.418] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.418] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.468] 测试写DID 0x2000, 数据: 00 00 00...
[10:53:50.468] 发送: can0  715   [8]  06 2E 20 00 00 00 00 55
[10:53:50.468] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.468] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.518] 测试写DID 0x2000, 数据: 01 02 03...
[10:53:50.518] 发送: can0  715   [8]  06 2E 20 00 01 02 03 55
[10:53:50.518] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.518] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.569] 测试写DID 0x2000, 数据: 00 00 00 00...
[10:53:50.569] 发送: can0  715   [8]  07 2E 20 00 00 00 00 00
[10:53:50.569] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.569] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.619] 测试写DID 0x2000, 数据: 12 34 56 78...
[10:53:50.619] 发送: can0  715   [8]  07 2E 20 00 12 34 56 78
[10:53:50.619] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.619] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.669] 测试写DID 0x2000, 数据: FF FF FF FF...
[10:53:50.669] 发送: can0  715   [8]  07 2E 20 00 FF FF FF FF
[10:53:50.669] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.669] ❌ DID 0x2000: 负响应 NRC=0x13
[10:53:50.719] 测试写DID 0x2001, 数据: 00...
[10:53:50.720] 发送: can0  715   [8]  04 2E 20 01 00 55 55 55
[10:53:50.720] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.720] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:50.770] 测试写DID 0x2001, 数据: 01...
[10:53:50.770] 发送: can0  715   [8]  04 2E 20 01 01 55 55 55
[10:53:50.770] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:50.770] ❌ DID 0x2001: 无效响应格式
[10:53:50.820] 测试写DID 0x2001, 数据: FF...
[10:53:50.820] 发送: can0  715   [8]  04 2E 20 01 FF 55 55 55
[10:53:50.820] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.820] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:50.870] 测试写DID 0x2001, 数据: 00 00...
[10:53:50.871] 发送: can0  715   [8]  05 2E 20 01 00 00 55 55
[10:53:50.871] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.871] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:50.921] 测试写DID 0x2001, 数据: 00 01...
[10:53:50.921] 发送: can0  715   [8]  05 2E 20 01 00 01 55 55
[10:53:50.921] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.921] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:50.971] 测试写DID 0x2001, 数据: FF FF...
[10:53:50.971] 发送: can0  715   [8]  05 2E 20 01 FF FF 55 55
[10:53:50.971] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:50.971] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.021] 测试写DID 0x2001, 数据: 12 34...
[10:53:51.021] 发送: can0  715   [8]  05 2E 20 01 12 34 55 55
[10:53:51.021] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.021] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.072] 测试写DID 0x2001, 数据: 00 00 00...
[10:53:51.072] 发送: can0  715   [8]  06 2E 20 01 00 00 00 55
[10:53:51.072] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.072] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.122] 测试写DID 0x2001, 数据: 01 02 03...
[10:53:51.122] 发送: can0  715   [8]  06 2E 20 01 01 02 03 55
[10:53:51.122] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.122] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.172] 测试写DID 0x2001, 数据: 00 00 00 00...
[10:53:51.172] 发送: can0  715   [8]  07 2E 20 01 00 00 00 00
[10:53:51.172] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.172] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.223] 测试写DID 0x2001, 数据: 12 34 56 78...
[10:53:51.223] 发送: can0  715   [8]  07 2E 20 01 12 34 56 78
[10:53:51.223] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.223] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.273] 测试写DID 0x2001, 数据: FF FF FF FF...
[10:53:51.273] 发送: can0  715   [8]  07 2E 20 01 FF FF FF FF
[10:53:51.273] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.273] ❌ DID 0x2001: 负响应 NRC=0x13
[10:53:51.323] 测试写DID 0x2002, 数据: 00...
[10:53:51.323] 发送: can0  715   [8]  04 2E 20 02 00 55 55 55
[10:53:51.323] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:51.323] ❌ DID 0x2002: 无效响应格式
[10:53:51.373] 测试写DID 0x2002, 数据: 01...
[10:53:51.374] 发送: can0  715   [8]  04 2E 20 02 01 55 55 55
[10:53:51.374] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.374] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.424] 测试写DID 0x2002, 数据: FF...
[10:53:51.424] 发送: can0  715   [8]  04 2E 20 02 FF 55 55 55
[10:53:51.424] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.424] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.474] 测试写DID 0x2002, 数据: 00 00...
[10:53:51.474] 发送: can0  715   [8]  05 2E 20 02 00 00 55 55
[10:53:51.474] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.474] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.525] 测试写DID 0x2002, 数据: 00 01...
[10:53:51.525] 发送: can0  715   [8]  05 2E 20 02 00 01 55 55
[10:53:51.525] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.525] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.575] 测试写DID 0x2002, 数据: FF FF...
[10:53:51.575] 发送: can0  715   [8]  05 2E 20 02 FF FF 55 55
[10:53:51.575] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.575] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.625] 测试写DID 0x2002, 数据: 12 34...
[10:53:51.625] 发送: can0  715   [8]  05 2E 20 02 12 34 55 55
[10:53:51.625] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.625] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.675] 测试写DID 0x2002, 数据: 00 00 00...
[10:53:51.676] 发送: can0  715   [8]  06 2E 20 02 00 00 00 55
[10:53:51.676] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.676] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.726] 测试写DID 0x2002, 数据: 01 02 03...
[10:53:51.726] 发送: can0  715   [8]  06 2E 20 02 01 02 03 55
[10:53:51.726] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.726] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.776] 测试写DID 0x2002, 数据: 00 00 00 00...
[10:53:51.776] 发送: can0  715   [8]  07 2E 20 02 00 00 00 00
[10:53:51.776] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.776] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.826] 测试写DID 0x2002, 数据: 12 34 56 78...
[10:53:51.827] 发送: can0  715   [8]  07 2E 20 02 12 34 56 78
[10:53:51.827] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.827] ❌ DID 0x2002: 负响应 NRC=0x13
[10:53:51.877] 测试写DID 0x2002, 数据: FF FF FF FF...
[10:53:51.877] 发送: can0  715   [8]  07 2E 20 02 FF FF FF FF
[10:53:51.877] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:51.877] ❌ DID 0x2002: 无效响应格式
[10:53:51.927] 测试写DID 0x2003, 数据: 00...
[10:53:51.927] 发送: can0  715   [8]  04 2E 20 03 00 55 55 55
[10:53:51.927] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.928] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:51.978] 测试写DID 0x2003, 数据: 01...
[10:53:51.978] 发送: can0  715   [8]  04 2E 20 03 01 55 55 55
[10:53:51.978] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:51.978] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.028] 测试写DID 0x2003, 数据: FF...
[10:53:52.028] 发送: can0  715   [8]  04 2E 20 03 FF 55 55 55
[10:53:52.028] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.028] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.078] 测试写DID 0x2003, 数据: 00 00...
[10:53:52.078] 发送: can0  715   [8]  05 2E 20 03 00 00 55 55
[10:53:52.078] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.078] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.129] 测试写DID 0x2003, 数据: 00 01...
[10:53:52.129] 发送: can0  715   [8]  05 2E 20 03 00 01 55 55
[10:53:52.129] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.129] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.179] 测试写DID 0x2003, 数据: FF FF...
[10:53:52.179] 发送: can0  715   [8]  05 2E 20 03 FF FF 55 55
[10:53:52.179] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.179] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.229] 测试写DID 0x2003, 数据: 12 34...
[10:53:52.229] 发送: can0  715   [8]  05 2E 20 03 12 34 55 55
[10:53:52.229] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.229] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.279] 测试写DID 0x2003, 数据: 00 00 00...
[10:53:52.280] 发送: can0  715   [8]  06 2E 20 03 00 00 00 55
[10:53:52.280] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.280] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.330] 测试写DID 0x2003, 数据: 01 02 03...
[10:53:52.330] 发送: can0  715   [8]  06 2E 20 03 01 02 03 55
[10:53:52.330] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.330] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.380] 测试写DID 0x2003, 数据: 00 00 00 00...
[10:53:52.380] 发送: can0  715   [8]  07 2E 20 03 00 00 00 00
[10:53:52.380] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.380] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.430] 测试写DID 0x2003, 数据: 12 34 56 78...
[10:53:52.431] 发送: can0  715   [8]  07 2E 20 03 12 34 56 78
[10:53:52.431] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:52.431] ❌ DID 0x2003: 无效响应格式
[10:53:52.481] 测试写DID 0x2003, 数据: FF FF FF FF...
[10:53:52.481] 发送: can0  715   [8]  07 2E 20 03 FF FF FF FF
[10:53:52.481] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.481] ❌ DID 0x2003: 负响应 NRC=0x13
[10:53:52.531] 测试写DID 0x2004, 数据: 00...
[10:53:52.531] 发送: can0  715   [8]  04 2E 20 04 00 55 55 55
[10:53:52.531] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.531] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.581] 测试写DID 0x2004, 数据: 01...
[10:53:52.582] 发送: can0  715   [8]  04 2E 20 04 01 55 55 55
[10:53:52.582] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.582] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.632] 测试写DID 0x2004, 数据: FF...
[10:53:52.632] 发送: can0  715   [8]  04 2E 20 04 FF 55 55 55
[10:53:52.632] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.632] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.682] 测试写DID 0x2004, 数据: 00 00...
[10:53:52.682] 发送: can0  715   [8]  05 2E 20 04 00 00 55 55
[10:53:52.683] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.683] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.733] 测试写DID 0x2004, 数据: 00 01...
[10:53:52.733] 发送: can0  715   [8]  05 2E 20 04 00 01 55 55
[10:53:52.733] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.733] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.783] 测试写DID 0x2004, 数据: FF FF...
[10:53:52.783] 发送: can0  715   [8]  05 2E 20 04 FF FF 55 55
[10:53:52.783] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.783] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.834] 测试写DID 0x2004, 数据: 12 34...
[10:53:52.834] 发送: can0  715   [8]  05 2E 20 04 12 34 55 55
[10:53:52.834] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.834] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.884] 测试写DID 0x2004, 数据: 00 00 00...
[10:53:52.884] 发送: can0  715   [8]  06 2E 20 04 00 00 00 55
[10:53:52.884] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.884] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.934] 测试写DID 0x2004, 数据: 01 02 03...
[10:53:52.934] 发送: can0  715   [8]  06 2E 20 04 01 02 03 55
[10:53:52.934] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:52.934] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:52.985] 测试写DID 0x2004, 数据: 00 00 00 00...
[10:53:52.985] 发送: can0  715   [8]  07 2E 20 04 00 00 00 00
[10:53:52.985] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:52.985] ❌ DID 0x2004: 无效响应格式
[10:53:53.035] 测试写DID 0x2004, 数据: 12 34 56 78...
[10:53:53.035] 发送: can0  715   [8]  07 2E 20 04 12 34 56 78
[10:53:53.035] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.035] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:53.085] 测试写DID 0x2004, 数据: FF FF FF FF...
[10:53:53.085] 发送: can0  715   [8]  07 2E 20 04 FF FF FF FF
[10:53:53.085] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.085] ❌ DID 0x2004: 负响应 NRC=0x13
[10:53:53.136] 测试写DID 0x2010, 数据: 00...
[10:53:53.136] 发送: can0  715   [8]  04 2E 20 10 00 55 55 55
[10:53:53.136] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.136] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.186] 测试写DID 0x2010, 数据: 01...
[10:53:53.186] 发送: can0  715   [8]  04 2E 20 10 01 55 55 55
[10:53:53.186] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.186] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.236] 测试写DID 0x2010, 数据: FF...
[10:53:53.236] 发送: can0  715   [8]  04 2E 20 10 FF 55 55 55
[10:53:53.236] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.236] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.286] 测试写DID 0x2010, 数据: 00 00...
[10:53:53.287] 发送: can0  715   [8]  05 2E 20 10 00 00 55 55
[10:53:53.287] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.287] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.337] 测试写DID 0x2010, 数据: 00 01...
[10:53:53.337] 发送: can0  715   [8]  05 2E 20 10 00 01 55 55
[10:53:53.337] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.337] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.387] 测试写DID 0x2010, 数据: FF FF...
[10:53:53.387] 发送: can0  715   [8]  05 2E 20 10 FF FF 55 55
[10:53:53.387] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.387] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.437] 测试写DID 0x2010, 数据: 12 34...
[10:53:53.438] 发送: can0  715   [8]  05 2E 20 10 12 34 55 55
[10:53:53.438] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.438] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.488] 测试写DID 0x2010, 数据: 00 00 00...
[10:53:53.488] 发送: can0  715   [8]  06 2E 20 10 00 00 00 55
[10:53:53.488] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.488] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.538] 测试写DID 0x2010, 数据: 01 02 03...
[10:53:53.538] 发送: can0  715   [8]  06 2E 20 10 01 02 03 55
[10:53:53.538] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:53.538] ❌ DID 0x2010: 无效响应格式
[10:53:53.588] 测试写DID 0x2010, 数据: 00 00 00 00...
[10:53:53.588] 发送: can0  715   [8]  07 2E 20 10 00 00 00 00
[10:53:53.588] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.588] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.639] 测试写DID 0x2010, 数据: 12 34 56 78...
[10:53:53.639] 发送: can0  715   [8]  07 2E 20 10 12 34 56 78
[10:53:53.639] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.639] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.689] 测试写DID 0x2010, 数据: FF FF FF FF...
[10:53:53.689] 发送: can0  715   [8]  07 2E 20 10 FF FF FF FF
[10:53:53.689] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.689] ❌ DID 0x2010: 负响应 NRC=0x13
[10:53:53.739] 测试写DID 0x2011, 数据: 00...
[10:53:53.739] 发送: can0  715   [8]  04 2E 20 11 00 55 55 55
[10:53:53.739] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.739] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:53.790] 测试写DID 0x2011, 数据: 01...
[10:53:53.790] 发送: can0  715   [8]  04 2E 20 11 01 55 55 55
[10:53:53.790] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.790] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:53.840] 测试写DID 0x2011, 数据: FF...
[10:53:53.840] 发送: can0  715   [8]  04 2E 20 11 FF 55 55 55
[10:53:53.840] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.840] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:53.890] 测试写DID 0x2011, 数据: 00 00...
[10:53:53.890] 发送: can0  715   [8]  05 2E 20 11 00 00 55 55
[10:53:53.890] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.890] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:53.941] 测试写DID 0x2011, 数据: 00 01...
[10:53:53.941] 发送: can0  715   [8]  05 2E 20 11 00 01 55 55
[10:53:53.941] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.941] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:53.991] 测试写DID 0x2011, 数据: FF FF...
[10:53:53.991] 发送: can0  715   [8]  05 2E 20 11 FF FF 55 55
[10:53:53.991] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:53.991] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.041] 测试写DID 0x2011, 数据: 12 34...
[10:53:54.041] 发送: can0  715   [8]  05 2E 20 11 12 34 55 55
[10:53:54.041] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.041] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.092] 测试写DID 0x2011, 数据: 00 00 00...
[10:53:54.092] 发送: can0  715   [8]  06 2E 20 11 00 00 00 55
[10:53:54.092] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:54.092] ❌ DID 0x2011: 无效响应格式
[10:53:54.142] 测试写DID 0x2011, 数据: 01 02 03...
[10:53:54.142] 发送: can0  715   [8]  06 2E 20 11 01 02 03 55
[10:53:54.142] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.142] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.192] 测试写DID 0x2011, 数据: 00 00 00 00...
[10:53:54.193] 发送: can0  715   [8]  07 2E 20 11 00 00 00 00
[10:53:54.193] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.193] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.243] 测试写DID 0x2011, 数据: 12 34 56 78...
[10:53:54.243] 发送: can0  715   [8]  07 2E 20 11 12 34 56 78
[10:53:54.243] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.243] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.293] 测试写DID 0x2011, 数据: FF FF FF FF...
[10:53:54.293] 发送: can0  715   [8]  07 2E 20 11 FF FF FF FF
[10:53:54.293] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.293] ❌ DID 0x2011: 负响应 NRC=0x13
[10:53:54.343] 测试写DID 0x2012, 数据: 00...
[10:53:54.344] 发送: can0  715   [8]  04 2E 20 12 00 55 55 55
[10:53:54.344] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.344] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.394] 测试写DID 0x2012, 数据: 01...
[10:53:54.394] 发送: can0  715   [8]  04 2E 20 12 01 55 55 55
[10:53:54.394] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.394] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.444] 测试写DID 0x2012, 数据: FF...
[10:53:54.444] 发送: can0  715   [8]  04 2E 20 12 FF 55 55 55
[10:53:54.444] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.444] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.494] 测试写DID 0x2012, 数据: 00 00...
[10:53:54.494] 发送: can0  715   [8]  05 2E 20 12 00 00 55 55
[10:53:54.494] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.495] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.545] 测试写DID 0x2012, 数据: 00 01...
[10:53:54.545] 发送: can0  715   [8]  05 2E 20 12 00 01 55 55
[10:53:54.545] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.545] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.595] 测试写DID 0x2012, 数据: FF FF...
[10:53:54.595] 发送: can0  715   [8]  05 2E 20 12 FF FF 55 55
[10:53:54.595] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.595] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.645] 测试写DID 0x2012, 数据: 12 34...
[10:53:54.645] 发送: can0  715   [8]  05 2E 20 12 12 34 55 55
[10:53:54.645] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:54.645] ❌ DID 0x2012: 无效响应格式
[10:53:54.696] 测试写DID 0x2012, 数据: 00 00 00...
[10:53:54.696] 发送: can0  715   [8]  06 2E 20 12 00 00 00 55
[10:53:54.696] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.696] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.746] 测试写DID 0x2012, 数据: 01 02 03...
[10:53:54.746] 发送: can0  715   [8]  06 2E 20 12 01 02 03 55
[10:53:54.746] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.746] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.796] 测试写DID 0x2012, 数据: 00 00 00 00...
[10:53:54.796] 发送: can0  715   [8]  07 2E 20 12 00 00 00 00
[10:53:54.797] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.797] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.847] 测试写DID 0x2012, 数据: 12 34 56 78...
[10:53:54.847] 发送: can0  715   [8]  07 2E 20 12 12 34 56 78
[10:53:54.847] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.847] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.897] 测试写DID 0x2012, 数据: FF FF FF FF...
[10:53:54.897] 发送: can0  715   [8]  07 2E 20 12 FF FF FF FF
[10:53:54.897] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.897] ❌ DID 0x2012: 负响应 NRC=0x13
[10:53:54.947] 测试写DID 0x2013, 数据: 00...
[10:53:54.947] 发送: can0  715   [8]  04 2E 20 13 00 55 55 55
[10:53:54.947] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.947] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:54.998] 测试写DID 0x2013, 数据: 01...
[10:53:54.998] 发送: can0  715   [8]  04 2E 20 13 01 55 55 55
[10:53:54.998] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:54.998] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.048] 测试写DID 0x2013, 数据: FF...
[10:53:55.048] 发送: can0  715   [8]  04 2E 20 13 FF 55 55 55
[10:53:55.048] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.048] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.098] 测试写DID 0x2013, 数据: 00 00...
[10:53:55.098] 发送: can0  715   [8]  05 2E 20 13 00 00 55 55
[10:53:55.098] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.098] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.148] 测试写DID 0x2013, 数据: 00 01...
[10:53:55.149] 发送: can0  715   [8]  05 2E 20 13 00 01 55 55
[10:53:55.149] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.149] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.199] 测试写DID 0x2013, 数据: FF FF...
[10:53:55.199] 发送: can0  715   [8]  05 2E 20 13 FF FF 55 55
[10:53:55.199] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:55.199] ❌ DID 0x2013: 无效响应格式
[10:53:55.249] 测试写DID 0x2013, 数据: 12 34...
[10:53:55.249] 发送: can0  715   [8]  05 2E 20 13 12 34 55 55
[10:53:55.249] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.249] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.299] 测试写DID 0x2013, 数据: 00 00 00...
[10:53:55.300] 发送: can0  715   [8]  06 2E 20 13 00 00 00 55
[10:53:55.300] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.300] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.350] 测试写DID 0x2013, 数据: 01 02 03...
[10:53:55.350] 发送: can0  715   [8]  06 2E 20 13 01 02 03 55
[10:53:55.350] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.350] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.400] 测试写DID 0x2013, 数据: 00 00 00 00...
[10:53:55.400] 发送: can0  715   [8]  07 2E 20 13 00 00 00 00
[10:53:55.400] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.400] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.451] 测试写DID 0x2013, 数据: 12 34 56 78...
[10:53:55.451] 发送: can0  715   [8]  07 2E 20 13 12 34 56 78
[10:53:55.451] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.451] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.501] 测试写DID 0x2013, 数据: FF FF FF FF...
[10:53:55.501] 发送: can0  715   [8]  07 2E 20 13 FF FF FF FF
[10:53:55.501] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.501] ❌ DID 0x2013: 负响应 NRC=0x13
[10:53:55.551] 测试写DID 0x2014, 数据: 00...
[10:53:55.551] 发送: can0  715   [8]  04 2E 20 14 00 55 55 55
[10:53:55.551] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.551] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.602] 测试写DID 0x2014, 数据: 01...
[10:53:55.602] 发送: can0  715   [8]  04 2E 20 14 01 55 55 55
[10:53:55.602] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.602] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.652] 测试写DID 0x2014, 数据: FF...
[10:53:55.652] 发送: can0  715   [8]  04 2E 20 14 FF 55 55 55
[10:53:55.652] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.652] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.702] 测试写DID 0x2014, 数据: 00 00...
[10:53:55.702] 发送: can0  715   [8]  05 2E 20 14 00 00 55 55
[10:53:55.702] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.702] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.753] 测试写DID 0x2014, 数据: 00 01...
[10:53:55.753] 发送: can0  715   [8]  05 2E 20 14 00 01 55 55
[10:53:55.753] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:55.753] ❌ DID 0x2014: 无效响应格式
[10:53:55.803] 测试写DID 0x2014, 数据: FF FF...
[10:53:55.803] 发送: can0  715   [8]  05 2E 20 14 FF FF 55 55
[10:53:55.803] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.803] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.853] 测试写DID 0x2014, 数据: 12 34...
[10:53:55.853] 发送: can0  715   [8]  05 2E 20 14 12 34 55 55
[10:53:55.853] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.853] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.903] 测试写DID 0x2014, 数据: 00 00 00...
[10:53:55.904] 发送: can0  715   [8]  06 2E 20 14 00 00 00 55
[10:53:55.904] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.904] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:55.954] 测试写DID 0x2014, 数据: 01 02 03...
[10:53:55.954] 发送: can0  715   [8]  06 2E 20 14 01 02 03 55
[10:53:55.954] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:55.954] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:56.004] 测试写DID 0x2014, 数据: 00 00 00 00...
[10:53:56.004] 发送: can0  715   [8]  07 2E 20 14 00 00 00 00
[10:53:56.004] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.004] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:56.054] 测试写DID 0x2014, 数据: 12 34 56 78...
[10:53:56.055] 发送: can0  715   [8]  07 2E 20 14 12 34 56 78
[10:53:56.055] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.055] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:56.105] 测试写DID 0x2014, 数据: FF FF FF FF...
[10:53:56.105] 发送: can0  715   [8]  07 2E 20 14 FF FF FF FF
[10:53:56.105] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.105] ❌ DID 0x2014: 负响应 NRC=0x13
[10:53:56.155] 测试写DID 0x2100, 数据: 00...
[10:53:56.155] 发送: can0  715   [8]  04 2E 21 00 00 55 55 55
[10:53:56.155] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.155] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.206] 测试写DID 0x2100, 数据: 01...
[10:53:56.206] 发送: can0  715   [8]  04 2E 21 00 01 55 55 55
[10:53:56.206] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.206] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.256] 测试写DID 0x2100, 数据: FF...
[10:53:56.256] 发送: can0  715   [8]  04 2E 21 00 FF 55 55 55
[10:53:56.256] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.256] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.306] 测试写DID 0x2100, 数据: 00 00...
[10:53:56.306] 发送: can0  715   [8]  05 2E 21 00 00 00 55 55
[10:53:56.306] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:56.306] ❌ DID 0x2100: 无效响应格式
[10:53:56.356] 测试写DID 0x2100, 数据: 00 01...
[10:53:56.357] 发送: can0  715   [8]  05 2E 21 00 00 01 55 55
[10:53:56.357] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.357] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.407] 测试写DID 0x2100, 数据: FF FF...
[10:53:56.407] 发送: can0  715   [8]  05 2E 21 00 FF FF 55 55
[10:53:56.407] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.407] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.457] 测试写DID 0x2100, 数据: 12 34...
[10:53:56.457] 发送: can0  715   [8]  05 2E 21 00 12 34 55 55
[10:53:56.457] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.457] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.508] 测试写DID 0x2100, 数据: 00 00 00...
[10:53:56.508] 发送: can0  715   [8]  06 2E 21 00 00 00 00 55
[10:53:56.508] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.508] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.558] 测试写DID 0x2100, 数据: 01 02 03...
[10:53:56.558] 发送: can0  715   [8]  06 2E 21 00 01 02 03 55
[10:53:56.558] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.558] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.608] 测试写DID 0x2100, 数据: 00 00 00 00...
[10:53:56.608] 发送: can0  715   [8]  07 2E 21 00 00 00 00 00
[10:53:56.608] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.608] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.659] 测试写DID 0x2100, 数据: 12 34 56 78...
[10:53:56.659] 发送: can0  715   [8]  07 2E 21 00 12 34 56 78
[10:53:56.659] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.659] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.709] 测试写DID 0x2100, 数据: FF FF FF FF...
[10:53:56.709] 发送: can0  715   [8]  07 2E 21 00 FF FF FF FF
[10:53:56.709] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.709] ❌ DID 0x2100: 负响应 NRC=0x13
[10:53:56.759] 测试写DID 0x2101, 数据: 00...
[10:53:56.759] 发送: can0  715   [8]  04 2E 21 01 00 55 55 55
[10:53:56.759] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.759] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:56.809] 测试写DID 0x2101, 数据: 01...
[10:53:56.810] 发送: can0  715   [8]  04 2E 21 01 01 55 55 55
[10:53:56.810] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.810] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:56.860] 测试写DID 0x2101, 数据: FF...
[10:53:56.860] 发送: can0  715   [8]  04 2E 21 01 FF 55 55 55
[10:53:56.860] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:56.860] ❌ DID 0x2101: 无效响应格式
[10:53:56.910] 测试写DID 0x2101, 数据: 00 00...
[10:53:56.910] 发送: can0  715   [8]  05 2E 21 01 00 00 55 55
[10:53:56.911] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.911] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:56.961] 测试写DID 0x2101, 数据: 00 01...
[10:53:56.961] 发送: can0  715   [8]  05 2E 21 01 00 01 55 55
[10:53:56.961] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:56.961] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.011] 测试写DID 0x2101, 数据: FF FF...
[10:53:57.011] 发送: can0  715   [8]  05 2E 21 01 FF FF 55 55
[10:53:57.011] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.011] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.061] 测试写DID 0x2101, 数据: 12 34...
[10:53:57.061] 发送: can0  715   [8]  05 2E 21 01 12 34 55 55
[10:53:57.062] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.062] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.112] 测试写DID 0x2101, 数据: 00 00 00...
[10:53:57.112] 发送: can0  715   [8]  06 2E 21 01 00 00 00 55
[10:53:57.112] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.112] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.162] 测试写DID 0x2101, 数据: 01 02 03...
[10:53:57.162] 发送: can0  715   [8]  06 2E 21 01 01 02 03 55
[10:53:57.162] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.162] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.212] 测试写DID 0x2101, 数据: 00 00 00 00...
[10:53:57.212] 发送: can0  715   [8]  07 2E 21 01 00 00 00 00
[10:53:57.212] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.212] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.263] 测试写DID 0x2101, 数据: 12 34 56 78...
[10:53:57.263] 发送: can0  715   [8]  07 2E 21 01 12 34 56 78
[10:53:57.263] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.263] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.313] 测试写DID 0x2101, 数据: FF FF FF FF...
[10:53:57.313] 发送: can0  715   [8]  07 2E 21 01 FF FF FF FF
[10:53:57.313] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.313] ❌ DID 0x2101: 负响应 NRC=0x13
[10:53:57.363] 测试写DID 0x2102, 数据: 00...
[10:53:57.363] 发送: can0  715   [8]  04 2E 21 02 00 55 55 55
[10:53:57.363] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.363] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.414] 测试写DID 0x2102, 数据: 01...
[10:53:57.414] 发送: can0  715   [8]  04 2E 21 02 01 55 55 55
[10:53:57.414] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[10:53:57.414] ❌ DID 0x2102: 无效响应格式
[10:53:57.464] 测试写DID 0x2102, 数据: FF...
[10:53:57.464] 发送: can0  715   [8]  04 2E 21 02 FF 55 55 55
[10:53:57.464] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.464] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.514] 测试写DID 0x2102, 数据: 00 00...
[10:53:57.514] 发送: can0  715   [8]  05 2E 21 02 00 00 55 55
[10:53:57.514] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.514] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.564] 测试写DID 0x2102, 数据: 00 01...
[10:53:57.565] 发送: can0  715   [8]  05 2E 21 02 00 01 55 55
[10:53:57.565] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.565] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.615] 测试写DID 0x2102, 数据: FF FF...
[10:53:57.615] 发送: can0  715   [8]  05 2E 21 02 FF FF 55 55
[10:53:57.615] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.615] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.665] 测试写DID 0x2102, 数据: 12 34...
[10:53:57.665] 发送: can0  715   [8]  05 2E 21 02 12 34 55 55
[10:53:57.665] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.665] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.716] 测试写DID 0x2102, 数据: 00 00 00...
[10:53:57.716] 发送: can0  715   [8]  06 2E 21 02 00 00 00 55
[10:53:57.716] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.716] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.766] 测试写DID 0x2102, 数据: 01 02 03...
[10:53:57.766] 发送: can0  715   [8]  06 2E 21 02 01 02 03 55
[10:53:57.766] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.766] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.816] 测试写DID 0x2102, 数据: 00 00 00 00...
[10:53:57.816] 发送: can0  715   [8]  07 2E 21 02 00 00 00 00
[10:53:57.816] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.816] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.867] 测试写DID 0x2102, 数据: 12 34 56 78...
[10:53:57.867] 发送: can0  715   [8]  07 2E 21 02 12 34 56 78
[10:53:57.867] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.867] ❌ DID 0x2102: 负响应 NRC=0x13
[10:53:57.917] 测试写DID 0x2102, 数据: FF FF FF FF...
[10:53:57.917] 发送: can0  715   [8]  07 2E 21 02 FF FF FF FF
[10:53:57.917] 接收: can0  795   [8]  03 7F 2E 13 55 55 55 55
[10:53:57.917] ❌ DID 0x2102: 负响应 NRC=0x13
