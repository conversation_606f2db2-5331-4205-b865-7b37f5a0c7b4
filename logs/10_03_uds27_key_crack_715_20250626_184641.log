UDS 27服务AES密钥破解 - 2025-06-26 18:46:41.592348
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[18:46:41.592] 执行UDS 10 01 (默认会话) - 第1次尝试...
[18:46:41.592] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:41.593] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:46:41.593] ✅ UDS 10 01 成功 (第1次尝试)
[18:46:42.094] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[18:46:42.094] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[18:46:42.094] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:42.095] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[18:46:42.095] ✅ UDS 10 03 成功 (第1次尝试)
[18:46:42.096] === 第  1次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[18:46:42.096] 第  1次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[18:46:42.096] 执行UDS 10 01 (默认会话) - 第1次尝试...
[18:46:42.096] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:42.097] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:46:42.097] ✅ UDS 10 01 成功 (第1次尝试)
[18:46:43.099] 第  1次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[18:46:43.099] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[18:46:43.099] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[18:46:43.099] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:43.099] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:43.100] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[18:46:43.100] ✅ UDS 10 03 成功 (第1次尝试)
[18:46:44.100] 第  1次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[18:46:44.101] 第  1次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[18:46:44.113] 第  1次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[18:46:44.113] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[18:46:44.113] === 第  1次测试结束 ===
[18:46:46.114] === 第  2次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[18:46:46.114] 第  2次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[18:46:46.114] 执行UDS 10 01 (默认会话) - 第1次尝试...
[18:46:46.114] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:46.114] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:46.114] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:46.114] 接收: can0  715   [3]  02 27 03
[18:46:46.114] 接收: can0  795   [8]  10 12 67 03 D0 2A 52 56
[18:46:46.114] ❌ UDS 10 01 失败 (第1次尝试)
[18:46:46.114] 等待1秒后重试...
[18:46:47.116] 执行UDS 10 01 (默认会话) - 第2次尝试...
[18:46:47.116] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:47.116] 接收: can0  715   [3]  30 00 00
[18:46:47.116] 接收: can0  795   [8]  21 D6 2E BC 14 70 A2 75
[18:46:47.116] ❌ UDS 10 01 失败 (第2次尝试)
[18:46:47.116] 等待1秒后重试...
[18:46:48.117] 执行UDS 10 01 (默认会话) - 第3次尝试...
[18:46:48.117] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:48.117] 接收: can0  795   [8]  22 6B C2 55 82 75 55 55
[18:46:48.117] ❌ UDS 10 01 失败 (第3次尝试)
[18:46:48.117] ❌ UDS 10 01 最终失败 (已重试3次)
[18:46:48.117] 第  2次: UDS 10 01 最终失败，测试终止
[18:46:48.117] === 第  3次测试开始: 密钥 00000000000000000000000000000000 ===
[18:46:48.117] 第  3次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[18:46:48.117] 执行UDS 10 01 (默认会话) - 第1次尝试...
[18:46:48.117] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:48.117] 接收: can0  715   [8]  10 12 27 04 FA 55 C7 04
[18:46:48.117] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[18:46:48.117] ❌ UDS 10 01 失败 (第1次尝试)
[18:46:48.117] 等待1秒后重试...
[18:46:49.118] 执行UDS 10 01 (默认会话) - 第2次尝试...
[18:46:49.118] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:49.118] 接收: can0  715   [8]  21 1F 46 11 D0 33 D2 CB
[18:46:49.119] 接收: can0  715   [6]  22 28 5A 86 64 1B
[18:46:49.119] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[18:46:49.119] ❌ UDS 10 01 失败 (第2次尝试)
[18:46:49.119] 等待1秒后重试...
[18:46:50.120] 执行UDS 10 01 (默认会话) - 第3次尝试...
[18:46:50.120] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:46:50.120] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:50.120] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:50.120] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:50.120] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:50.120] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:46:50.120] ✅ UDS 10 01 成功 (第3次尝试)
[18:46:51.121] 第  3次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[18:46:51.121] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[18:46:51.121] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[18:46:51.121] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:51.121] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:46:51.121] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:46:51.121] ✅ UDS 10 03 成功 (第1次尝试)
[18:46:52.122] 第  3次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[18:46:52.122] 第  3次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[18:46:52.130] 第  3次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[18:46:52.131] 第  3次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[18:46:52.131] === 第  3次测试结束 ===
