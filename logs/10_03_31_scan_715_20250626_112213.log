UDS 31服务例程控制扫描 - 2025-06-26 11:22:13.740399
请求CAN ID: 0x715
响应CAN ID: 0x795
测试例程数量: 70
============================================================

[11:22:13.740] 执行UDS 10 01 (默认会话)...
[11:22:13.740] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[11:22:13.741] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[11:22:13.741] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[11:22:13.741] ✅ UDS 10 01 成功
[11:22:14.242] 执行UDS 10 03 (扩展会话)...
[11:22:14.242] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[11:22:14.242] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:14.242] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:14.243] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[11:22:14.243] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[11:22:14.243] ✅ UDS 10 03 成功
[11:22:14.744] 测试例程 0x0001 - 启动例程...
[11:22:14.744] 发送: can0  715   [8]  04 31 01 00 01 55 55 55
[11:22:14.744] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:14.744] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:14.745] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:14.745] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:14.745] ❌ 例程 0x0001: 负响应 NRC=0x31
[11:22:14.846] 测试例程 0x0001 - 停止例程...
[11:22:14.846] 发送: can0  715   [8]  04 31 02 00 01 55 55 55
[11:22:14.846] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:14.846] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:14.846] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:14.847] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:14.847] ❌ 例程 0x0001: 负响应 NRC=0x12
[11:22:14.947] 测试例程 0x0001 - 请求结果...
[11:22:14.947] 发送: can0  715   [8]  04 31 03 00 01 55 55 55
[11:22:14.948] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:14.949] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:14.949] ❌ 例程 0x0001: 负响应 NRC=0x12
[11:22:15.049] 测试例程 0x0002 - 启动例程...
[11:22:15.049] 发送: can0  715   [8]  04 31 01 00 02 55 55 55
[11:22:15.050] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.050] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.051] ❌ 例程 0x0002: 负响应 NRC=0x31
[11:22:15.151] 测试例程 0x0002 - 停止例程...
[11:22:15.151] 发送: can0  715   [8]  04 31 02 00 02 55 55 55
[11:22:15.151] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.151] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.151] ❌ 例程 0x0002: 负响应 NRC=0x12
[11:22:15.252] 测试例程 0x0002 - 请求结果...
[11:22:15.252] 发送: can0  715   [8]  04 31 03 00 02 55 55 55
[11:22:15.252] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:15.252] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:15.252] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.253] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.253] ❌ 例程 0x0002: 负响应 NRC=0x12
[11:22:15.353] 测试例程 0x0003 - 启动例程...
[11:22:15.353] 发送: can0  715   [8]  04 31 01 00 03 55 55 55
[11:22:15.354] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.354] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.354] ❌ 例程 0x0003: 负响应 NRC=0x31
[11:22:15.454] 测试例程 0x0003 - 停止例程...
[11:22:15.454] 发送: can0  715   [8]  04 31 02 00 03 55 55 55
[11:22:15.455] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.455] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.455] ❌ 例程 0x0003: 负响应 NRC=0x12
[11:22:15.555] 测试例程 0x0003 - 请求结果...
[11:22:15.555] 发送: can0  715   [8]  04 31 03 00 03 55 55 55
[11:22:15.556] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.556] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.556] ❌ 例程 0x0003: 负响应 NRC=0x12
[11:22:15.656] 测试例程 0x0004 - 启动例程...
[11:22:15.656] 发送: can0  715   [8]  04 31 01 00 04 55 55 55
[11:22:15.657] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.657] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.657] ❌ 例程 0x0004: 负响应 NRC=0x31
[11:22:15.757] 测试例程 0x0004 - 停止例程...
[11:22:15.757] 发送: can0  715   [8]  04 31 02 00 04 55 55 55
[11:22:15.757] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:15.757] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:15.758] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.758] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.758] ❌ 例程 0x0004: 负响应 NRC=0x12
[11:22:15.858] 测试例程 0x0004 - 请求结果...
[11:22:15.858] 发送: can0  715   [8]  04 31 03 00 04 55 55 55
[11:22:15.860] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.860] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:15.860] ❌ 例程 0x0004: 负响应 NRC=0x12
[11:22:15.960] 测试例程 0x0005 - 启动例程...
[11:22:15.960] 发送: can0  715   [8]  04 31 01 00 05 55 55 55
[11:22:15.961] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.961] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:15.961] ❌ 例程 0x0005: 负响应 NRC=0x31
[11:22:16.061] 测试例程 0x0005 - 停止例程...
[11:22:16.061] 发送: can0  715   [8]  04 31 02 00 05 55 55 55
[11:22:16.062] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.062] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.062] ❌ 例程 0x0005: 负响应 NRC=0x12
[11:22:16.162] 测试例程 0x0005 - 请求结果...
[11:22:16.162] 发送: can0  715   [8]  04 31 03 00 05 55 55 55
[11:22:16.163] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.163] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.163] ❌ 例程 0x0005: 负响应 NRC=0x12
[11:22:16.263] 测试例程 0x0010 - 启动例程...
[11:22:16.263] 发送: can0  715   [8]  04 31 01 00 10 55 55 55
[11:22:16.263] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:16.263] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:16.264] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.264] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.264] ❌ 例程 0x0010: 负响应 NRC=0x31
[11:22:16.364] 测试例程 0x0010 - 停止例程...
[11:22:16.364] 发送: can0  715   [8]  04 31 02 00 10 55 55 55
[11:22:16.365] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.365] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.365] ❌ 例程 0x0010: 负响应 NRC=0x12
[11:22:16.465] 测试例程 0x0010 - 请求结果...
[11:22:16.465] 发送: can0  715   [8]  04 31 03 00 10 55 55 55
[11:22:16.467] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.467] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.467] ❌ 例程 0x0010: 负响应 NRC=0x12
[11:22:16.567] 测试例程 0x0011 - 启动例程...
[11:22:16.567] 发送: can0  715   [8]  04 31 01 00 11 55 55 55
[11:22:16.568] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.568] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.568] ❌ 例程 0x0011: 负响应 NRC=0x31
[11:22:16.668] 测试例程 0x0011 - 停止例程...
[11:22:16.668] 发送: can0  715   [8]  04 31 02 00 11 55 55 55
[11:22:16.670] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.670] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.670] ❌ 例程 0x0011: 负响应 NRC=0x12
[11:22:16.770] 测试例程 0x0011 - 请求结果...
[11:22:16.770] 发送: can0  715   [8]  04 31 03 00 11 55 55 55
[11:22:16.770] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:16.770] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:16.771] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.771] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.771] ❌ 例程 0x0011: 负响应 NRC=0x12
[11:22:16.871] 测试例程 0x0012 - 启动例程...
[11:22:16.871] 发送: can0  715   [8]  04 31 01 00 12 55 55 55
[11:22:16.872] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.872] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:16.872] ❌ 例程 0x0012: 负响应 NRC=0x31
[11:22:16.972] 测试例程 0x0012 - 停止例程...
[11:22:16.972] 发送: can0  715   [8]  04 31 02 00 12 55 55 55
[11:22:16.973] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.973] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:16.973] ❌ 例程 0x0012: 负响应 NRC=0x12
[11:22:17.073] 测试例程 0x0012 - 请求结果...
[11:22:17.073] 发送: can0  715   [8]  04 31 03 00 12 55 55 55
[11:22:17.074] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.074] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.074] ❌ 例程 0x0012: 负响应 NRC=0x12
[11:22:17.174] 测试例程 0x0013 - 启动例程...
[11:22:17.175] 发送: can0  715   [8]  04 31 01 00 13 55 55 55
[11:22:17.176] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.176] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.176] ❌ 例程 0x0013: 负响应 NRC=0x31
[11:22:17.277] 测试例程 0x0013 - 停止例程...
[11:22:17.277] 发送: can0  715   [8]  04 31 02 00 13 55 55 55
[11:22:17.277] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:17.277] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:17.278] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.278] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.278] ❌ 例程 0x0013: 负响应 NRC=0x12
[11:22:17.378] 测试例程 0x0013 - 请求结果...
[11:22:17.379] 发送: can0  715   [8]  04 31 03 00 13 55 55 55
[11:22:17.380] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.380] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.380] ❌ 例程 0x0013: 负响应 NRC=0x12
[11:22:17.480] 测试例程 0x0014 - 启动例程...
[11:22:17.481] 发送: can0  715   [8]  04 31 01 00 14 55 55 55
[11:22:17.481] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.481] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.481] ❌ 例程 0x0014: 负响应 NRC=0x31
[11:22:17.581] 测试例程 0x0014 - 停止例程...
[11:22:17.582] 发送: can0  715   [8]  04 31 02 00 14 55 55 55
[11:22:17.582] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.582] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.582] ❌ 例程 0x0014: 负响应 NRC=0x12
[11:22:17.683] 测试例程 0x0014 - 请求结果...
[11:22:17.683] 发送: can0  715   [8]  04 31 03 00 14 55 55 55
[11:22:17.684] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.684] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.684] ❌ 例程 0x0014: 负响应 NRC=0x12
[11:22:17.785] 测试例程 0x0020 - 启动例程...
[11:22:17.785] 发送: can0  715   [8]  04 31 01 00 20 55 55 55
[11:22:17.785] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:17.785] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:17.785] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.785] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:17.785] ❌ 例程 0x0020: 负响应 NRC=0x31
[11:22:17.886] 测试例程 0x0020 - 停止例程...
[11:22:17.886] 发送: can0  715   [8]  04 31 02 00 20 55 55 55
[11:22:17.886] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.886] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.886] ❌ 例程 0x0020: 负响应 NRC=0x12
[11:22:17.987] 测试例程 0x0020 - 请求结果...
[11:22:17.987] 发送: can0  715   [8]  04 31 03 00 20 55 55 55
[11:22:17.987] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.987] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:17.987] ❌ 例程 0x0020: 负响应 NRC=0x12
[11:22:18.088] 测试例程 0x0021 - 启动例程...
[11:22:18.088] 发送: can0  715   [8]  04 31 01 00 21 55 55 55
[11:22:18.088] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.088] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.088] ❌ 例程 0x0021: 负响应 NRC=0x31
[11:22:18.189] 测试例程 0x0021 - 停止例程...
[11:22:18.189] 发送: can0  715   [8]  04 31 02 00 21 55 55 55
[11:22:18.189] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.189] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.189] ❌ 例程 0x0021: 负响应 NRC=0x12
[11:22:18.290] 测试例程 0x0021 - 请求结果...
[11:22:18.290] 发送: can0  715   [8]  04 31 03 00 21 55 55 55
[11:22:18.290] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:18.290] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:18.292] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.292] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.292] ❌ 例程 0x0021: 负响应 NRC=0x12
[11:22:18.392] 测试例程 0x0022 - 启动例程...
[11:22:18.392] 发送: can0  715   [8]  04 31 01 00 22 55 55 55
[11:22:18.393] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.393] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.393] ❌ 例程 0x0022: 负响应 NRC=0x31
[11:22:18.493] 测试例程 0x0022 - 停止例程...
[11:22:18.493] 发送: can0  715   [8]  04 31 02 00 22 55 55 55
[11:22:18.494] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.494] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.494] ❌ 例程 0x0022: 负响应 NRC=0x12
[11:22:18.594] 测试例程 0x0022 - 请求结果...
[11:22:18.594] 发送: can0  715   [8]  04 31 03 00 22 55 55 55
[11:22:18.595] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.595] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.595] ❌ 例程 0x0022: 负响应 NRC=0x12
[11:22:18.695] 测试例程 0x0023 - 启动例程...
[11:22:18.695] 发送: can0  715   [8]  04 31 01 00 23 55 55 55
[11:22:18.698] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.698] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:18.698] ❌ 例程 0x0023: 负响应 NRC=0x31
[11:22:18.799] 测试例程 0x0023 - 停止例程...
[11:22:18.799] 发送: can0  715   [8]  04 31 02 00 23 55 55 55
[11:22:18.799] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:18.799] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:18.800] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.800] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.800] ❌ 例程 0x0023: 负响应 NRC=0x12
[11:22:18.900] 测试例程 0x0023 - 请求结果...
[11:22:18.900] 发送: can0  715   [8]  04 31 03 00 23 55 55 55
[11:22:18.902] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.902] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:18.902] ❌ 例程 0x0023: 负响应 NRC=0x12
[11:22:19.002] 测试例程 0x0024 - 启动例程...
[11:22:19.002] 发送: can0  715   [8]  04 31 01 00 24 55 55 55
[11:22:19.004] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.004] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.004] ❌ 例程 0x0024: 负响应 NRC=0x31
[11:22:19.104] 测试例程 0x0024 - 停止例程...
[11:22:19.104] 发送: can0  715   [8]  04 31 02 00 24 55 55 55
[11:22:19.105] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.105] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.105] ❌ 例程 0x0024: 负响应 NRC=0x12
[11:22:19.205] 测试例程 0x0024 - 请求结果...
[11:22:19.205] 发送: can0  715   [8]  04 31 03 00 24 55 55 55
[11:22:19.206] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.206] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.206] ❌ 例程 0x0024: 负响应 NRC=0x12
[11:22:19.306] 测试例程 0x0100 - 启动例程...
[11:22:19.306] 发送: can0  715   [8]  04 31 01 01 00 55 55 55
[11:22:19.306] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:19.306] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:19.307] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.307] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.307] ❌ 例程 0x0100: 负响应 NRC=0x31
[11:22:19.407] 测试例程 0x0100 - 停止例程...
[11:22:19.407] 发送: can0  715   [8]  04 31 02 01 00 55 55 55
[11:22:19.408] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.408] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.408] ❌ 例程 0x0100: 负响应 NRC=0x12
[11:22:19.508] 测试例程 0x0100 - 请求结果...
[11:22:19.508] 发送: can0  715   [8]  04 31 03 01 00 55 55 55
[11:22:19.510] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.510] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.510] ❌ 例程 0x0100: 负响应 NRC=0x12
[11:22:19.610] 测试例程 0x0101 - 启动例程...
[11:22:19.610] 发送: can0  715   [8]  04 31 01 01 01 55 55 55
[11:22:19.612] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.612] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.612] ❌ 例程 0x0101: 负响应 NRC=0x31
[11:22:19.712] 测试例程 0x0101 - 停止例程...
[11:22:19.712] 发送: can0  715   [8]  04 31 02 01 01 55 55 55
[11:22:19.713] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.713] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.713] ❌ 例程 0x0101: 负响应 NRC=0x12
[11:22:19.813] 测试例程 0x0101 - 请求结果...
[11:22:19.813] 发送: can0  715   [8]  04 31 03 01 01 55 55 55
[11:22:19.813] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:19.813] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:19.814] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.814] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:19.814] ❌ 例程 0x0101: 负响应 NRC=0x12
[11:22:19.914] 测试例程 0x0102 - 启动例程...
[11:22:19.915] 发送: can0  715   [8]  04 31 01 01 02 55 55 55
[11:22:19.916] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.916] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:19.916] ❌ 例程 0x0102: 负响应 NRC=0x31
[11:22:20.016] 测试例程 0x0102 - 停止例程...
[11:22:20.016] 发送: can0  715   [8]  04 31 02 01 02 55 55 55
[11:22:20.018] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.018] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.018] ❌ 例程 0x0102: 负响应 NRC=0x12
[11:22:20.118] 测试例程 0x0102 - 请求结果...
[11:22:20.118] 发送: can0  715   [8]  04 31 03 01 02 55 55 55
[11:22:20.119] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.119] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.119] ❌ 例程 0x0102: 负响应 NRC=0x12
[11:22:20.219] 测试例程 0x0103 - 启动例程...
[11:22:20.219] 发送: can0  715   [8]  04 31 01 01 03 55 55 55
[11:22:20.220] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.220] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.220] ❌ 例程 0x0103: 负响应 NRC=0x31
[11:22:20.320] 测试例程 0x0103 - 停止例程...
[11:22:20.320] 发送: can0  715   [8]  04 31 02 01 03 55 55 55
[11:22:20.321] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:20.321] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:20.322] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.322] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.322] ❌ 例程 0x0103: 负响应 NRC=0x12
[11:22:20.422] 测试例程 0x0103 - 请求结果...
[11:22:20.423] 发送: can0  715   [8]  04 31 03 01 03 55 55 55
[11:22:20.424] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.424] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.424] ❌ 例程 0x0103: 负响应 NRC=0x12
[11:22:20.525] 测试例程 0x0104 - 启动例程...
[11:22:20.525] 发送: can0  715   [8]  04 31 01 01 04 55 55 55
[11:22:20.526] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.526] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.526] ❌ 例程 0x0104: 负响应 NRC=0x31
[11:22:20.626] 测试例程 0x0104 - 停止例程...
[11:22:20.627] 发送: can0  715   [8]  04 31 02 01 04 55 55 55
[11:22:20.628] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.628] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.628] ❌ 例程 0x0104: 负响应 NRC=0x12
[11:22:20.729] 测试例程 0x0104 - 请求结果...
[11:22:20.729] 发送: can0  715   [8]  04 31 03 01 04 55 55 55
[11:22:20.730] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.730] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.730] ❌ 例程 0x0104: 负响应 NRC=0x12
[11:22:20.831] 测试例程 0x0200 - 启动例程...
[11:22:20.831] 发送: can0  715   [8]  04 31 01 02 00 55 55 55
[11:22:20.831] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:20.831] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:20.832] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.832] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:20.832] ❌ 例程 0x0200: 负响应 NRC=0x31
[11:22:20.933] 测试例程 0x0200 - 停止例程...
[11:22:20.933] 发送: can0  715   [8]  04 31 02 02 00 55 55 55
[11:22:20.933] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.933] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:20.933] ❌ 例程 0x0200: 负响应 NRC=0x12
[11:22:21.034] 测试例程 0x0200 - 请求结果...
[11:22:21.034] 发送: can0  715   [8]  04 31 03 02 00 55 55 55
[11:22:21.034] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.034] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.034] ❌ 例程 0x0200: 负响应 NRC=0x12
[11:22:21.135] 测试例程 0x0201 - 启动例程...
[11:22:21.135] 发送: can0  715   [8]  04 31 01 02 01 55 55 55
[11:22:21.135] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:21.135] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:21.135] ❌ 例程 0x0201: 负响应 NRC=0x31
[11:22:21.236] 测试例程 0x0201 - 停止例程...
[11:22:21.236] 发送: can0  715   [8]  04 31 02 02 01 55 55 55
[11:22:21.236] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.236] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.236] ❌ 例程 0x0201: 负响应 NRC=0x12
[11:22:21.337] 测试例程 0x0201 - 请求结果...
[11:22:21.337] 发送: can0  715   [8]  04 31 03 02 01 55 55 55
[11:22:21.337] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:21.337] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:21.337] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.338] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.338] ❌ 例程 0x0201: 负响应 NRC=0x12
[11:22:21.438] 测试例程 0x0202 - 启动例程...
[11:22:21.438] 发送: can0  715   [8]  04 31 01 02 02 55 55 55
[11:22:21.439] 接收到帧: can0  795   [8]  03 7F 31 13 55 55 55 55
[11:22:21.439] 接收目标响应: can0  795   [8]  03 7F 31 13 55 55 55 55
[11:22:21.439] ❌ 例程 0x0202: 负响应 NRC=0x13
[11:22:21.539] 测试例程 0x0202 - 停止例程...
[11:22:21.539] 发送: can0  715   [8]  04 31 02 02 02 55 55 55
[11:22:21.540] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.540] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.540] ❌ 例程 0x0202: 负响应 NRC=0x12
[11:22:21.640] 测试例程 0x0202 - 请求结果...
[11:22:21.640] 发送: can0  715   [8]  04 31 03 02 02 55 55 55
[11:22:21.642] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.642] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.642] ❌ 例程 0x0202: 负响应 NRC=0x12
[11:22:21.742] 测试例程 0x0203 - 启动例程...
[11:22:21.742] 发送: can0  715   [8]  04 31 01 02 03 55 55 55
[11:22:21.743] 接收到帧: can0  795   [8]  05 71 01 02 03 00 55 55
[11:22:21.743] 接收目标响应: can0  795   [8]  05 71 01 02 03 00 55 55
[11:22:21.743] ✅ 例程 0x0203 (启动例程): 01 02 03 00 55 55
[11:22:21.843] 测试例程 0x0203 - 停止例程...
[11:22:21.843] 发送: can0  715   [8]  04 31 02 02 03 55 55 55
[11:22:21.843] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:21.843] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:21.844] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.844] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.844] ❌ 例程 0x0203: 负响应 NRC=0x12
[11:22:21.944] 测试例程 0x0203 - 请求结果...
[11:22:21.944] 发送: can0  715   [8]  04 31 03 02 03 55 55 55
[11:22:21.946] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.946] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:21.946] ❌ 例程 0x0203: 负响应 NRC=0x12
[11:22:22.046] 测试例程 0x0204 - 启动例程...
[11:22:22.046] 发送: can0  715   [8]  04 31 01 02 04 55 55 55
[11:22:22.047] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.047] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.047] ❌ 例程 0x0204: 负响应 NRC=0x31
[11:22:22.147] 测试例程 0x0204 - 停止例程...
[11:22:22.147] 发送: can0  715   [8]  04 31 02 02 04 55 55 55
[11:22:22.148] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.148] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.148] ❌ 例程 0x0204: 负响应 NRC=0x12
[11:22:22.248] 测试例程 0x0204 - 请求结果...
[11:22:22.248] 发送: can0  715   [8]  04 31 03 02 04 55 55 55
[11:22:22.249] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:22.249] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:22.250] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.250] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.250] ❌ 例程 0x0204: 负响应 NRC=0x12
[11:22:22.350] 测试例程 0x1000 - 启动例程...
[11:22:22.350] 发送: can0  715   [8]  04 31 01 10 00 55 55 55
[11:22:22.351] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.351] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.351] ❌ 例程 0x1000: 负响应 NRC=0x31
[11:22:22.451] 测试例程 0x1000 - 停止例程...
[11:22:22.451] 发送: can0  715   [8]  04 31 02 10 00 55 55 55
[11:22:22.453] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.453] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.453] ❌ 例程 0x1000: 负响应 NRC=0x12
[11:22:22.553] 测试例程 0x1000 - 请求结果...
[11:22:22.553] 发送: can0  715   [8]  04 31 03 10 00 55 55 55
[11:22:22.554] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.554] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.554] ❌ 例程 0x1000: 负响应 NRC=0x12
[11:22:22.654] 测试例程 0x1001 - 启动例程...
[11:22:22.654] 发送: can0  715   [8]  04 31 01 10 01 55 55 55
[11:22:22.655] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.655] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.655] ❌ 例程 0x1001: 负响应 NRC=0x31
[11:22:22.755] 测试例程 0x1001 - 停止例程...
[11:22:22.755] 发送: can0  715   [8]  04 31 02 10 01 55 55 55
[11:22:22.755] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:22.755] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:22.756] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.756] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.756] ❌ 例程 0x1001: 负响应 NRC=0x12
[11:22:22.856] 测试例程 0x1001 - 请求结果...
[11:22:22.856] 发送: can0  715   [8]  04 31 03 10 01 55 55 55
[11:22:22.858] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.858] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:22.858] ❌ 例程 0x1001: 负响应 NRC=0x12
[11:22:22.958] 测试例程 0x1002 - 启动例程...
[11:22:22.958] 发送: can0  715   [8]  04 31 01 10 02 55 55 55
[11:22:22.959] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.959] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:22.959] ❌ 例程 0x1002: 负响应 NRC=0x31
[11:22:23.059] 测试例程 0x1002 - 停止例程...
[11:22:23.059] 发送: can0  715   [8]  04 31 02 10 02 55 55 55
[11:22:23.060] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.060] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.060] ❌ 例程 0x1002: 负响应 NRC=0x12
[11:22:23.160] 测试例程 0x1002 - 请求结果...
[11:22:23.160] 发送: can0  715   [8]  04 31 03 10 02 55 55 55
[11:22:23.161] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.161] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.161] ❌ 例程 0x1002: 负响应 NRC=0x12
[11:22:23.261] 测试例程 0x1003 - 启动例程...
[11:22:23.261] 发送: can0  715   [8]  04 31 01 10 03 55 55 55
[11:22:23.261] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:23.262] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:23.263] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.263] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.263] ❌ 例程 0x1003: 负响应 NRC=0x31
[11:22:23.363] 测试例程 0x1003 - 停止例程...
[11:22:23.363] 发送: can0  715   [8]  04 31 02 10 03 55 55 55
[11:22:23.364] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.364] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.364] ❌ 例程 0x1003: 负响应 NRC=0x12
[11:22:23.464] 测试例程 0x1003 - 请求结果...
[11:22:23.465] 发送: can0  715   [8]  04 31 03 10 03 55 55 55
[11:22:23.465] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.465] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.465] ❌ 例程 0x1003: 负响应 NRC=0x12
[11:22:23.565] 测试例程 0x1004 - 启动例程...
[11:22:23.565] 发送: can0  715   [8]  04 31 01 10 04 55 55 55
[11:22:23.566] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.566] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.566] ❌ 例程 0x1004: 负响应 NRC=0x31
[11:22:23.666] 测试例程 0x1004 - 停止例程...
[11:22:23.667] 发送: can0  715   [8]  04 31 02 10 04 55 55 55
[11:22:23.667] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.667] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.667] ❌ 例程 0x1004: 负响应 NRC=0x12
[11:22:23.767] 测试例程 0x1004 - 请求结果...
[11:22:23.768] 发送: can0  715   [8]  04 31 03 10 04 55 55 55
[11:22:23.771] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:23.771] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:23.771] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.771] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.771] ❌ 例程 0x1004: 负响应 NRC=0x12
[11:22:23.871] 测试例程 0x2000 - 启动例程...
[11:22:23.871] 发送: can0  715   [8]  04 31 01 20 00 55 55 55
[11:22:23.873] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.873] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:23.874] ❌ 例程 0x2000: 负响应 NRC=0x31
[11:22:23.974] 测试例程 0x2000 - 停止例程...
[11:22:23.974] 发送: can0  715   [8]  04 31 02 20 00 55 55 55
[11:22:23.975] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.975] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:23.975] ❌ 例程 0x2000: 负响应 NRC=0x12
[11:22:24.076] 测试例程 0x2000 - 请求结果...
[11:22:24.076] 发送: can0  715   [8]  04 31 03 20 00 55 55 55
[11:22:24.077] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.077] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.077] ❌ 例程 0x2000: 负响应 NRC=0x12
[11:22:24.178] 测试例程 0x2001 - 启动例程...
[11:22:24.178] 发送: can0  715   [8]  04 31 01 20 01 55 55 55
[11:22:24.178] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.178] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.178] ❌ 例程 0x2001: 负响应 NRC=0x31
[11:22:24.279] 测试例程 0x2001 - 停止例程...
[11:22:24.279] 发送: can0  715   [8]  04 31 02 20 01 55 55 55
[11:22:24.279] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:24.279] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:24.279] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.279] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.279] ❌ 例程 0x2001: 负响应 NRC=0x12
[11:22:24.380] 测试例程 0x2001 - 请求结果...
[11:22:24.380] 发送: can0  715   [8]  04 31 03 20 01 55 55 55
[11:22:24.380] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.380] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.381] ❌ 例程 0x2001: 负响应 NRC=0x12
[11:22:24.481] 测试例程 0x2002 - 启动例程...
[11:22:24.481] 发送: can0  715   [8]  04 31 01 20 02 55 55 55
[11:22:24.482] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.482] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.482] ❌ 例程 0x2002: 负响应 NRC=0x31
[11:22:24.582] 测试例程 0x2002 - 停止例程...
[11:22:24.582] 发送: can0  715   [8]  04 31 02 20 02 55 55 55
[11:22:24.584] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.584] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.584] ❌ 例程 0x2002: 负响应 NRC=0x12
[11:22:24.684] 测试例程 0x2002 - 请求结果...
[11:22:24.684] 发送: can0  715   [8]  04 31 03 20 02 55 55 55
[11:22:24.686] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.686] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.686] ❌ 例程 0x2002: 负响应 NRC=0x12
[11:22:24.786] 测试例程 0x2003 - 启动例程...
[11:22:24.786] 发送: can0  715   [8]  04 31 01 20 03 55 55 55
[11:22:24.786] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:24.786] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:24.787] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.787] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:24.787] ❌ 例程 0x2003: 负响应 NRC=0x31
[11:22:24.887] 测试例程 0x2003 - 停止例程...
[11:22:24.887] 发送: can0  715   [8]  04 31 02 20 03 55 55 55
[11:22:24.888] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.888] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.888] ❌ 例程 0x2003: 负响应 NRC=0x12
[11:22:24.988] 测试例程 0x2003 - 请求结果...
[11:22:24.988] 发送: can0  715   [8]  04 31 03 20 03 55 55 55
[11:22:24.989] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.989] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:24.989] ❌ 例程 0x2003: 负响应 NRC=0x12
[11:22:25.089] 测试例程 0x2004 - 启动例程...
[11:22:25.089] 发送: can0  715   [8]  04 31 01 20 04 55 55 55
[11:22:25.091] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.091] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.091] ❌ 例程 0x2004: 负响应 NRC=0x31
[11:22:25.191] 测试例程 0x2004 - 停止例程...
[11:22:25.191] 发送: can0  715   [8]  04 31 02 20 04 55 55 55
[11:22:25.193] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.193] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.193] ❌ 例程 0x2004: 负响应 NRC=0x12
[11:22:25.293] 测试例程 0x2004 - 请求结果...
[11:22:25.293] 发送: can0  715   [8]  04 31 03 20 04 55 55 55
[11:22:25.293] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:25.293] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:25.295] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.295] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.295] ❌ 例程 0x2004: 负响应 NRC=0x12
[11:22:25.395] 测试例程 0x3000 - 启动例程...
[11:22:25.395] 发送: can0  715   [8]  04 31 01 30 00 55 55 55
[11:22:25.397] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.397] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.397] ❌ 例程 0x3000: 负响应 NRC=0x31
[11:22:25.497] 测试例程 0x3000 - 停止例程...
[11:22:25.497] 发送: can0  715   [8]  04 31 02 30 00 55 55 55
[11:22:25.499] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.499] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.499] ❌ 例程 0x3000: 负响应 NRC=0x12
[11:22:25.599] 测试例程 0x3000 - 请求结果...
[11:22:25.599] 发送: can0  715   [8]  04 31 03 30 00 55 55 55
[11:22:25.601] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.601] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.601] ❌ 例程 0x3000: 负响应 NRC=0x12
[11:22:25.701] 测试例程 0x3001 - 启动例程...
[11:22:25.701] 发送: can0  715   [8]  04 31 01 30 01 55 55 55
[11:22:25.702] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.702] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:25.702] ❌ 例程 0x3001: 负响应 NRC=0x31
[11:22:25.802] 测试例程 0x3001 - 停止例程...
[11:22:25.802] 发送: can0  715   [8]  04 31 02 30 01 55 55 55
[11:22:25.802] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:25.802] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:25.804] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.804] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.804] ❌ 例程 0x3001: 负响应 NRC=0x12
[11:22:25.904] 测试例程 0x3001 - 请求结果...
[11:22:25.904] 发送: can0  715   [8]  04 31 03 30 01 55 55 55
[11:22:25.905] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.905] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:25.905] ❌ 例程 0x3001: 负响应 NRC=0x12
[11:22:26.005] 测试例程 0x3002 - 启动例程...
[11:22:26.005] 发送: can0  715   [8]  04 31 01 30 02 55 55 55
[11:22:26.006] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.006] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.006] ❌ 例程 0x3002: 负响应 NRC=0x31
[11:22:26.106] 测试例程 0x3002 - 停止例程...
[11:22:26.106] 发送: can0  715   [8]  04 31 02 30 02 55 55 55
[11:22:26.108] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.108] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.108] ❌ 例程 0x3002: 负响应 NRC=0x12
[11:22:26.208] 测试例程 0x3002 - 请求结果...
[11:22:26.208] 发送: can0  715   [8]  04 31 03 30 02 55 55 55
[11:22:26.210] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.210] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.210] ❌ 例程 0x3002: 负响应 NRC=0x12
[11:22:26.310] 测试例程 0x3003 - 启动例程...
[11:22:26.310] 发送: can0  715   [8]  04 31 01 30 03 55 55 55
[11:22:26.310] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:26.310] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:26.311] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.311] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.311] ❌ 例程 0x3003: 负响应 NRC=0x31
[11:22:26.411] 测试例程 0x3003 - 停止例程...
[11:22:26.411] 发送: can0  715   [8]  04 31 02 30 03 55 55 55
[11:22:26.412] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.412] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.412] ❌ 例程 0x3003: 负响应 NRC=0x12
[11:22:26.512] 测试例程 0x3003 - 请求结果...
[11:22:26.512] 发送: can0  715   [8]  04 31 03 30 03 55 55 55
[11:22:26.514] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.514] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.514] ❌ 例程 0x3003: 负响应 NRC=0x12
[11:22:26.614] 测试例程 0x3004 - 启动例程...
[11:22:26.614] 发送: can0  715   [8]  04 31 01 30 04 55 55 55
[11:22:26.615] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.615] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.615] ❌ 例程 0x3004: 负响应 NRC=0x31
[11:22:26.715] 测试例程 0x3004 - 停止例程...
[11:22:26.716] 发送: can0  715   [8]  04 31 02 30 04 55 55 55
[11:22:26.716] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.716] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.716] ❌ 例程 0x3004: 负响应 NRC=0x12
[11:22:26.817] 测试例程 0x3004 - 请求结果...
[11:22:26.817] 发送: can0  715   [8]  04 31 03 30 04 55 55 55
[11:22:26.817] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:26.817] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:26.818] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.818] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:26.818] ❌ 例程 0x3004: 负响应 NRC=0x12
[11:22:26.918] 测试例程 0xF010 - 启动例程...
[11:22:26.919] 发送: can0  715   [8]  04 31 01 F0 10 55 55 55
[11:22:26.919] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.919] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:26.919] ❌ 例程 0xF010: 负响应 NRC=0x31
[11:22:27.019] 测试例程 0xF010 - 停止例程...
[11:22:27.020] 发送: can0  715   [8]  04 31 02 F0 10 55 55 55
[11:22:27.020] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.020] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.020] ❌ 例程 0xF010: 负响应 NRC=0x12
[11:22:27.121] 测试例程 0xF010 - 请求结果...
[11:22:27.121] 发送: can0  715   [8]  04 31 03 F0 10 55 55 55
[11:22:27.121] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.121] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.121] ❌ 例程 0xF010: 负响应 NRC=0x12
[11:22:27.222] 测试例程 0xF011 - 启动例程...
[11:22:27.222] 发送: can0  715   [8]  04 31 01 F0 11 55 55 55
[11:22:27.223] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.223] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.223] ❌ 例程 0xF011: 负响应 NRC=0x31
[11:22:27.324] 测试例程 0xF011 - 停止例程...
[11:22:27.324] 发送: can0  715   [8]  04 31 02 F0 11 55 55 55
[11:22:27.324] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:27.324] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:27.325] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.325] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.325] ❌ 例程 0xF011: 负响应 NRC=0x12
[11:22:27.426] 测试例程 0xF011 - 请求结果...
[11:22:27.426] 发送: can0  715   [8]  04 31 03 F0 11 55 55 55
[11:22:27.426] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.426] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.427] ❌ 例程 0xF011: 负响应 NRC=0x12
[11:22:27.527] 测试例程 0xF012 - 启动例程...
[11:22:27.527] 发送: can0  715   [8]  04 31 01 F0 12 55 55 55
[11:22:27.527] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.528] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.528] ❌ 例程 0xF012: 负响应 NRC=0x31
[11:22:27.628] 测试例程 0xF012 - 停止例程...
[11:22:27.628] 发送: can0  715   [8]  04 31 02 F0 12 55 55 55
[11:22:27.629] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.629] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.629] ❌ 例程 0xF012: 负响应 NRC=0x12
[11:22:27.729] 测试例程 0xF012 - 请求结果...
[11:22:27.729] 发送: can0  715   [8]  04 31 03 F0 12 55 55 55
[11:22:27.730] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.730] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.730] ❌ 例程 0xF012: 负响应 NRC=0x12
[11:22:27.830] 测试例程 0xF013 - 启动例程...
[11:22:27.830] 发送: can0  715   [8]  04 31 01 F0 13 55 55 55
[11:22:27.830] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:27.830] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:27.831] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.831] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:27.831] ❌ 例程 0xF013: 负响应 NRC=0x31
[11:22:27.931] 测试例程 0xF013 - 停止例程...
[11:22:27.931] 发送: can0  715   [8]  04 31 02 F0 13 55 55 55
[11:22:27.932] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.932] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:27.932] ❌ 例程 0xF013: 负响应 NRC=0x12
[11:22:28.032] 测试例程 0xF013 - 请求结果...
[11:22:28.032] 发送: can0  715   [8]  04 31 03 F0 13 55 55 55
[11:22:28.033] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.033] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.033] ❌ 例程 0xF013: 负响应 NRC=0x12
[11:22:28.133] 测试例程 0xF014 - 启动例程...
[11:22:28.133] 发送: can0  715   [8]  04 31 01 F0 14 55 55 55
[11:22:28.135] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.135] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.135] ❌ 例程 0xF014: 负响应 NRC=0x31
[11:22:28.235] 测试例程 0xF014 - 停止例程...
[11:22:28.235] 发送: can0  715   [8]  04 31 02 F0 14 55 55 55
[11:22:28.236] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.236] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.236] ❌ 例程 0xF014: 负响应 NRC=0x12
[11:22:28.336] 测试例程 0xF014 - 请求结果...
[11:22:28.336] 发送: can0  715   [8]  04 31 03 F0 14 55 55 55
[11:22:28.336] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:28.336] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:28.337] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.337] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.337] ❌ 例程 0xF014: 负响应 NRC=0x12
[11:22:28.437] 测试例程 0xF020 - 启动例程...
[11:22:28.437] 发送: can0  715   [8]  04 31 01 F0 20 55 55 55
[11:22:28.438] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.438] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.438] ❌ 例程 0xF020: 负响应 NRC=0x31
[11:22:28.538] 测试例程 0xF020 - 停止例程...
[11:22:28.538] 发送: can0  715   [8]  04 31 02 F0 20 55 55 55
[11:22:28.539] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.539] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.539] ❌ 例程 0xF020: 负响应 NRC=0x12
[11:22:28.639] 测试例程 0xF020 - 请求结果...
[11:22:28.639] 发送: can0  715   [8]  04 31 03 F0 20 55 55 55
[11:22:28.640] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.640] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.640] ❌ 例程 0xF020: 负响应 NRC=0x12
[11:22:28.740] 测试例程 0xF021 - 启动例程...
[11:22:28.740] 发送: can0  715   [8]  04 31 01 F0 21 55 55 55
[11:22:28.741] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.741] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:28.741] ❌ 例程 0xF021: 负响应 NRC=0x31
[11:22:28.841] 测试例程 0xF021 - 停止例程...
[11:22:28.841] 发送: can0  715   [8]  04 31 02 F0 21 55 55 55
[11:22:28.844] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:28.844] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:28.844] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.845] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.845] ❌ 例程 0xF021: 负响应 NRC=0x12
[11:22:28.945] 测试例程 0xF021 - 请求结果...
[11:22:28.945] 发送: can0  715   [8]  04 31 03 F0 21 55 55 55
[11:22:28.946] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.946] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:28.946] ❌ 例程 0xF021: 负响应 NRC=0x12
[11:22:29.046] 测试例程 0xF022 - 启动例程...
[11:22:29.046] 发送: can0  715   [8]  04 31 01 F0 22 55 55 55
[11:22:29.048] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.048] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.048] ❌ 例程 0xF022: 负响应 NRC=0x31
[11:22:29.148] 测试例程 0xF022 - 停止例程...
[11:22:29.148] 发送: can0  715   [8]  04 31 02 F0 22 55 55 55
[11:22:29.150] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.150] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.150] ❌ 例程 0xF022: 负响应 NRC=0x12
[11:22:29.250] 测试例程 0xF022 - 请求结果...
[11:22:29.250] 发送: can0  715   [8]  04 31 03 F0 22 55 55 55
[11:22:29.251] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:29.251] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:29.252] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.252] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.252] ❌ 例程 0xF022: 负响应 NRC=0x12
[11:22:29.352] 测试例程 0xF023 - 启动例程...
[11:22:29.353] 发送: can0  715   [8]  04 31 01 F0 23 55 55 55
[11:22:29.354] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.354] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.354] ❌ 例程 0xF023: 负响应 NRC=0x31
[11:22:29.454] 测试例程 0xF023 - 停止例程...
[11:22:29.454] 发送: can0  715   [8]  04 31 02 F0 23 55 55 55
[11:22:29.455] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.455] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.455] ❌ 例程 0xF023: 负响应 NRC=0x12
[11:22:29.555] 测试例程 0xF023 - 请求结果...
[11:22:29.555] 发送: can0  715   [8]  04 31 03 F0 23 55 55 55
[11:22:29.556] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.556] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.556] ❌ 例程 0xF023: 负响应 NRC=0x12
[11:22:29.656] 测试例程 0xF024 - 启动例程...
[11:22:29.656] 发送: can0  715   [8]  04 31 01 F0 24 55 55 55
[11:22:29.657] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.657] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.657] ❌ 例程 0xF024: 负响应 NRC=0x31
[11:22:29.757] 测试例程 0xF024 - 停止例程...
[11:22:29.758] 发送: can0  715   [8]  04 31 02 F0 24 55 55 55
[11:22:29.758] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:29.758] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:29.759] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.759] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.759] ❌ 例程 0xF024: 负响应 NRC=0x12
[11:22:29.859] 测试例程 0xF024 - 请求结果...
[11:22:29.860] 发送: can0  715   [8]  04 31 03 F0 24 55 55 55
[11:22:29.860] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.860] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:29.860] ❌ 例程 0xF024: 负响应 NRC=0x12
[11:22:29.960] 测试例程 0xF100 - 启动例程...
[11:22:29.961] 发送: can0  715   [8]  04 31 01 F1 00 55 55 55
[11:22:29.961] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.961] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:29.961] ❌ 例程 0xF100: 负响应 NRC=0x31
[11:22:30.061] 测试例程 0xF100 - 停止例程...
[11:22:30.062] 发送: can0  715   [8]  04 31 02 F1 00 55 55 55
[11:22:30.062] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.062] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.062] ❌ 例程 0xF100: 负响应 NRC=0x12
[11:22:30.163] 测试例程 0xF100 - 请求结果...
[11:22:30.163] 发送: can0  715   [8]  04 31 03 F1 00 55 55 55
[11:22:30.163] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.163] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.163] ❌ 例程 0xF100: 负响应 NRC=0x12
[11:22:30.264] 测试例程 0xF101 - 启动例程...
[11:22:30.264] 发送: can0  715   [8]  04 31 01 F1 01 55 55 55
[11:22:30.264] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:30.264] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:30.265] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.265] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.265] ❌ 例程 0xF101: 负响应 NRC=0x31
[11:22:30.366] 测试例程 0xF101 - 停止例程...
[11:22:30.366] 发送: can0  715   [8]  04 31 02 F1 01 55 55 55
[11:22:30.367] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.368] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.368] ❌ 例程 0xF101: 负响应 NRC=0x12
[11:22:30.468] 测试例程 0xF101 - 请求结果...
[11:22:30.468] 发送: can0  715   [8]  04 31 03 F1 01 55 55 55
[11:22:30.469] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.469] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.469] ❌ 例程 0xF101: 负响应 NRC=0x12
[11:22:30.570] 测试例程 0xF102 - 启动例程...
[11:22:30.570] 发送: can0  715   [8]  04 31 01 F1 02 55 55 55
[11:22:30.570] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.570] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.571] ❌ 例程 0xF102: 负响应 NRC=0x31
[11:22:30.671] 测试例程 0xF102 - 停止例程...
[11:22:30.671] 发送: can0  715   [8]  04 31 02 F1 02 55 55 55
[11:22:30.671] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.672] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.672] ❌ 例程 0xF102: 负响应 NRC=0x12
[11:22:30.772] 测试例程 0xF102 - 请求结果...
[11:22:30.772] 发送: can0  715   [8]  04 31 03 F1 02 55 55 55
[11:22:30.772] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:30.772] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:30.773] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.773] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.773] ❌ 例程 0xF102: 负响应 NRC=0x12
[11:22:30.873] 测试例程 0xF103 - 启动例程...
[11:22:30.873] 发送: can0  715   [8]  04 31 01 F1 03 55 55 55
[11:22:30.874] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.874] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:30.874] ❌ 例程 0xF103: 负响应 NRC=0x31
[11:22:30.974] 测试例程 0xF103 - 停止例程...
[11:22:30.974] 发送: can0  715   [8]  04 31 02 F1 03 55 55 55
[11:22:30.976] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.976] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:30.976] ❌ 例程 0xF103: 负响应 NRC=0x12
[11:22:31.076] 测试例程 0xF103 - 请求结果...
[11:22:31.076] 发送: can0  715   [8]  04 31 03 F1 03 55 55 55
[11:22:31.077] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.077] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.077] ❌ 例程 0xF103: 负响应 NRC=0x12
[11:22:31.177] 测试例程 0xF104 - 启动例程...
[11:22:31.177] 发送: can0  715   [8]  04 31 01 F1 04 55 55 55
[11:22:31.178] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.178] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.178] ❌ 例程 0xF104: 负响应 NRC=0x31
[11:22:31.278] 测试例程 0xF104 - 停止例程...
[11:22:31.278] 发送: can0  715   [8]  04 31 02 F1 04 55 55 55
[11:22:31.278] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:31.278] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:31.279] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.279] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.279] ❌ 例程 0xF104: 负响应 NRC=0x12
[11:22:31.379] 测试例程 0xF104 - 请求结果...
[11:22:31.379] 发送: can0  715   [8]  04 31 03 F1 04 55 55 55
[11:22:31.380] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.380] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.380] ❌ 例程 0xF104: 负响应 NRC=0x12
[11:22:31.480] 测试例程 0xF200 - 启动例程...
[11:22:31.480] 发送: can0  715   [8]  04 31 01 F2 00 55 55 55
[11:22:31.481] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.481] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.481] ❌ 例程 0xF200: 负响应 NRC=0x31
[11:22:31.581] 测试例程 0xF200 - 停止例程...
[11:22:31.581] 发送: can0  715   [8]  04 31 02 F2 00 55 55 55
[11:22:31.583] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.583] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.583] ❌ 例程 0xF200: 负响应 NRC=0x12
[11:22:31.683] 测试例程 0xF200 - 请求结果...
[11:22:31.683] 发送: can0  715   [8]  04 31 03 F2 00 55 55 55
[11:22:31.685] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.685] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.685] ❌ 例程 0xF200: 负响应 NRC=0x12
[11:22:31.785] 测试例程 0xF201 - 启动例程...
[11:22:31.785] 发送: can0  715   [8]  04 31 01 F2 01 55 55 55
[11:22:31.785] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:31.785] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:31.786] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.786] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:31.786] ❌ 例程 0xF201: 负响应 NRC=0x31
[11:22:31.886] 测试例程 0xF201 - 停止例程...
[11:22:31.886] 发送: can0  715   [8]  04 31 02 F2 01 55 55 55
[11:22:31.887] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.887] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.887] ❌ 例程 0xF201: 负响应 NRC=0x12
[11:22:31.987] 测试例程 0xF201 - 请求结果...
[11:22:31.987] 发送: can0  715   [8]  04 31 03 F2 01 55 55 55
[11:22:31.989] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.989] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:31.989] ❌ 例程 0xF201: 负响应 NRC=0x12
[11:22:32.089] 测试例程 0xF202 - 启动例程...
[11:22:32.089] 发送: can0  715   [8]  04 31 01 F2 02 55 55 55
[11:22:32.091] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.091] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.091] ❌ 例程 0xF202: 负响应 NRC=0x31
[11:22:32.191] 测试例程 0xF202 - 停止例程...
[11:22:32.191] 发送: can0  715   [8]  04 31 02 F2 02 55 55 55
[11:22:32.193] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.193] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.193] ❌ 例程 0xF202: 负响应 NRC=0x12
[11:22:32.293] 测试例程 0xF202 - 请求结果...
[11:22:32.293] 发送: can0  715   [8]  04 31 03 F2 02 55 55 55
[11:22:32.293] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:32.293] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:32.294] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.294] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.294] ❌ 例程 0xF202: 负响应 NRC=0x12
[11:22:32.394] 测试例程 0xF203 - 启动例程...
[11:22:32.394] 发送: can0  715   [8]  04 31 01 F2 03 55 55 55
[11:22:32.396] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.396] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.396] ❌ 例程 0xF203: 负响应 NRC=0x31
[11:22:32.496] 测试例程 0xF203 - 停止例程...
[11:22:32.496] 发送: can0  715   [8]  04 31 02 F2 03 55 55 55
[11:22:32.497] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.497] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.497] ❌ 例程 0xF203: 负响应 NRC=0x12
[11:22:32.597] 测试例程 0xF203 - 请求结果...
[11:22:32.598] 发送: can0  715   [8]  04 31 03 F2 03 55 55 55
[11:22:32.599] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.599] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.599] ❌ 例程 0xF203: 负响应 NRC=0x12
[11:22:32.699] 测试例程 0xF204 - 启动例程...
[11:22:32.700] 发送: can0  715   [8]  04 31 01 F2 04 55 55 55
[11:22:32.701] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.701] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:32.701] ❌ 例程 0xF204: 负响应 NRC=0x31
[11:22:32.801] 测试例程 0xF204 - 停止例程...
[11:22:32.802] 发送: can0  715   [8]  04 31 02 F2 04 55 55 55
[11:22:32.802] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:32.802] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:32.802] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.802] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.802] ❌ 例程 0xF204: 负响应 NRC=0x12
[11:22:32.902] 测试例程 0xF204 - 请求结果...
[11:22:32.902] 发送: can0  715   [8]  04 31 03 F2 04 55 55 55
[11:22:32.903] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.903] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:32.903] ❌ 例程 0xF204: 负响应 NRC=0x12
[11:22:33.003] 测试例程 0xFF00 - 启动例程...
[11:22:33.004] 发送: can0  715   [8]  04 31 01 FF 00 55 55 55
[11:22:33.004] 接收到帧: can0  795   [8]  03 7F 31 13 55 55 55 55
[11:22:33.004] 接收目标响应: can0  795   [8]  03 7F 31 13 55 55 55 55
[11:22:33.004] ❌ 例程 0xFF00: 负响应 NRC=0x13
[11:22:33.104] 测试例程 0xFF00 - 停止例程...
[11:22:33.105] 发送: can0  715   [8]  04 31 02 FF 00 55 55 55
[11:22:33.106] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.106] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.106] ❌ 例程 0xFF00: 负响应 NRC=0x12
[11:22:33.207] 测试例程 0xFF00 - 请求结果...
[11:22:33.207] 发送: can0  715   [8]  04 31 03 FF 00 55 55 55
[11:22:33.207] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.207] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.207] ❌ 例程 0xFF00: 负响应 NRC=0x12
[11:22:33.308] 测试例程 0xFF01 - 启动例程...
[11:22:33.308] 发送: can0  715   [8]  04 31 01 FF 01 55 55 55
[11:22:33.308] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:33.308] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:33.308] 接收到帧: can0  795   [8]  05 71 01 FF 01 00 55 55
[11:22:33.308] 接收目标响应: can0  795   [8]  05 71 01 FF 01 00 55 55
[11:22:33.308] ✅ 例程 0xFF01 (启动例程): 01 FF 01 00 55 55
[11:22:33.409] 测试例程 0xFF01 - 停止例程...
[11:22:33.409] 发送: can0  715   [8]  04 31 02 FF 01 55 55 55
[11:22:33.409] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.409] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.409] ❌ 例程 0xFF01: 负响应 NRC=0x12
[11:22:33.510] 测试例程 0xFF01 - 请求结果...
[11:22:33.510] 发送: can0  715   [8]  04 31 03 FF 01 55 55 55
[11:22:33.510] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.510] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.510] ❌ 例程 0xFF01: 负响应 NRC=0x12
[11:22:33.611] 测试例程 0xFF02 - 启动例程...
[11:22:33.611] 发送: can0  715   [8]  04 31 01 FF 02 55 55 55
[11:22:33.611] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:33.611] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:33.611] ❌ 例程 0xFF02: 负响应 NRC=0x31
[11:22:33.712] 测试例程 0xFF02 - 停止例程...
[11:22:33.712] 发送: can0  715   [8]  04 31 02 FF 02 55 55 55
[11:22:33.713] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.713] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.713] ❌ 例程 0xFF02: 负响应 NRC=0x12
[11:22:33.813] 测试例程 0xFF02 - 请求结果...
[11:22:33.813] 发送: can0  715   [8]  04 31 03 FF 02 55 55 55
[11:22:33.813] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:33.813] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:33.814] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.814] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:33.814] ❌ 例程 0xFF02: 负响应 NRC=0x12
[11:22:33.914] 测试例程 0xFF03 - 启动例程...
[11:22:33.914] 发送: can0  715   [8]  04 31 01 FF 03 55 55 55
[11:22:33.915] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:33.915] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:33.915] ❌ 例程 0xFF03: 负响应 NRC=0x31
[11:22:34.015] 测试例程 0xFF03 - 停止例程...
[11:22:34.015] 发送: can0  715   [8]  04 31 02 FF 03 55 55 55
[11:22:34.017] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.017] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.017] ❌ 例程 0xFF03: 负响应 NRC=0x12
[11:22:34.117] 测试例程 0xFF03 - 请求结果...
[11:22:34.117] 发送: can0  715   [8]  04 31 03 FF 03 55 55 55
[11:22:34.119] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.119] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.119] ❌ 例程 0xFF03: 负响应 NRC=0x12
[11:22:34.219] 测试例程 0xFF04 - 启动例程...
[11:22:34.219] 发送: can0  715   [8]  04 31 01 FF 04 55 55 55
[11:22:34.220] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.220] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.220] ❌ 例程 0xFF04: 负响应 NRC=0x31
[11:22:34.320] 测试例程 0xFF04 - 停止例程...
[11:22:34.320] 发送: can0  715   [8]  04 31 02 FF 04 55 55 55
[11:22:34.320] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:34.320] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:34.321] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.321] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.321] ❌ 例程 0xFF04: 负响应 NRC=0x12
[11:22:34.421] 测试例程 0xFF04 - 请求结果...
[11:22:34.421] 发送: can0  715   [8]  04 31 03 FF 04 55 55 55
[11:22:34.422] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.422] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.422] ❌ 例程 0xFF04: 负响应 NRC=0x12
[11:22:34.522] 测试例程 0xFF10 - 启动例程...
[11:22:34.522] 发送: can0  715   [8]  04 31 01 FF 10 55 55 55
[11:22:34.523] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.523] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.523] ❌ 例程 0xFF10: 负响应 NRC=0x31
[11:22:34.623] 测试例程 0xFF10 - 停止例程...
[11:22:34.623] 发送: can0  715   [8]  04 31 02 FF 10 55 55 55
[11:22:34.624] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.624] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.624] ❌ 例程 0xFF10: 负响应 NRC=0x12
[11:22:34.724] 测试例程 0xFF10 - 请求结果...
[11:22:34.724] 发送: can0  715   [8]  04 31 03 FF 10 55 55 55
[11:22:34.725] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.725] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.725] ❌ 例程 0xFF10: 负响应 NRC=0x12
[11:22:34.825] 测试例程 0xFF11 - 启动例程...
[11:22:34.825] 发送: can0  715   [8]  04 31 01 FF 11 55 55 55
[11:22:34.825] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:34.825] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:34.827] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.827] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:34.827] ❌ 例程 0xFF11: 负响应 NRC=0x31
[11:22:34.927] 测试例程 0xFF11 - 停止例程...
[11:22:34.927] 发送: can0  715   [8]  04 31 02 FF 11 55 55 55
[11:22:34.929] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.929] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:34.929] ❌ 例程 0xFF11: 负响应 NRC=0x12
[11:22:35.029] 测试例程 0xFF11 - 请求结果...
[11:22:35.029] 发送: can0  715   [8]  04 31 03 FF 11 55 55 55
[11:22:35.030] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.030] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.030] ❌ 例程 0xFF11: 负响应 NRC=0x12
[11:22:35.130] 测试例程 0xFF12 - 启动例程...
[11:22:35.130] 发送: can0  715   [8]  04 31 01 FF 12 55 55 55
[11:22:35.133] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.133] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.133] ❌ 例程 0xFF12: 负响应 NRC=0x31
[11:22:35.234] 测试例程 0xFF12 - 停止例程...
[11:22:35.234] 发送: can0  715   [8]  04 31 02 FF 12 55 55 55
[11:22:35.235] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.235] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.235] ❌ 例程 0xFF12: 负响应 NRC=0x12
[11:22:35.335] 测试例程 0xFF12 - 请求结果...
[11:22:35.335] 发送: can0  715   [8]  04 31 03 FF 12 55 55 55
[11:22:35.335] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:35.335] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:35.337] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.337] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.337] ❌ 例程 0xFF12: 负响应 NRC=0x12
[11:22:35.437] 测试例程 0xFF13 - 启动例程...
[11:22:35.437] 发送: can0  715   [8]  04 31 01 FF 13 55 55 55
[11:22:35.438] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.438] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.438] ❌ 例程 0xFF13: 负响应 NRC=0x31
[11:22:35.538] 测试例程 0xFF13 - 停止例程...
[11:22:35.538] 发送: can0  715   [8]  04 31 02 FF 13 55 55 55
[11:22:35.539] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.539] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.539] ❌ 例程 0xFF13: 负响应 NRC=0x12
[11:22:35.639] 测试例程 0xFF13 - 请求结果...
[11:22:35.640] 发送: can0  715   [8]  04 31 03 FF 13 55 55 55
[11:22:35.641] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.641] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.641] ❌ 例程 0xFF13: 负响应 NRC=0x12
[11:22:35.741] 测试例程 0xFF14 - 启动例程...
[11:22:35.742] 发送: can0  715   [8]  04 31 01 FF 14 55 55 55
[11:22:35.743] 接收到帧: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.743] 接收目标响应: can0  795   [8]  03 7F 31 31 55 55 55 55
[11:22:35.743] ❌ 例程 0xFF14: 负响应 NRC=0x31
[11:22:35.843] 测试例程 0xFF14 - 停止例程...
[11:22:35.843] 发送: can0  715   [8]  04 31 02 FF 14 55 55 55
[11:22:35.844] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:22:35.844] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:22:35.844] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.844] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.844] ❌ 例程 0xFF14: 负响应 NRC=0x12
[11:22:35.944] 测试例程 0xFF14 - 请求结果...
[11:22:35.945] 发送: can0  715   [8]  04 31 03 FF 14 55 55 55
[11:22:35.945] 接收到帧: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.945] 接收目标响应: can0  795   [8]  03 7F 31 12 55 55 55 55
[11:22:35.945] ❌ 例程 0xFF14: 负响应 NRC=0x12
[11:22:36.046] 有效例程: 0x0203 子功能0x01 = 01 02 03 00 55 55
[11:22:36.046] 有效例程: 0xFF01 子功能0x01 = 01 FF 01 00 55 55
[11:22:36.046] 
例程扫描统计:
[11:22:36.046] 总测试例程数: 210
[11:22:36.046] 成功例程数: 2
[11:22:36.046] 失败例程数: 208
[11:22:36.046] 成功率: 0.95%
