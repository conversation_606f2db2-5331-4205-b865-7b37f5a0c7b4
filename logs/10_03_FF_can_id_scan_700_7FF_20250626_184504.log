FF数据CAN ID扫描 - 2025-06-26 18:45:04.964471
扫描范围: 0x700 - 0x7FF
周期CAN ID: 0x3A1 (将被忽略)
UDS请求ID: 0x715
UDS响应ID: 0x795
测试数据: FF FF FF FF FF FF FF FF
CAN接口: can0
============================================================

[18:45:04.964] 执行UDS 10 01 (默认会话)...
[18:45:04.964] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[18:45:04.965] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:45:04.965] 接收UDS响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[18:45:04.965] ✅ UDS 10 01 成功
[18:45:05.466] 执行UDS 10 03 (扩展会话)...
[18:45:05.466] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[18:45:05.466] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:05.466] 忽略非UDS响应帧: 0x3A1 (期望0x795)
[18:45:05.467] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[18:45:05.467] 接收UDS响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[18:45:05.467] ✅ UDS 10 03 成功
[18:45:05.968] 发送: can0  700   [8]  FF FF FF FF FF FF FF FF
[18:45:05.968] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:05.968] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:06.276] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:06.276] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:06.497] 发送: can0  701   [8]  FF FF FF FF FF FF FF FF
[18:45:06.776] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:06.776] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:07.047] 发送: can0  702   [8]  FF FF FF FF FF FF FF FF
[18:45:07.277] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:07.277] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:07.597] 发送: can0  703   [8]  FF FF FF FF FF FF FF FF
[18:45:07.777] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:07.777] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:08.148] 发送: can0  704   [8]  FF FF FF FF FF FF FF FF
[18:45:08.277] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:08.277] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:08.698] 发送: can0  705   [8]  FF FF FF FF FF FF FF FF
[18:45:08.778] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:08.778] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:09.249] 发送: can0  706   [8]  FF FF FF FF FF FF FF FF
[18:45:09.278] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:09.278] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:09.778] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:09.778] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:09.798] 发送: can0  707   [8]  FF FF FF FF FF FF FF FF
[18:45:10.278] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:10.279] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:10.349] 发送: can0  708   [8]  FF FF FF FF FF FF FF FF
[18:45:10.779] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:10.779] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:10.899] 发送: can0  709   [8]  FF FF FF FF FF FF FF FF
[18:45:11.279] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:11.279] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:11.450] 发送: can0  70A   [8]  FF FF FF FF FF FF FF FF
[18:45:11.779] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:11.780] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:12.000] 发送: can0  70B   [8]  FF FF FF FF FF FF FF FF
[18:45:12.280] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:12.280] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:12.551] 发送: can0  70C   [8]  FF FF FF FF FF FF FF FF
[18:45:12.780] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:12.780] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:13.101] 发送: can0  70D   [8]  FF FF FF FF FF FF FF FF
[18:45:13.280] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:13.280] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:13.651] 发送: can0  70E   [8]  FF FF FF FF FF FF FF FF
[18:45:13.781] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:13.781] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:14.202] 发送: can0  70F   [8]  FF FF FF FF FF FF FF FF
[18:45:14.281] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:14.281] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:14.752] 发送: can0  710   [8]  FF FF FF FF FF FF FF FF
[18:45:14.781] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:14.781] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:15.282] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:15.282] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:15.302] 发送: can0  711   [8]  FF FF FF FF FF FF FF FF
[18:45:15.782] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:15.782] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:15.856] 发送: can0  712   [8]  FF FF FF FF FF FF FF FF
[18:45:16.282] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:16.282] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:16.403] 发送: can0  713   [8]  FF FF FF FF FF FF FF FF
[18:45:16.783] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:16.783] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:16.953] 发送: can0  714   [8]  FF FF FF FF FF FF FF FF
[18:45:17.283] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:17.283] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:17.504] 发送: can0  715   [8]  FF FF FF FF FF FF FF FF
[18:45:17.783] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:17.783] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:18.054] 发送: can0  716   [8]  FF FF FF FF FF FF FF FF
[18:45:18.284] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:18.284] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:18.604] 发送: can0  717   [8]  FF FF FF FF FF FF FF FF
[18:45:18.784] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[18:45:18.784] 忽略周期性帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
