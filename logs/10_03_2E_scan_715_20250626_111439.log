UDS 2E服务写数据标识符扫描 - 2025-06-26 11:14:39.815850
请求CAN ID: 0x715
响应CAN ID: 0x795
测试DID数量: 95
测试数据模式: 12
============================================================

[11:14:39.815] 执行UDS 10 01 (默认会话)...
[11:14:39.815] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[11:14:39.817] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[11:14:39.817] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[11:14:39.817] ✅ UDS 10 01 成功
[11:14:40.317] 执行UDS 10 03 (扩展会话)...
[11:14:40.317] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[11:14:40.317] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:40.317] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:40.319] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[11:14:40.319] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[11:14:40.319] ✅ UDS 10 03 成功
[11:14:40.819] 测试写DID 0x0001, 数据: 00...
[11:14:40.819] 发送: can0  715   [8]  04 2E 00 01 00 55 55 55
[11:14:40.820] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:40.820] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:40.821] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.821] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.821] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:40.871] 测试写DID 0x0001, 数据: 01...
[11:14:40.871] 发送: can0  715   [8]  04 2E 00 01 01 55 55 55
[11:14:40.872] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.872] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.872] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:40.922] 测试写DID 0x0001, 数据: FF...
[11:14:40.922] 发送: can0  715   [8]  04 2E 00 01 FF 55 55 55
[11:14:40.924] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.924] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.924] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:40.974] 测试写DID 0x0001, 数据: 00 00...
[11:14:40.974] 发送: can0  715   [8]  05 2E 00 01 00 00 55 55
[11:14:40.975] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.975] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:40.975] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.025] 测试写DID 0x0001, 数据: 00 01...
[11:14:41.025] 发送: can0  715   [8]  05 2E 00 01 00 01 55 55
[11:14:41.027] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.027] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.027] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.077] 测试写DID 0x0001, 数据: FF FF...
[11:14:41.077] 发送: can0  715   [8]  05 2E 00 01 FF FF 55 55
[11:14:41.078] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.078] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.078] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.128] 测试写DID 0x0001, 数据: 12 34...
[11:14:41.128] 发送: can0  715   [8]  05 2E 00 01 12 34 55 55
[11:14:41.128] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:41.128] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:41.129] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.129] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.129] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.179] 测试写DID 0x0001, 数据: 00 00 00...
[11:14:41.179] 发送: can0  715   [8]  06 2E 00 01 00 00 00 55
[11:14:41.180] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.180] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.180] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.230] 测试写DID 0x0001, 数据: 01 02 03...
[11:14:41.230] 发送: can0  715   [8]  06 2E 00 01 01 02 03 55
[11:14:41.231] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.231] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.231] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.281] 测试写DID 0x0001, 数据: 00 00 00 00...
[11:14:41.281] 发送: can0  715   [8]  07 2E 00 01 00 00 00 00
[11:14:41.283] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.283] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.283] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.333] 测试写DID 0x0001, 数据: 12 34 56 78...
[11:14:41.333] 发送: can0  715   [8]  07 2E 00 01 12 34 56 78
[11:14:41.335] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.335] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.335] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.385] 测试写DID 0x0001, 数据: FF FF FF FF...
[11:14:41.385] 发送: can0  715   [8]  07 2E 00 01 FF FF FF FF
[11:14:41.387] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.387] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.387] ❌ DID 0x0001: 负响应 NRC=0x13
[11:14:41.437] 测试写DID 0x0002, 数据: 00...
[11:14:41.437] 发送: can0  715   [8]  04 2E 00 02 00 55 55 55
[11:14:41.439] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.439] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.439] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.489] 测试写DID 0x0002, 数据: 01...
[11:14:41.489] 发送: can0  715   [8]  04 2E 00 02 01 55 55 55
[11:14:41.490] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.490] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.490] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.540] 测试写DID 0x0002, 数据: FF...
[11:14:41.540] 发送: can0  715   [8]  04 2E 00 02 FF 55 55 55
[11:14:41.541] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.541] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.541] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.591] 测试写DID 0x0002, 数据: 00 00...
[11:14:41.591] 发送: can0  715   [8]  05 2E 00 02 00 00 55 55
[11:14:41.592] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.592] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.592] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.642] 测试写DID 0x0002, 数据: 00 01...
[11:14:41.642] 发送: can0  715   [8]  05 2E 00 02 00 01 55 55
[11:14:41.642] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:41.642] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:41.643] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.643] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.643] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.693] 测试写DID 0x0002, 数据: FF FF...
[11:14:41.693] 发送: can0  715   [8]  05 2E 00 02 FF FF 55 55
[11:14:41.694] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.694] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.694] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.744] 测试写DID 0x0002, 数据: 12 34...
[11:14:41.744] 发送: can0  715   [8]  05 2E 00 02 12 34 55 55
[11:14:41.745] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.745] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.745] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.795] 测试写DID 0x0002, 数据: 00 00 00...
[11:14:41.795] 发送: can0  715   [8]  06 2E 00 02 00 00 00 55
[11:14:41.797] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.797] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.797] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.847] 测试写DID 0x0002, 数据: 01 02 03...
[11:14:41.847] 发送: can0  715   [8]  06 2E 00 02 01 02 03 55
[11:14:41.849] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.849] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.849] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.899] 测试写DID 0x0002, 数据: 00 00 00 00...
[11:14:41.899] 发送: can0  715   [8]  07 2E 00 02 00 00 00 00
[11:14:41.901] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.901] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.901] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:41.951] 测试写DID 0x0002, 数据: 12 34 56 78...
[11:14:41.951] 发送: can0  715   [8]  07 2E 00 02 12 34 56 78
[11:14:41.952] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.952] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:41.952] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:42.002] 测试写DID 0x0002, 数据: FF FF FF FF...
[11:14:42.002] 发送: can0  715   [8]  07 2E 00 02 FF FF FF FF
[11:14:42.003] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.003] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.003] ❌ DID 0x0002: 负响应 NRC=0x13
[11:14:42.053] 测试写DID 0x0003, 数据: 00...
[11:14:42.053] 发送: can0  715   [8]  04 2E 00 03 00 55 55 55
[11:14:42.054] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.054] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.054] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.104] 测试写DID 0x0003, 数据: 01...
[11:14:42.104] 发送: can0  715   [8]  04 2E 00 03 01 55 55 55
[11:14:42.105] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.105] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.105] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.155] 测试写DID 0x0003, 数据: FF...
[11:14:42.155] 发送: can0  715   [8]  04 2E 00 03 FF 55 55 55
[11:14:42.155] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:42.155] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:42.157] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.157] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.157] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.207] 测试写DID 0x0003, 数据: 00 00...
[11:14:42.207] 发送: can0  715   [8]  05 2E 00 03 00 00 55 55
[11:14:42.209] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.209] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.209] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.259] 测试写DID 0x0003, 数据: 00 01...
[11:14:42.259] 发送: can0  715   [8]  05 2E 00 03 00 01 55 55
[11:14:42.260] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.260] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.260] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.310] 测试写DID 0x0003, 数据: FF FF...
[11:14:42.310] 发送: can0  715   [8]  05 2E 00 03 FF FF 55 55
[11:14:42.311] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.311] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.311] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.361] 测试写DID 0x0003, 数据: 12 34...
[11:14:42.361] 发送: can0  715   [8]  05 2E 00 03 12 34 55 55
[11:14:42.362] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.362] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.362] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.412] 测试写DID 0x0003, 数据: 00 00 00...
[11:14:42.412] 发送: can0  715   [8]  06 2E 00 03 00 00 00 55
[11:14:42.413] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.413] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.413] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.463] 测试写DID 0x0003, 数据: 01 02 03...
[11:14:42.463] 发送: can0  715   [8]  06 2E 00 03 01 02 03 55
[11:14:42.464] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.464] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.464] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.514] 测试写DID 0x0003, 数据: 00 00 00 00...
[11:14:42.514] 发送: can0  715   [8]  07 2E 00 03 00 00 00 00
[11:14:42.515] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.515] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.515] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.565] 测试写DID 0x0003, 数据: 12 34 56 78...
[11:14:42.565] 发送: can0  715   [8]  07 2E 00 03 12 34 56 78
[11:14:42.567] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.567] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.567] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.617] 测试写DID 0x0003, 数据: FF FF FF FF...
[11:14:42.617] 发送: can0  715   [8]  07 2E 00 03 FF FF FF FF
[11:14:42.617] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:42.617] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:42.618] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.618] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.618] ❌ DID 0x0003: 负响应 NRC=0x13
[11:14:42.668] 测试写DID 0x0004, 数据: 00...
[11:14:42.668] 发送: can0  715   [8]  04 2E 00 04 00 55 55 55
[11:14:42.669] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.669] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.669] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.719] 测试写DID 0x0004, 数据: 01...
[11:14:42.719] 发送: can0  715   [8]  04 2E 00 04 01 55 55 55
[11:14:42.720] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.720] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.720] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.770] 测试写DID 0x0004, 数据: FF...
[11:14:42.770] 发送: can0  715   [8]  04 2E 00 04 FF 55 55 55
[11:14:42.771] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.771] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.771] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.821] 测试写DID 0x0004, 数据: 00 00...
[11:14:42.821] 发送: can0  715   [8]  05 2E 00 04 00 00 55 55
[11:14:42.822] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.822] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.822] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.872] 测试写DID 0x0004, 数据: 00 01...
[11:14:42.872] 发送: can0  715   [8]  05 2E 00 04 00 01 55 55
[11:14:42.873] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.873] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.873] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.923] 测试写DID 0x0004, 数据: FF FF...
[11:14:42.923] 发送: can0  715   [8]  05 2E 00 04 FF FF 55 55
[11:14:42.924] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.924] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.924] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:42.974] 测试写DID 0x0004, 数据: 12 34...
[11:14:42.974] 发送: can0  715   [8]  05 2E 00 04 12 34 55 55
[11:14:42.976] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.976] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:42.976] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.026] 测试写DID 0x0004, 数据: 00 00 00...
[11:14:43.026] 发送: can0  715   [8]  06 2E 00 04 00 00 00 55
[11:14:43.027] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.027] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.027] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.077] 测试写DID 0x0004, 数据: 01 02 03...
[11:14:43.077] 发送: can0  715   [8]  06 2E 00 04 01 02 03 55
[11:14:43.078] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.078] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.078] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.128] 测试写DID 0x0004, 数据: 00 00 00 00...
[11:14:43.128] 发送: can0  715   [8]  07 2E 00 04 00 00 00 00
[11:14:43.128] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:43.128] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:43.129] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.129] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.129] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.179] 测试写DID 0x0004, 数据: 12 34 56 78...
[11:14:43.179] 发送: can0  715   [8]  07 2E 00 04 12 34 56 78
[11:14:43.180] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.180] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.180] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.230] 测试写DID 0x0004, 数据: FF FF FF FF...
[11:14:43.230] 发送: can0  715   [8]  07 2E 00 04 FF FF FF FF
[11:14:43.231] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.231] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.231] ❌ DID 0x0004: 负响应 NRC=0x13
[11:14:43.281] 测试写DID 0x0005, 数据: 00...
[11:14:43.281] 发送: can0  715   [8]  04 2E 00 05 00 55 55 55
[11:14:43.282] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.282] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.282] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.332] 测试写DID 0x0005, 数据: 01...
[11:14:43.332] 发送: can0  715   [8]  04 2E 00 05 01 55 55 55
[11:14:43.333] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.333] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.333] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.383] 测试写DID 0x0005, 数据: FF...
[11:14:43.383] 发送: can0  715   [8]  04 2E 00 05 FF 55 55 55
[11:14:43.384] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.384] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.384] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.434] 测试写DID 0x0005, 数据: 00 00...
[11:14:43.434] 发送: can0  715   [8]  05 2E 00 05 00 00 55 55
[11:14:43.435] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.435] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.435] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.485] 测试写DID 0x0005, 数据: 00 01...
[11:14:43.485] 发送: can0  715   [8]  05 2E 00 05 00 01 55 55
[11:14:43.487] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.487] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.487] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.537] 测试写DID 0x0005, 数据: FF FF...
[11:14:43.537] 发送: can0  715   [8]  05 2E 00 05 FF FF 55 55
[11:14:43.538] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.538] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.538] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.588] 测试写DID 0x0005, 数据: 12 34...
[11:14:43.588] 发送: can0  715   [8]  05 2E 00 05 12 34 55 55
[11:14:43.590] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.590] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.590] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.640] 测试写DID 0x0005, 数据: 00 00 00...
[11:14:43.640] 发送: can0  715   [8]  06 2E 00 05 00 00 00 55
[11:14:43.640] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:43.640] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:43.642] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.642] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.642] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.692] 测试写DID 0x0005, 数据: 01 02 03...
[11:14:43.692] 发送: can0  715   [8]  06 2E 00 05 01 02 03 55
[11:14:43.693] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.693] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.693] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.743] 测试写DID 0x0005, 数据: 00 00 00 00...
[11:14:43.743] 发送: can0  715   [8]  07 2E 00 05 00 00 00 00
[11:14:43.744] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.744] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.744] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.794] 测试写DID 0x0005, 数据: 12 34 56 78...
[11:14:43.794] 发送: can0  715   [8]  07 2E 00 05 12 34 56 78
[11:14:43.795] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.795] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.795] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.845] 测试写DID 0x0005, 数据: FF FF FF FF...
[11:14:43.845] 发送: can0  715   [8]  07 2E 00 05 FF FF FF FF
[11:14:43.847] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.847] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.847] ❌ DID 0x0005: 负响应 NRC=0x13
[11:14:43.897] 测试写DID 0x0010, 数据: 00...
[11:14:43.897] 发送: can0  715   [8]  04 2E 00 10 00 55 55 55
[11:14:43.899] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.899] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.899] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:43.949] 测试写DID 0x0010, 数据: 01...
[11:14:43.949] 发送: can0  715   [8]  04 2E 00 10 01 55 55 55
[11:14:43.950] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.950] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:43.950] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.000] 测试写DID 0x0010, 数据: FF...
[11:14:44.000] 发送: can0  715   [8]  04 2E 00 10 FF 55 55 55
[11:14:44.001] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.001] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.001] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.051] 测试写DID 0x0010, 数据: 00 00...
[11:14:44.051] 发送: can0  715   [8]  05 2E 00 10 00 00 55 55
[11:14:44.052] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.052] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.052] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.102] 测试写DID 0x0010, 数据: 00 01...
[11:14:44.102] 发送: can0  715   [8]  05 2E 00 10 00 01 55 55
[11:14:44.103] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.103] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.103] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.153] 测试写DID 0x0010, 数据: FF FF...
[11:14:44.153] 发送: can0  715   [8]  05 2E 00 10 FF FF 55 55
[11:14:44.153] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:44.153] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:44.154] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.154] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.154] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.204] 测试写DID 0x0010, 数据: 12 34...
[11:14:44.204] 发送: can0  715   [8]  05 2E 00 10 12 34 55 55
[11:14:44.205] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.205] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.205] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.255] 测试写DID 0x0010, 数据: 00 00 00...
[11:14:44.255] 发送: can0  715   [8]  06 2E 00 10 00 00 00 55
[11:14:44.257] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.257] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.257] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.307] 测试写DID 0x0010, 数据: 01 02 03...
[11:14:44.307] 发送: can0  715   [8]  06 2E 00 10 01 02 03 55
[11:14:44.308] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.308] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.308] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.358] 测试写DID 0x0010, 数据: 00 00 00 00...
[11:14:44.358] 发送: can0  715   [8]  07 2E 00 10 00 00 00 00
[11:14:44.359] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.359] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.359] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.409] 测试写DID 0x0010, 数据: 12 34 56 78...
[11:14:44.409] 发送: can0  715   [8]  07 2E 00 10 12 34 56 78
[11:14:44.413] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.413] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.413] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.463] 测试写DID 0x0010, 数据: FF FF FF FF...
[11:14:44.463] 发送: can0  715   [8]  07 2E 00 10 FF FF FF FF
[11:14:44.464] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.464] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.464] ❌ DID 0x0010: 负响应 NRC=0x13
[11:14:44.514] 测试写DID 0x0011, 数据: 00...
[11:14:44.514] 发送: can0  715   [8]  04 2E 00 11 00 55 55 55
[11:14:44.515] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.515] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.515] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.565] 测试写DID 0x0011, 数据: 01...
[11:14:44.565] 发送: can0  715   [8]  04 2E 00 11 01 55 55 55
[11:14:44.567] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.567] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.567] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.617] 测试写DID 0x0011, 数据: FF...
[11:14:44.617] 发送: can0  715   [8]  04 2E 00 11 FF 55 55 55
[11:14:44.617] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:44.617] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:44.618] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.618] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.618] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.668] 测试写DID 0x0011, 数据: 00 00...
[11:14:44.668] 发送: can0  715   [8]  05 2E 00 11 00 00 55 55
[11:14:44.669] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.669] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.669] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.719] 测试写DID 0x0011, 数据: 00 01...
[11:14:44.719] 发送: can0  715   [8]  05 2E 00 11 00 01 55 55
[11:14:44.720] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.720] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.720] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.770] 测试写DID 0x0011, 数据: FF FF...
[11:14:44.770] 发送: can0  715   [8]  05 2E 00 11 FF FF 55 55
[11:14:44.772] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.772] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.772] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.822] 测试写DID 0x0011, 数据: 12 34...
[11:14:44.822] 发送: can0  715   [8]  05 2E 00 11 12 34 55 55
[11:14:44.823] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.823] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.823] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.873] 测试写DID 0x0011, 数据: 00 00 00...
[11:14:44.873] 发送: can0  715   [8]  06 2E 00 11 00 00 00 55
[11:14:44.874] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.874] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.874] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.924] 测试写DID 0x0011, 数据: 01 02 03...
[11:14:44.924] 发送: can0  715   [8]  06 2E 00 11 01 02 03 55
[11:14:44.925] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.925] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.925] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:44.975] 测试写DID 0x0011, 数据: 00 00 00 00...
[11:14:44.975] 发送: can0  715   [8]  07 2E 00 11 00 00 00 00
[11:14:44.977] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.977] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:44.977] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:45.027] 测试写DID 0x0011, 数据: 12 34 56 78...
[11:14:45.027] 发送: can0  715   [8]  07 2E 00 11 12 34 56 78
[11:14:45.028] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.028] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.028] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:45.078] 测试写DID 0x0011, 数据: FF FF FF FF...
[11:14:45.078] 发送: can0  715   [8]  07 2E 00 11 FF FF FF FF
[11:14:45.079] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.079] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.079] ❌ DID 0x0011: 负响应 NRC=0x13
[11:14:45.129] 测试写DID 0x0012, 数据: 00...
[11:14:45.129] 发送: can0  715   [8]  04 2E 00 12 00 55 55 55
[11:14:45.129] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:45.129] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:45.130] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.130] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.130] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.180] 测试写DID 0x0012, 数据: 01...
[11:14:45.180] 发送: can0  715   [8]  04 2E 00 12 01 55 55 55
[11:14:45.181] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.181] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.181] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.231] 测试写DID 0x0012, 数据: FF...
[11:14:45.231] 发送: can0  715   [8]  04 2E 00 12 FF 55 55 55
[11:14:45.232] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.232] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.232] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.282] 测试写DID 0x0012, 数据: 00 00...
[11:14:45.282] 发送: can0  715   [8]  05 2E 00 12 00 00 55 55
[11:14:45.284] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.284] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.284] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.334] 测试写DID 0x0012, 数据: 00 01...
[11:14:45.334] 发送: can0  715   [8]  05 2E 00 12 00 01 55 55
[11:14:45.335] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.335] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.335] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.385] 测试写DID 0x0012, 数据: FF FF...
[11:14:45.385] 发送: can0  715   [8]  05 2E 00 12 FF FF 55 55
[11:14:45.387] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.387] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.387] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.437] 测试写DID 0x0012, 数据: 12 34...
[11:14:45.437] 发送: can0  715   [8]  05 2E 00 12 12 34 55 55
[11:14:45.439] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.439] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.439] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.489] 测试写DID 0x0012, 数据: 00 00 00...
[11:14:45.489] 发送: can0  715   [8]  06 2E 00 12 00 00 00 55
[11:14:45.491] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.491] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.491] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.541] 测试写DID 0x0012, 数据: 01 02 03...
[11:14:45.541] 发送: can0  715   [8]  06 2E 00 12 01 02 03 55
[11:14:45.543] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.543] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.543] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.593] 测试写DID 0x0012, 数据: 00 00 00 00...
[11:14:45.593] 发送: can0  715   [8]  07 2E 00 12 00 00 00 00
[11:14:45.595] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.595] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.595] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.645] 测试写DID 0x0012, 数据: 12 34 56 78...
[11:14:45.645] 发送: can0  715   [8]  07 2E 00 12 12 34 56 78
[11:14:45.645] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:45.645] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:45.647] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.647] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.647] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.697] 测试写DID 0x0012, 数据: FF FF FF FF...
[11:14:45.697] 发送: can0  715   [8]  07 2E 00 12 FF FF FF FF
[11:14:45.698] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.698] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.698] ❌ DID 0x0012: 负响应 NRC=0x13
[11:14:45.748] 测试写DID 0x0013, 数据: 00...
[11:14:45.748] 发送: can0  715   [8]  04 2E 00 13 00 55 55 55
[11:14:45.750] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.750] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.750] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:45.800] 测试写DID 0x0013, 数据: 01...
[11:14:45.800] 发送: can0  715   [8]  04 2E 00 13 01 55 55 55
[11:14:45.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.802] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:45.852] 测试写DID 0x0013, 数据: FF...
[11:14:45.852] 发送: can0  715   [8]  04 2E 00 13 FF 55 55 55
[11:14:45.853] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.853] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.853] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:45.903] 测试写DID 0x0013, 数据: 00 00...
[11:14:45.903] 发送: can0  715   [8]  05 2E 00 13 00 00 55 55
[11:14:45.904] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.904] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.904] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:45.954] 测试写DID 0x0013, 数据: 00 01...
[11:14:45.954] 发送: can0  715   [8]  05 2E 00 13 00 01 55 55
[11:14:45.956] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.956] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:45.956] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.006] 测试写DID 0x0013, 数据: FF FF...
[11:14:46.006] 发送: can0  715   [8]  05 2E 00 13 FF FF 55 55
[11:14:46.007] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.007] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.007] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.057] 测试写DID 0x0013, 数据: 12 34...
[11:14:46.057] 发送: can0  715   [8]  05 2E 00 13 12 34 55 55
[11:14:46.058] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.058] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.058] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.108] 测试写DID 0x0013, 数据: 00 00 00...
[11:14:46.108] 发送: can0  715   [8]  06 2E 00 13 00 00 00 55
[11:14:46.109] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.109] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.109] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.159] 测试写DID 0x0013, 数据: 01 02 03...
[11:14:46.159] 发送: can0  715   [8]  06 2E 00 13 01 02 03 55
[11:14:46.159] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:46.159] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:46.160] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.160] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.160] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.210] 测试写DID 0x0013, 数据: 00 00 00 00...
[11:14:46.210] 发送: can0  715   [8]  07 2E 00 13 00 00 00 00
[11:14:46.211] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.211] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.211] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.261] 测试写DID 0x0013, 数据: 12 34 56 78...
[11:14:46.261] 发送: can0  715   [8]  07 2E 00 13 12 34 56 78
[11:14:46.262] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.262] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.262] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.312] 测试写DID 0x0013, 数据: FF FF FF FF...
[11:14:46.312] 发送: can0  715   [8]  07 2E 00 13 FF FF FF FF
[11:14:46.313] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.313] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.313] ❌ DID 0x0013: 负响应 NRC=0x13
[11:14:46.363] 测试写DID 0x0014, 数据: 00...
[11:14:46.363] 发送: can0  715   [8]  04 2E 00 14 00 55 55 55
[11:14:46.364] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.364] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.364] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.414] 测试写DID 0x0014, 数据: 01...
[11:14:46.414] 发送: can0  715   [8]  04 2E 00 14 01 55 55 55
[11:14:46.415] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.415] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.415] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.465] 测试写DID 0x0014, 数据: FF...
[11:14:46.465] 发送: can0  715   [8]  04 2E 00 14 FF 55 55 55
[11:14:46.467] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.467] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.467] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.517] 测试写DID 0x0014, 数据: 00 00...
[11:14:46.517] 发送: can0  715   [8]  05 2E 00 14 00 00 55 55
[11:14:46.518] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.518] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.518] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.568] 测试写DID 0x0014, 数据: 00 01...
[11:14:46.568] 发送: can0  715   [8]  05 2E 00 14 00 01 55 55
[11:14:46.569] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.569] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.569] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.619] 测试写DID 0x0014, 数据: FF FF...
[11:14:46.619] 发送: can0  715   [8]  05 2E 00 14 FF FF 55 55
[11:14:46.619] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:46.619] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:46.620] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.620] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.620] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.670] 测试写DID 0x0014, 数据: 12 34...
[11:14:46.670] 发送: can0  715   [8]  05 2E 00 14 12 34 55 55
[11:14:46.671] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.671] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.671] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.721] 测试写DID 0x0014, 数据: 00 00 00...
[11:14:46.721] 发送: can0  715   [8]  06 2E 00 14 00 00 00 55
[11:14:46.723] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.723] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.723] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.773] 测试写DID 0x0014, 数据: 01 02 03...
[11:14:46.773] 发送: can0  715   [8]  06 2E 00 14 01 02 03 55
[11:14:46.775] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.775] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.775] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.825] 测试写DID 0x0014, 数据: 00 00 00 00...
[11:14:46.825] 发送: can0  715   [8]  07 2E 00 14 00 00 00 00
[11:14:46.827] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.827] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.827] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.877] 测试写DID 0x0014, 数据: 12 34 56 78...
[11:14:46.877] 发送: can0  715   [8]  07 2E 00 14 12 34 56 78
[11:14:46.878] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.878] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.878] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.928] 测试写DID 0x0014, 数据: FF FF FF FF...
[11:14:46.928] 发送: can0  715   [8]  07 2E 00 14 FF FF FF FF
[11:14:46.929] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.929] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.929] ❌ DID 0x0014: 负响应 NRC=0x13
[11:14:46.979] 测试写DID 0x0100, 数据: 00...
[11:14:46.979] 发送: can0  715   [8]  04 2E 01 00 00 55 55 55
[11:14:46.980] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.980] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:46.980] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.030] 测试写DID 0x0100, 数据: 01...
[11:14:47.030] 发送: can0  715   [8]  04 2E 01 00 01 55 55 55
[11:14:47.031] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.031] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.031] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.081] 测试写DID 0x0100, 数据: FF...
[11:14:47.081] 发送: can0  715   [8]  04 2E 01 00 FF 55 55 55
[11:14:47.082] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.082] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.082] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.132] 测试写DID 0x0100, 数据: 00 00...
[11:14:47.132] 发送: can0  715   [8]  05 2E 01 00 00 00 55 55
[11:14:47.132] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:47.133] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:47.133] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.133] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.133] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.183] 测试写DID 0x0100, 数据: 00 01...
[11:14:47.184] 发送: can0  715   [8]  05 2E 01 00 00 01 55 55
[11:14:47.185] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.185] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.185] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.235] 测试写DID 0x0100, 数据: FF FF...
[11:14:47.235] 发送: can0  715   [8]  05 2E 01 00 FF FF 55 55
[11:14:47.237] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.237] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.237] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.287] 测试写DID 0x0100, 数据: 12 34...
[11:14:47.287] 发送: can0  715   [8]  05 2E 01 00 12 34 55 55
[11:14:47.289] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.289] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.289] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.339] 测试写DID 0x0100, 数据: 00 00 00...
[11:14:47.339] 发送: can0  715   [8]  06 2E 01 00 00 00 00 55
[11:14:47.340] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.340] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.340] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.390] 测试写DID 0x0100, 数据: 01 02 03...
[11:14:47.390] 发送: can0  715   [8]  06 2E 01 00 01 02 03 55
[11:14:47.391] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.391] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.391] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.441] 测试写DID 0x0100, 数据: 00 00 00 00...
[11:14:47.441] 发送: can0  715   [8]  07 2E 01 00 00 00 00 00
[11:14:47.443] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.443] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.443] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.493] 测试写DID 0x0100, 数据: 12 34 56 78...
[11:14:47.493] 发送: can0  715   [8]  07 2E 01 00 12 34 56 78
[11:14:47.494] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.494] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.494] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.544] 测试写DID 0x0100, 数据: FF FF FF FF...
[11:14:47.544] 发送: can0  715   [8]  07 2E 01 00 FF FF FF FF
[11:14:47.545] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.545] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.545] ❌ DID 0x0100: 负响应 NRC=0x13
[11:14:47.595] 测试写DID 0x0101, 数据: 00...
[11:14:47.595] 发送: can0  715   [8]  04 2E 01 01 00 55 55 55
[11:14:47.597] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.597] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.597] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.647] 测试写DID 0x0101, 数据: 01...
[11:14:47.648] 发送: can0  715   [8]  04 2E 01 01 01 55 55 55
[11:14:47.648] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:47.648] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:47.649] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.649] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.649] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.699] 测试写DID 0x0101, 数据: FF...
[11:14:47.699] 发送: can0  715   [8]  04 2E 01 01 FF 55 55 55
[11:14:47.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.700] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.750] 测试写DID 0x0101, 数据: 00 00...
[11:14:47.750] 发送: can0  715   [8]  05 2E 01 01 00 00 55 55
[11:14:47.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.751] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.801] 测试写DID 0x0101, 数据: 00 01...
[11:14:47.801] 发送: can0  715   [8]  05 2E 01 01 00 01 55 55
[11:14:47.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.802] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.852] 测试写DID 0x0101, 数据: FF FF...
[11:14:47.852] 发送: can0  715   [8]  05 2E 01 01 FF FF 55 55
[11:14:47.853] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.853] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.853] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.903] 测试写DID 0x0101, 数据: 12 34...
[11:14:47.903] 发送: can0  715   [8]  05 2E 01 01 12 34 55 55
[11:14:47.904] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.904] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.904] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:47.954] 测试写DID 0x0101, 数据: 00 00 00...
[11:14:47.955] 发送: can0  715   [8]  06 2E 01 01 00 00 00 55
[11:14:47.956] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.956] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:47.956] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:48.006] 测试写DID 0x0101, 数据: 01 02 03...
[11:14:48.007] 发送: can0  715   [8]  06 2E 01 01 01 02 03 55
[11:14:48.008] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.008] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.008] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:48.058] 测试写DID 0x0101, 数据: 00 00 00 00...
[11:14:48.058] 发送: can0  715   [8]  07 2E 01 01 00 00 00 00
[11:14:48.059] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.059] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.059] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:48.109] 测试写DID 0x0101, 数据: 12 34 56 78...
[11:14:48.109] 发送: can0  715   [8]  07 2E 01 01 12 34 56 78
[11:14:48.110] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.110] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.110] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:48.160] 测试写DID 0x0101, 数据: FF FF FF FF...
[11:14:48.161] 发送: can0  715   [8]  07 2E 01 01 FF FF FF FF
[11:14:48.161] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:48.161] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:48.162] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.162] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.162] ❌ DID 0x0101: 负响应 NRC=0x13
[11:14:48.212] 测试写DID 0x0102, 数据: 00...
[11:14:48.212] 发送: can0  715   [8]  04 2E 01 02 00 55 55 55
[11:14:48.213] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.213] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.213] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.263] 测试写DID 0x0102, 数据: 01...
[11:14:48.263] 发送: can0  715   [8]  04 2E 01 02 01 55 55 55
[11:14:48.264] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.264] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.264] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.314] 测试写DID 0x0102, 数据: FF...
[11:14:48.315] 发送: can0  715   [8]  04 2E 01 02 FF 55 55 55
[11:14:48.315] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.315] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.315] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.365] 测试写DID 0x0102, 数据: 00 00...
[11:14:48.365] 发送: can0  715   [8]  05 2E 01 02 00 00 55 55
[11:14:48.367] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.367] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.367] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.417] 测试写DID 0x0102, 数据: 00 01...
[11:14:48.417] 发送: can0  715   [8]  05 2E 01 02 00 01 55 55
[11:14:48.418] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.418] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.418] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.468] 测试写DID 0x0102, 数据: FF FF...
[11:14:48.469] 发送: can0  715   [8]  05 2E 01 02 FF FF 55 55
[11:14:48.470] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.470] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.470] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.520] 测试写DID 0x0102, 数据: 12 34...
[11:14:48.521] 发送: can0  715   [8]  05 2E 01 02 12 34 55 55
[11:14:48.521] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.521] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.521] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.571] 测试写DID 0x0102, 数据: 00 00 00...
[11:14:48.571] 发送: can0  715   [8]  06 2E 01 02 00 00 00 55
[11:14:48.572] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.572] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.572] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.622] 测试写DID 0x0102, 数据: 01 02 03...
[11:14:48.623] 发送: can0  715   [8]  06 2E 01 02 01 02 03 55
[11:14:48.623] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:48.623] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:48.623] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.623] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.623] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.673] 测试写DID 0x0102, 数据: 00 00 00 00...
[11:14:48.673] 发送: can0  715   [8]  07 2E 01 02 00 00 00 00
[11:14:48.674] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.674] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.674] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.724] 测试写DID 0x0102, 数据: 12 34 56 78...
[11:14:48.725] 发送: can0  715   [8]  07 2E 01 02 12 34 56 78
[11:14:48.725] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.725] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.725] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.775] 测试写DID 0x0102, 数据: FF FF FF FF...
[11:14:48.775] 发送: can0  715   [8]  07 2E 01 02 FF FF FF FF
[11:14:48.777] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.777] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.777] ❌ DID 0x0102: 负响应 NRC=0x13
[11:14:48.827] 测试写DID 0x0103, 数据: 00...
[11:14:48.827] 发送: can0  715   [8]  04 2E 01 03 00 55 55 55
[11:14:48.828] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.828] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.828] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:48.878] 测试写DID 0x0103, 数据: 01...
[11:14:48.879] 发送: can0  715   [8]  04 2E 01 03 01 55 55 55
[11:14:48.879] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.879] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.879] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:48.929] 测试写DID 0x0103, 数据: FF...
[11:14:48.930] 发送: can0  715   [8]  04 2E 01 03 FF 55 55 55
[11:14:48.931] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.931] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.931] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:48.981] 测试写DID 0x0103, 数据: 00 00...
[11:14:48.982] 发送: can0  715   [8]  05 2E 01 03 00 00 55 55
[11:14:48.982] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.982] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:48.982] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.032] 测试写DID 0x0103, 数据: 00 01...
[11:14:49.033] 发送: can0  715   [8]  05 2E 01 03 00 01 55 55
[11:14:49.033] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.033] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.033] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.083] 测试写DID 0x0103, 数据: FF FF...
[11:14:49.084] 发送: can0  715   [8]  05 2E 01 03 FF FF 55 55
[11:14:49.084] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.084] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.084] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.134] 测试写DID 0x0103, 数据: 12 34...
[11:14:49.134] 发送: can0  715   [8]  05 2E 01 03 12 34 55 55
[11:14:49.135] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:49.135] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:49.135] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.135] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.135] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.185] 测试写DID 0x0103, 数据: 00 00 00...
[11:14:49.185] 发送: can0  715   [8]  06 2E 01 03 00 00 00 55
[11:14:49.187] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.187] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.187] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.237] 测试写DID 0x0103, 数据: 01 02 03...
[11:14:49.238] 发送: can0  715   [8]  06 2E 01 03 01 02 03 55
[11:14:49.238] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.238] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.238] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.288] 测试写DID 0x0103, 数据: 00 00 00 00...
[11:14:49.288] 发送: can0  715   [8]  07 2E 01 03 00 00 00 00
[11:14:49.289] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.289] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.289] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.339] 测试写DID 0x0103, 数据: 12 34 56 78...
[11:14:49.340] 发送: can0  715   [8]  07 2E 01 03 12 34 56 78
[11:14:49.340] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.340] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.340] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.390] 测试写DID 0x0103, 数据: FF FF FF FF...
[11:14:49.391] 发送: can0  715   [8]  07 2E 01 03 FF FF FF FF
[11:14:49.391] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.391] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.391] ❌ DID 0x0103: 负响应 NRC=0x13
[11:14:49.441] 测试写DID 0x0104, 数据: 00...
[11:14:49.442] 发送: can0  715   [8]  04 2E 01 04 00 55 55 55
[11:14:49.442] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.442] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.442] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.493] 测试写DID 0x0104, 数据: 01...
[11:14:49.493] 发送: can0  715   [8]  04 2E 01 04 01 55 55 55
[11:14:49.494] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.494] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.495] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.545] 测试写DID 0x0104, 数据: FF...
[11:14:49.545] 发送: can0  715   [8]  04 2E 01 04 FF 55 55 55
[11:14:49.546] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.546] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.546] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.596] 测试写DID 0x0104, 数据: 00 00...
[11:14:49.597] 发送: can0  715   [8]  05 2E 01 04 00 00 55 55
[11:14:49.597] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.597] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.597] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.647] 测试写DID 0x0104, 数据: 00 01...
[11:14:49.648] 发送: can0  715   [8]  05 2E 01 04 00 01 55 55
[11:14:49.648] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:49.648] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:49.648] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.648] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.649] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.699] 测试写DID 0x0104, 数据: FF FF...
[11:14:49.699] 发送: can0  715   [8]  05 2E 01 04 FF FF 55 55
[11:14:49.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.700] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.750] 测试写DID 0x0104, 数据: 12 34...
[11:14:49.751] 发送: can0  715   [8]  05 2E 01 04 12 34 55 55
[11:14:49.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.751] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.801] 测试写DID 0x0104, 数据: 00 00 00...
[11:14:49.802] 发送: can0  715   [8]  06 2E 01 04 00 00 00 55
[11:14:49.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.802] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.852] 测试写DID 0x0104, 数据: 01 02 03...
[11:14:49.853] 发送: can0  715   [8]  06 2E 01 04 01 02 03 55
[11:14:49.853] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.853] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.853] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.904] 测试写DID 0x0104, 数据: 00 00 00 00...
[11:14:49.904] 发送: can0  715   [8]  07 2E 01 04 00 00 00 00
[11:14:49.905] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.905] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.905] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:49.955] 测试写DID 0x0104, 数据: 12 34 56 78...
[11:14:49.956] 发送: can0  715   [8]  07 2E 01 04 12 34 56 78
[11:14:49.957] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.957] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:49.957] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:50.007] 测试写DID 0x0104, 数据: FF FF FF FF...
[11:14:50.008] 发送: can0  715   [8]  07 2E 01 04 FF FF FF FF
[11:14:50.008] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.008] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.008] ❌ DID 0x0104: 负响应 NRC=0x13
[11:14:50.058] 测试写DID 0x0200, 数据: 00...
[11:14:50.059] 发送: can0  715   [8]  04 2E 02 00 00 55 55 55
[11:14:50.059] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.059] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.059] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.109] 测试写DID 0x0200, 数据: 01...
[11:14:50.110] 发送: can0  715   [8]  04 2E 02 00 01 55 55 55
[11:14:50.110] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.110] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.110] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.160] 测试写DID 0x0200, 数据: FF...
[11:14:50.161] 发送: can0  715   [8]  04 2E 02 00 FF 55 55 55
[11:14:50.161] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:50.161] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:50.161] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.161] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.161] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.212] 测试写DID 0x0200, 数据: 00 00...
[11:14:50.212] 发送: can0  715   [8]  05 2E 02 00 00 00 55 55
[11:14:50.212] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.212] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.212] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.262] 测试写DID 0x0200, 数据: 00 01...
[11:14:50.263] 发送: can0  715   [8]  05 2E 02 00 00 01 55 55
[11:14:50.263] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.263] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.263] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.314] 测试写DID 0x0200, 数据: FF FF...
[11:14:50.314] 发送: can0  715   [8]  05 2E 02 00 FF FF 55 55
[11:14:50.314] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.314] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.314] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.364] 测试写DID 0x0200, 数据: 12 34...
[11:14:50.365] 发送: can0  715   [8]  05 2E 02 00 12 34 55 55
[11:14:50.365] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.365] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.365] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.415] 测试写DID 0x0200, 数据: 00 00 00...
[11:14:50.416] 发送: can0  715   [8]  06 2E 02 00 00 00 00 55
[11:14:50.417] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.417] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.417] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.467] 测试写DID 0x0200, 数据: 01 02 03...
[11:14:50.468] 发送: can0  715   [8]  06 2E 02 00 01 02 03 55
[11:14:50.468] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.468] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.468] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.518] 测试写DID 0x0200, 数据: 00 00 00 00...
[11:14:50.519] 发送: can0  715   [8]  07 2E 02 00 00 00 00 00
[11:14:50.519] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.519] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.519] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.570] 测试写DID 0x0200, 数据: 12 34 56 78...
[11:14:50.570] 发送: can0  715   [8]  07 2E 02 00 12 34 56 78
[11:14:50.570] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.570] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.570] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.621] 测试写DID 0x0200, 数据: FF FF FF FF...
[11:14:50.621] 发送: can0  715   [8]  07 2E 02 00 FF FF FF FF
[11:14:50.621] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:50.621] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:50.622] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.622] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.622] ❌ DID 0x0200: 负响应 NRC=0x13
[11:14:50.672] 测试写DID 0x0201, 数据: 00...
[11:14:50.673] 发送: can0  715   [8]  04 2E 02 01 00 55 55 55
[11:14:50.673] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.673] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.673] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.723] 测试写DID 0x0201, 数据: 01...
[11:14:50.724] 发送: can0  715   [8]  04 2E 02 01 01 55 55 55
[11:14:50.724] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.724] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.724] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.775] 测试写DID 0x0201, 数据: FF...
[11:14:50.775] 发送: can0  715   [8]  04 2E 02 01 FF 55 55 55
[11:14:50.775] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.775] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.775] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.826] 测试写DID 0x0201, 数据: 00 00...
[11:14:50.826] 发送: can0  715   [8]  05 2E 02 01 00 00 55 55
[11:14:50.827] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.827] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.827] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.878] 测试写DID 0x0201, 数据: 00 01...
[11:14:50.878] 发送: can0  715   [8]  05 2E 02 01 00 01 55 55
[11:14:50.878] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.878] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.878] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.929] 测试写DID 0x0201, 数据: FF FF...
[11:14:50.929] 发送: can0  715   [8]  05 2E 02 01 FF FF 55 55
[11:14:50.930] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.930] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.930] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:50.981] 测试写DID 0x0201, 数据: 12 34...
[11:14:50.981] 发送: can0  715   [8]  05 2E 02 01 12 34 55 55
[11:14:50.981] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.981] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:50.981] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.032] 测试写DID 0x0201, 数据: 00 00 00...
[11:14:51.032] 发送: can0  715   [8]  06 2E 02 01 00 00 00 55
[11:14:51.032] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.032] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.032] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.083] 测试写DID 0x0201, 数据: 01 02 03...
[11:14:51.083] 发送: can0  715   [8]  06 2E 02 01 01 02 03 55
[11:14:51.083] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.083] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.083] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.134] 测试写DID 0x0201, 数据: 00 00 00 00...
[11:14:51.134] 发送: can0  715   [8]  07 2E 02 01 00 00 00 00
[11:14:51.134] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:51.134] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:51.134] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.134] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.134] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.185] 测试写DID 0x0201, 数据: 12 34 56 78...
[11:14:51.185] 发送: can0  715   [8]  07 2E 02 01 12 34 56 78
[11:14:51.185] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.185] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.185] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.236] 测试写DID 0x0201, 数据: FF FF FF FF...
[11:14:51.236] 发送: can0  715   [8]  07 2E 02 01 FF FF FF FF
[11:14:51.237] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.237] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.237] ❌ DID 0x0201: 负响应 NRC=0x13
[11:14:51.288] 测试写DID 0x0202, 数据: 00...
[11:14:51.288] 发送: can0  715   [8]  04 2E 02 02 00 55 55 55
[11:14:51.288] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.288] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.288] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.339] 测试写DID 0x0202, 数据: 01...
[11:14:51.339] 发送: can0  715   [8]  04 2E 02 02 01 55 55 55
[11:14:51.339] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.339] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.339] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.390] 测试写DID 0x0202, 数据: FF...
[11:14:51.390] 发送: can0  715   [8]  04 2E 02 02 FF 55 55 55
[11:14:51.390] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.390] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.390] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.441] 测试写DID 0x0202, 数据: 00 00...
[11:14:51.441] 发送: can0  715   [8]  05 2E 02 02 00 00 55 55
[11:14:51.441] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.441] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.441] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.492] 测试写DID 0x0202, 数据: 00 01...
[11:14:51.492] 发送: can0  715   [8]  05 2E 02 02 00 01 55 55
[11:14:51.492] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.492] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.492] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.543] 测试写DID 0x0202, 数据: FF FF...
[11:14:51.543] 发送: can0  715   [8]  05 2E 02 02 FF FF 55 55
[11:14:51.543] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.543] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.544] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.594] 测试写DID 0x0202, 数据: 12 34...
[11:14:51.594] 发送: can0  715   [8]  05 2E 02 02 12 34 55 55
[11:14:51.594] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.594] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.595] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.645] 测试写DID 0x0202, 数据: 00 00 00...
[11:14:51.645] 发送: can0  715   [8]  06 2E 02 02 00 00 00 55
[11:14:51.645] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:51.645] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:51.646] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.646] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.647] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.697] 测试写DID 0x0202, 数据: 01 02 03...
[11:14:51.697] 发送: can0  715   [8]  06 2E 02 02 01 02 03 55
[11:14:51.697] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.697] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.697] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.748] 测试写DID 0x0202, 数据: 00 00 00 00...
[11:14:51.748] 发送: can0  715   [8]  07 2E 02 02 00 00 00 00
[11:14:51.748] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.749] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.749] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.799] 测试写DID 0x0202, 数据: 12 34 56 78...
[11:14:51.799] 发送: can0  715   [8]  07 2E 02 02 12 34 56 78
[11:14:51.800] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.801] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.801] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.851] 测试写DID 0x0202, 数据: FF FF FF FF...
[11:14:51.851] 发送: can0  715   [8]  07 2E 02 02 FF FF FF FF
[11:14:51.851] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.852] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.852] ❌ DID 0x0202: 负响应 NRC=0x13
[11:14:51.902] 测试写DID 0x0203, 数据: 00...
[11:14:51.902] 发送: can0  715   [8]  04 2E 02 03 00 55 55 55
[11:14:51.902] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.903] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.903] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:51.953] 测试写DID 0x0203, 数据: 01...
[11:14:51.953] 发送: can0  715   [8]  04 2E 02 03 01 55 55 55
[11:14:51.954] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.955] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:51.955] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.005] 测试写DID 0x0203, 数据: FF...
[11:14:52.005] 发送: can0  715   [8]  04 2E 02 03 FF 55 55 55
[11:14:52.005] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.005] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.006] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.056] 测试写DID 0x0203, 数据: 00 00...
[11:14:52.056] 发送: can0  715   [8]  05 2E 02 03 00 00 55 55
[11:14:52.057] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.058] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.058] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.108] 测试写DID 0x0203, 数据: 00 01...
[11:14:52.108] 发送: can0  715   [8]  05 2E 02 03 00 01 55 55
[11:14:52.108] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.108] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.109] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.159] 测试写DID 0x0203, 数据: FF FF...
[11:14:52.159] 发送: can0  715   [8]  05 2E 02 03 FF FF 55 55
[11:14:52.159] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:52.159] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:52.159] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.160] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.160] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.210] 测试写DID 0x0203, 数据: 12 34...
[11:14:52.210] 发送: can0  715   [8]  05 2E 02 03 12 34 55 55
[11:14:52.210] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.211] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.211] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.261] 测试写DID 0x0203, 数据: 00 00 00...
[11:14:52.261] 发送: can0  715   [8]  06 2E 02 03 00 00 00 55
[11:14:52.261] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.262] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.262] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.312] 测试写DID 0x0203, 数据: 01 02 03...
[11:14:52.312] 发送: can0  715   [8]  06 2E 02 03 01 02 03 55
[11:14:52.313] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.313] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.313] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.363] 测试写DID 0x0203, 数据: 00 00 00 00...
[11:14:52.363] 发送: can0  715   [8]  07 2E 02 03 00 00 00 00
[11:14:52.363] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.364] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.364] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.414] 测试写DID 0x0203, 数据: 12 34 56 78...
[11:14:52.414] 发送: can0  715   [8]  07 2E 02 03 12 34 56 78
[11:14:52.414] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.415] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.415] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.465] 测试写DID 0x0203, 数据: FF FF FF FF...
[11:14:52.465] 发送: can0  715   [8]  07 2E 02 03 FF FF FF FF
[11:14:52.466] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.466] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.466] ❌ DID 0x0203: 负响应 NRC=0x13
[11:14:52.516] 测试写DID 0x0204, 数据: 00...
[11:14:52.516] 发送: can0  715   [8]  04 2E 02 04 00 55 55 55
[11:14:52.518] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.518] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.518] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.568] 测试写DID 0x0204, 数据: 01...
[11:14:52.568] 发送: can0  715   [8]  04 2E 02 04 01 55 55 55
[11:14:52.569] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.569] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.569] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.619] 测试写DID 0x0204, 数据: FF...
[11:14:52.619] 发送: can0  715   [8]  04 2E 02 04 FF 55 55 55
[11:14:52.619] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:52.619] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:52.621] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.621] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.621] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.671] 测试写DID 0x0204, 数据: 00 00...
[11:14:52.671] 发送: can0  715   [8]  05 2E 02 04 00 00 55 55
[11:14:52.673] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.673] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.673] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.723] 测试写DID 0x0204, 数据: 00 01...
[11:14:52.723] 发送: can0  715   [8]  05 2E 02 04 00 01 55 55
[11:14:52.725] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.725] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.725] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.775] 测试写DID 0x0204, 数据: FF FF...
[11:14:52.775] 发送: can0  715   [8]  05 2E 02 04 FF FF 55 55
[11:14:52.776] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.776] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.776] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.826] 测试写DID 0x0204, 数据: 12 34...
[11:14:52.826] 发送: can0  715   [8]  05 2E 02 04 12 34 55 55
[11:14:52.828] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.828] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.828] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.878] 测试写DID 0x0204, 数据: 00 00 00...
[11:14:52.878] 发送: can0  715   [8]  06 2E 02 04 00 00 00 55
[11:14:52.880] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.880] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.880] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.930] 测试写DID 0x0204, 数据: 01 02 03...
[11:14:52.930] 发送: can0  715   [8]  06 2E 02 04 01 02 03 55
[11:14:52.932] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.932] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.932] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:52.982] 测试写DID 0x0204, 数据: 00 00 00 00...
[11:14:52.982] 发送: can0  715   [8]  07 2E 02 04 00 00 00 00
[11:14:52.983] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.983] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:52.983] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:53.033] 测试写DID 0x0204, 数据: 12 34 56 78...
[11:14:53.033] 发送: can0  715   [8]  07 2E 02 04 12 34 56 78
[11:14:53.034] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.034] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.034] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:53.084] 测试写DID 0x0204, 数据: FF FF FF FF...
[11:14:53.084] 发送: can0  715   [8]  07 2E 02 04 FF FF FF FF
[11:14:53.085] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.085] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.085] ❌ DID 0x0204: 负响应 NRC=0x13
[11:14:53.135] 测试写DID 0x1000, 数据: 00...
[11:14:53.135] 发送: can0  715   [8]  04 2E 10 00 00 55 55 55
[11:14:53.135] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:53.135] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:53.136] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.136] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.136] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.186] 测试写DID 0x1000, 数据: 01...
[11:14:53.186] 发送: can0  715   [8]  04 2E 10 00 01 55 55 55
[11:14:53.188] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.188] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.188] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.238] 测试写DID 0x1000, 数据: FF...
[11:14:53.238] 发送: can0  715   [8]  04 2E 10 00 FF 55 55 55
[11:14:53.239] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.239] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.239] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.289] 测试写DID 0x1000, 数据: 00 00...
[11:14:53.289] 发送: can0  715   [8]  05 2E 10 00 00 00 55 55
[11:14:53.290] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.290] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.290] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.340] 测试写DID 0x1000, 数据: 00 01...
[11:14:53.340] 发送: can0  715   [8]  05 2E 10 00 00 01 55 55
[11:14:53.341] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.341] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.341] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.391] 测试写DID 0x1000, 数据: FF FF...
[11:14:53.391] 发送: can0  715   [8]  05 2E 10 00 FF FF 55 55
[11:14:53.392] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.392] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.392] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.442] 测试写DID 0x1000, 数据: 12 34...
[11:14:53.442] 发送: can0  715   [8]  05 2E 10 00 12 34 55 55
[11:14:53.443] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.443] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.443] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.493] 测试写DID 0x1000, 数据: 00 00 00...
[11:14:53.493] 发送: can0  715   [8]  06 2E 10 00 00 00 00 55
[11:14:53.495] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.495] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.495] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.545] 测试写DID 0x1000, 数据: 01 02 03...
[11:14:53.545] 发送: can0  715   [8]  06 2E 10 00 01 02 03 55
[11:14:53.546] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.546] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.546] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.596] 测试写DID 0x1000, 数据: 00 00 00 00...
[11:14:53.596] 发送: can0  715   [8]  07 2E 10 00 00 00 00 00
[11:14:53.598] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.598] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.598] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.648] 测试写DID 0x1000, 数据: 12 34 56 78...
[11:14:53.648] 发送: can0  715   [8]  07 2E 10 00 12 34 56 78
[11:14:53.648] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:53.648] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:53.649] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.649] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.649] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.699] 测试写DID 0x1000, 数据: FF FF FF FF...
[11:14:53.699] 发送: can0  715   [8]  07 2E 10 00 FF FF FF FF
[11:14:53.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.700] ❌ DID 0x1000: 负响应 NRC=0x13
[11:14:53.750] 测试写DID 0x1001, 数据: 00...
[11:14:53.750] 发送: can0  715   [8]  04 2E 10 01 00 55 55 55
[11:14:53.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.751] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:53.801] 测试写DID 0x1001, 数据: 01...
[11:14:53.801] 发送: can0  715   [8]  04 2E 10 01 01 55 55 55
[11:14:53.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.802] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:53.852] 测试写DID 0x1001, 数据: FF...
[11:14:53.852] 发送: can0  715   [8]  04 2E 10 01 FF 55 55 55
[11:14:53.853] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.853] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.853] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:53.903] 测试写DID 0x1001, 数据: 00 00...
[11:14:53.903] 发送: can0  715   [8]  05 2E 10 01 00 00 55 55
[11:14:53.904] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.904] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.904] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:53.954] 测试写DID 0x1001, 数据: 00 01...
[11:14:53.954] 发送: can0  715   [8]  05 2E 10 01 00 01 55 55
[11:14:53.955] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.955] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:53.955] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.005] 测试写DID 0x1001, 数据: FF FF...
[11:14:54.005] 发送: can0  715   [8]  05 2E 10 01 FF FF 55 55
[11:14:54.006] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.006] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.006] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.056] 测试写DID 0x1001, 数据: 12 34...
[11:14:54.056] 发送: can0  715   [8]  05 2E 10 01 12 34 55 55
[11:14:54.058] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.058] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.058] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.108] 测试写DID 0x1001, 数据: 00 00 00...
[11:14:54.108] 发送: can0  715   [8]  06 2E 10 01 00 00 00 55
[11:14:54.109] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.109] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.109] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.159] 测试写DID 0x1001, 数据: 01 02 03...
[11:14:54.159] 发送: can0  715   [8]  06 2E 10 01 01 02 03 55
[11:14:54.159] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:54.159] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:54.160] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.160] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.160] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.210] 测试写DID 0x1001, 数据: 00 00 00 00...
[11:14:54.210] 发送: can0  715   [8]  07 2E 10 01 00 00 00 00
[11:14:54.211] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.211] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.211] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.261] 测试写DID 0x1001, 数据: 12 34 56 78...
[11:14:54.261] 发送: can0  715   [8]  07 2E 10 01 12 34 56 78
[11:14:54.263] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.263] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.263] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.313] 测试写DID 0x1001, 数据: FF FF FF FF...
[11:14:54.313] 发送: can0  715   [8]  07 2E 10 01 FF FF FF FF
[11:14:54.314] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.314] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.314] ❌ DID 0x1001: 负响应 NRC=0x13
[11:14:54.364] 测试写DID 0x1002, 数据: 00...
[11:14:54.364] 发送: can0  715   [8]  04 2E 10 02 00 55 55 55
[11:14:54.366] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.366] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.366] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.416] 测试写DID 0x1002, 数据: 01...
[11:14:54.416] 发送: can0  715   [8]  04 2E 10 02 01 55 55 55
[11:14:54.418] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.418] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.418] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.468] 测试写DID 0x1002, 数据: FF...
[11:14:54.468] 发送: can0  715   [8]  04 2E 10 02 FF 55 55 55
[11:14:54.469] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.469] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.469] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.519] 测试写DID 0x1002, 数据: 00 00...
[11:14:54.519] 发送: can0  715   [8]  05 2E 10 02 00 00 55 55
[11:14:54.520] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.520] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.520] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.570] 测试写DID 0x1002, 数据: 00 01...
[11:14:54.570] 发送: can0  715   [8]  05 2E 10 02 00 01 55 55
[11:14:54.571] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.571] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.571] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.621] 测试写DID 0x1002, 数据: FF FF...
[11:14:54.621] 发送: can0  715   [8]  05 2E 10 02 FF FF 55 55
[11:14:54.621] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:54.621] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:54.623] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.623] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.623] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.673] 测试写DID 0x1002, 数据: 12 34...
[11:14:54.673] 发送: can0  715   [8]  05 2E 10 02 12 34 55 55
[11:14:54.674] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.674] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.674] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.724] 测试写DID 0x1002, 数据: 00 00 00...
[11:14:54.724] 发送: can0  715   [8]  06 2E 10 02 00 00 00 55
[11:14:54.725] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.725] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.725] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.775] 测试写DID 0x1002, 数据: 01 02 03...
[11:14:54.775] 发送: can0  715   [8]  06 2E 10 02 01 02 03 55
[11:14:54.776] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.776] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.776] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.826] 测试写DID 0x1002, 数据: 00 00 00 00...
[11:14:54.826] 发送: can0  715   [8]  07 2E 10 02 00 00 00 00
[11:14:54.828] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.828] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.828] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.878] 测试写DID 0x1002, 数据: 12 34 56 78...
[11:14:54.878] 发送: can0  715   [8]  07 2E 10 02 12 34 56 78
[11:14:54.880] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.880] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.880] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.930] 测试写DID 0x1002, 数据: FF FF FF FF...
[11:14:54.930] 发送: can0  715   [8]  07 2E 10 02 FF FF FF FF
[11:14:54.931] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.931] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.931] ❌ DID 0x1002: 负响应 NRC=0x13
[11:14:54.981] 测试写DID 0x1003, 数据: 00...
[11:14:54.981] 发送: can0  715   [8]  04 2E 10 03 00 55 55 55
[11:14:54.982] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.982] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:54.982] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.032] 测试写DID 0x1003, 数据: 01...
[11:14:55.032] 发送: can0  715   [8]  04 2E 10 03 01 55 55 55
[11:14:55.033] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.033] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.033] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.083] 测试写DID 0x1003, 数据: FF...
[11:14:55.083] 发送: can0  715   [8]  04 2E 10 03 FF 55 55 55
[11:14:55.084] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.084] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.084] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.134] 测试写DID 0x1003, 数据: 00 00...
[11:14:55.134] 发送: can0  715   [8]  05 2E 10 03 00 00 55 55
[11:14:55.134] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:55.134] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:55.135] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.135] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.135] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.185] 测试写DID 0x1003, 数据: 00 01...
[11:14:55.185] 发送: can0  715   [8]  05 2E 10 03 00 01 55 55
[11:14:55.186] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.186] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.186] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.236] 测试写DID 0x1003, 数据: FF FF...
[11:14:55.236] 发送: can0  715   [8]  05 2E 10 03 FF FF 55 55
[11:14:55.238] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.238] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.238] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.288] 测试写DID 0x1003, 数据: 12 34...
[11:14:55.288] 发送: can0  715   [8]  05 2E 10 03 12 34 55 55
[11:14:55.289] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.289] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.289] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.339] 测试写DID 0x1003, 数据: 00 00 00...
[11:14:55.339] 发送: can0  715   [8]  06 2E 10 03 00 00 00 55
[11:14:55.340] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.340] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.340] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.390] 测试写DID 0x1003, 数据: 01 02 03...
[11:14:55.390] 发送: can0  715   [8]  06 2E 10 03 01 02 03 55
[11:14:55.391] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.391] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.391] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.441] 测试写DID 0x1003, 数据: 00 00 00 00...
[11:14:55.441] 发送: can0  715   [8]  07 2E 10 03 00 00 00 00
[11:14:55.442] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.442] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.442] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.492] 测试写DID 0x1003, 数据: 12 34 56 78...
[11:14:55.492] 发送: can0  715   [8]  07 2E 10 03 12 34 56 78
[11:14:55.493] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.493] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.493] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.543] 测试写DID 0x1003, 数据: FF FF FF FF...
[11:14:55.543] 发送: can0  715   [8]  07 2E 10 03 FF FF FF FF
[11:14:55.545] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.545] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.545] ❌ DID 0x1003: 负响应 NRC=0x13
[11:14:55.595] 测试写DID 0x1004, 数据: 00...
[11:14:55.595] 发送: can0  715   [8]  04 2E 10 04 00 55 55 55
[11:14:55.597] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.597] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.597] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.647] 测试写DID 0x1004, 数据: 01...
[11:14:55.647] 发送: can0  715   [8]  04 2E 10 04 01 55 55 55
[11:14:55.647] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:55.647] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:55.649] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.649] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.649] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.699] 测试写DID 0x1004, 数据: FF...
[11:14:55.699] 发送: can0  715   [8]  04 2E 10 04 FF 55 55 55
[11:14:55.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.700] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.750] 测试写DID 0x1004, 数据: 00 00...
[11:14:55.750] 发送: can0  715   [8]  05 2E 10 04 00 00 55 55
[11:14:55.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.751] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.801] 测试写DID 0x1004, 数据: 00 01...
[11:14:55.801] 发送: can0  715   [8]  05 2E 10 04 00 01 55 55
[11:14:55.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.802] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.852] 测试写DID 0x1004, 数据: FF FF...
[11:14:55.852] 发送: can0  715   [8]  05 2E 10 04 FF FF 55 55
[11:14:55.854] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.854] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.854] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.904] 测试写DID 0x1004, 数据: 12 34...
[11:14:55.904] 发送: can0  715   [8]  05 2E 10 04 12 34 55 55
[11:14:55.905] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.905] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.905] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:55.955] 测试写DID 0x1004, 数据: 00 00 00...
[11:14:55.955] 发送: can0  715   [8]  06 2E 10 04 00 00 00 55
[11:14:55.956] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.956] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:55.956] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:56.006] 测试写DID 0x1004, 数据: 01 02 03...
[11:14:56.006] 发送: can0  715   [8]  06 2E 10 04 01 02 03 55
[11:14:56.008] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.008] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.008] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:56.058] 测试写DID 0x1004, 数据: 00 00 00 00...
[11:14:56.058] 发送: can0  715   [8]  07 2E 10 04 00 00 00 00
[11:14:56.059] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.059] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.059] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:56.109] 测试写DID 0x1004, 数据: 12 34 56 78...
[11:14:56.109] 发送: can0  715   [8]  07 2E 10 04 12 34 56 78
[11:14:56.110] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.110] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.110] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:56.160] 测试写DID 0x1004, 数据: FF FF FF FF...
[11:14:56.160] 发送: can0  715   [8]  07 2E 10 04 FF FF FF FF
[11:14:56.160] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:56.160] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:56.162] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.162] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.162] ❌ DID 0x1004: 负响应 NRC=0x13
[11:14:56.212] 测试写DID 0x1010, 数据: 00...
[11:14:56.212] 发送: can0  715   [8]  04 2E 10 10 00 55 55 55
[11:14:56.213] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.213] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.213] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.263] 测试写DID 0x1010, 数据: 01...
[11:14:56.263] 发送: can0  715   [8]  04 2E 10 10 01 55 55 55
[11:14:56.264] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.264] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.264] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.314] 测试写DID 0x1010, 数据: FF...
[11:14:56.314] 发送: can0  715   [8]  04 2E 10 10 FF 55 55 55
[11:14:56.315] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.315] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.315] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.365] 测试写DID 0x1010, 数据: 00 00...
[11:14:56.365] 发送: can0  715   [8]  05 2E 10 10 00 00 55 55
[11:14:56.367] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.367] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.367] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.417] 测试写DID 0x1010, 数据: 00 01...
[11:14:56.417] 发送: can0  715   [8]  05 2E 10 10 00 01 55 55
[11:14:56.419] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.419] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.419] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.469] 测试写DID 0x1010, 数据: FF FF...
[11:14:56.469] 发送: can0  715   [8]  05 2E 10 10 FF FF 55 55
[11:14:56.471] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.471] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.471] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.521] 测试写DID 0x1010, 数据: 12 34...
[11:14:56.521] 发送: can0  715   [8]  05 2E 10 10 12 34 55 55
[11:14:56.522] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.522] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.522] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.572] 测试写DID 0x1010, 数据: 00 00 00...
[11:14:56.572] 发送: can0  715   [8]  06 2E 10 10 00 00 00 55
[11:14:56.573] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.573] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.573] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.623] 测试写DID 0x1010, 数据: 01 02 03...
[11:14:56.623] 发送: can0  715   [8]  06 2E 10 10 01 02 03 55
[11:14:56.623] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:56.623] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:56.624] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.624] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.624] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.674] 测试写DID 0x1010, 数据: 00 00 00 00...
[11:14:56.674] 发送: can0  715   [8]  07 2E 10 10 00 00 00 00
[11:14:56.675] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.675] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.675] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.725] 测试写DID 0x1010, 数据: 12 34 56 78...
[11:14:56.725] 发送: can0  715   [8]  07 2E 10 10 12 34 56 78
[11:14:56.727] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.727] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.727] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.777] 测试写DID 0x1010, 数据: FF FF FF FF...
[11:14:56.777] 发送: can0  715   [8]  07 2E 10 10 FF FF FF FF
[11:14:56.779] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.779] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.779] ❌ DID 0x1010: 负响应 NRC=0x13
[11:14:56.829] 测试写DID 0x1011, 数据: 00...
[11:14:56.829] 发送: can0  715   [8]  04 2E 10 11 00 55 55 55
[11:14:56.830] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.830] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.830] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:56.880] 测试写DID 0x1011, 数据: 01...
[11:14:56.880] 发送: can0  715   [8]  04 2E 10 11 01 55 55 55
[11:14:56.881] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.881] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.881] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:56.931] 测试写DID 0x1011, 数据: FF...
[11:14:56.931] 发送: can0  715   [8]  04 2E 10 11 FF 55 55 55
[11:14:56.932] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.932] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.932] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:56.982] 测试写DID 0x1011, 数据: 00 00...
[11:14:56.982] 发送: can0  715   [8]  05 2E 10 11 00 00 55 55
[11:14:56.983] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.983] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:56.983] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.033] 测试写DID 0x1011, 数据: 00 01...
[11:14:57.033] 发送: can0  715   [8]  05 2E 10 11 00 01 55 55
[11:14:57.034] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.034] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.034] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.084] 测试写DID 0x1011, 数据: FF FF...
[11:14:57.084] 发送: can0  715   [8]  05 2E 10 11 FF FF 55 55
[11:14:57.085] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.085] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.085] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.135] 测试写DID 0x1011, 数据: 12 34...
[11:14:57.135] 发送: can0  715   [8]  05 2E 10 11 12 34 55 55
[11:14:57.135] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:57.135] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:57.136] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.136] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.136] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.186] 测试写DID 0x1011, 数据: 00 00 00...
[11:14:57.186] 发送: can0  715   [8]  06 2E 10 11 00 00 00 55
[11:14:57.188] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.188] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.188] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.238] 测试写DID 0x1011, 数据: 01 02 03...
[11:14:57.238] 发送: can0  715   [8]  06 2E 10 11 01 02 03 55
[11:14:57.239] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.239] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.239] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.289] 测试写DID 0x1011, 数据: 00 00 00 00...
[11:14:57.289] 发送: can0  715   [8]  07 2E 10 11 00 00 00 00
[11:14:57.290] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.290] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.290] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.340] 测试写DID 0x1011, 数据: 12 34 56 78...
[11:14:57.340] 发送: can0  715   [8]  07 2E 10 11 12 34 56 78
[11:14:57.341] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.341] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.341] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.391] 测试写DID 0x1011, 数据: FF FF FF FF...
[11:14:57.391] 发送: can0  715   [8]  07 2E 10 11 FF FF FF FF
[11:14:57.392] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.392] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.392] ❌ DID 0x1011: 负响应 NRC=0x13
[11:14:57.442] 测试写DID 0x1012, 数据: 00...
[11:14:57.442] 发送: can0  715   [8]  04 2E 10 12 00 55 55 55
[11:14:57.443] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.443] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.443] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.493] 测试写DID 0x1012, 数据: 01...
[11:14:57.493] 发送: can0  715   [8]  04 2E 10 12 01 55 55 55
[11:14:57.495] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.495] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.495] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.545] 测试写DID 0x1012, 数据: FF...
[11:14:57.545] 发送: can0  715   [8]  04 2E 10 12 FF 55 55 55
[11:14:57.546] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.546] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.546] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.596] 测试写DID 0x1012, 数据: 00 00...
[11:14:57.596] 发送: can0  715   [8]  05 2E 10 12 00 00 55 55
[11:14:57.598] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.598] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.598] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.648] 测试写DID 0x1012, 数据: 00 01...
[11:14:57.648] 发送: can0  715   [8]  05 2E 10 12 00 01 55 55
[11:14:57.648] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:57.648] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:57.649] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.649] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.649] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.699] 测试写DID 0x1012, 数据: FF FF...
[11:14:57.699] 发送: can0  715   [8]  05 2E 10 12 FF FF 55 55
[11:14:57.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.700] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.750] 测试写DID 0x1012, 数据: 12 34...
[11:14:57.750] 发送: can0  715   [8]  05 2E 10 12 12 34 55 55
[11:14:57.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.751] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.801] 测试写DID 0x1012, 数据: 00 00 00...
[11:14:57.801] 发送: can0  715   [8]  06 2E 10 12 00 00 00 55
[11:14:57.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.802] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.852] 测试写DID 0x1012, 数据: 01 02 03...
[11:14:57.852] 发送: can0  715   [8]  06 2E 10 12 01 02 03 55
[11:14:57.853] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.853] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.853] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.903] 测试写DID 0x1012, 数据: 00 00 00 00...
[11:14:57.903] 发送: can0  715   [8]  07 2E 10 12 00 00 00 00
[11:14:57.904] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.904] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.904] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:57.954] 测试写DID 0x1012, 数据: 12 34 56 78...
[11:14:57.954] 发送: can0  715   [8]  07 2E 10 12 12 34 56 78
[11:14:57.955] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.955] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:57.955] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:58.005] 测试写DID 0x1012, 数据: FF FF FF FF...
[11:14:58.005] 发送: can0  715   [8]  07 2E 10 12 FF FF FF FF
[11:14:58.007] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.007] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.007] ❌ DID 0x1012: 负响应 NRC=0x13
[11:14:58.057] 测试写DID 0x1013, 数据: 00...
[11:14:58.057] 发送: can0  715   [8]  04 2E 10 13 00 55 55 55
[11:14:58.058] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.058] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.058] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.108] 测试写DID 0x1013, 数据: 01...
[11:14:58.108] 发送: can0  715   [8]  04 2E 10 13 01 55 55 55
[11:14:58.109] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.109] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.109] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.159] 测试写DID 0x1013, 数据: FF...
[11:14:58.159] 发送: can0  715   [8]  04 2E 10 13 FF 55 55 55
[11:14:58.159] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:58.160] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:58.160] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.160] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.160] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.210] 测试写DID 0x1013, 数据: 00 00...
[11:14:58.210] 发送: can0  715   [8]  05 2E 10 13 00 00 55 55
[11:14:58.211] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.211] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.211] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.261] 测试写DID 0x1013, 数据: 00 01...
[11:14:58.261] 发送: can0  715   [8]  05 2E 10 13 00 01 55 55
[11:14:58.262] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.262] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.262] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.312] 测试写DID 0x1013, 数据: FF FF...
[11:14:58.312] 发送: can0  715   [8]  05 2E 10 13 FF FF 55 55
[11:14:58.313] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.313] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.313] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.363] 测试写DID 0x1013, 数据: 12 34...
[11:14:58.363] 发送: can0  715   [8]  05 2E 10 13 12 34 55 55
[11:14:58.364] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.364] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.364] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.414] 测试写DID 0x1013, 数据: 00 00 00...
[11:14:58.414] 发送: can0  715   [8]  06 2E 10 13 00 00 00 55
[11:14:58.415] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.415] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.415] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.465] 测试写DID 0x1013, 数据: 01 02 03...
[11:14:58.465] 发送: can0  715   [8]  06 2E 10 13 01 02 03 55
[11:14:58.466] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.466] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.466] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.516] 测试写DID 0x1013, 数据: 00 00 00 00...
[11:14:58.516] 发送: can0  715   [8]  07 2E 10 13 00 00 00 00
[11:14:58.518] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.518] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.518] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.568] 测试写DID 0x1013, 数据: 12 34 56 78...
[11:14:58.569] 发送: can0  715   [8]  07 2E 10 13 12 34 56 78
[11:14:58.570] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.570] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.570] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.620] 测试写DID 0x1013, 数据: FF FF FF FF...
[11:14:58.621] 发送: can0  715   [8]  07 2E 10 13 FF FF FF FF
[11:14:58.621] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:58.621] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:58.622] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.622] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.622] ❌ DID 0x1013: 负响应 NRC=0x13
[11:14:58.672] 测试写DID 0x1014, 数据: 00...
[11:14:58.672] 发送: can0  715   [8]  04 2E 10 14 00 55 55 55
[11:14:58.673] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.673] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.673] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.723] 测试写DID 0x1014, 数据: 01...
[11:14:58.723] 发送: can0  715   [8]  04 2E 10 14 01 55 55 55
[11:14:58.724] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.724] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.724] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.774] 测试写DID 0x1014, 数据: FF...
[11:14:58.774] 发送: can0  715   [8]  04 2E 10 14 FF 55 55 55
[11:14:58.775] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.775] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.775] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.825] 测试写DID 0x1014, 数据: 00 00...
[11:14:58.825] 发送: can0  715   [8]  05 2E 10 14 00 00 55 55
[11:14:58.829] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.829] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.829] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.879] 测试写DID 0x1014, 数据: 00 01...
[11:14:58.879] 发送: can0  715   [8]  05 2E 10 14 00 01 55 55
[11:14:58.880] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.880] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.880] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.930] 测试写DID 0x1014, 数据: FF FF...
[11:14:58.930] 发送: can0  715   [8]  05 2E 10 14 FF FF 55 55
[11:14:58.931] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.931] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.931] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:58.981] 测试写DID 0x1014, 数据: 12 34...
[11:14:58.981] 发送: can0  715   [8]  05 2E 10 14 12 34 55 55
[11:14:58.982] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.982] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:58.982] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.032] 测试写DID 0x1014, 数据: 00 00 00...
[11:14:59.032] 发送: can0  715   [8]  06 2E 10 14 00 00 00 55
[11:14:59.033] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.033] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.033] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.083] 测试写DID 0x1014, 数据: 01 02 03...
[11:14:59.083] 发送: can0  715   [8]  06 2E 10 14 01 02 03 55
[11:14:59.084] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.084] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.084] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.134] 测试写DID 0x1014, 数据: 00 00 00 00...
[11:14:59.135] 发送: can0  715   [8]  07 2E 10 14 00 00 00 00
[11:14:59.135] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:59.135] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:59.135] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.135] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.135] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.185] 测试写DID 0x1014, 数据: 12 34 56 78...
[11:14:59.185] 发送: can0  715   [8]  07 2E 10 14 12 34 56 78
[11:14:59.186] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.186] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.186] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.236] 测试写DID 0x1014, 数据: FF FF FF FF...
[11:14:59.237] 发送: can0  715   [8]  07 2E 10 14 FF FF FF FF
[11:14:59.238] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.238] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.238] ❌ DID 0x1014: 负响应 NRC=0x13
[11:14:59.288] 测试写DID 0x1100, 数据: 00...
[11:14:59.289] 发送: can0  715   [8]  04 2E 11 00 00 55 55 55
[11:14:59.289] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.289] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.289] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.339] 测试写DID 0x1100, 数据: 01...
[11:14:59.339] 发送: can0  715   [8]  04 2E 11 00 01 55 55 55
[11:14:59.340] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.340] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.340] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.390] 测试写DID 0x1100, 数据: FF...
[11:14:59.390] 发送: can0  715   [8]  04 2E 11 00 FF 55 55 55
[11:14:59.391] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.391] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.391] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.441] 测试写DID 0x1100, 数据: 00 00...
[11:14:59.441] 发送: can0  715   [8]  05 2E 11 00 00 00 55 55
[11:14:59.442] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.442] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.442] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.492] 测试写DID 0x1100, 数据: 00 01...
[11:14:59.492] 发送: can0  715   [8]  05 2E 11 00 00 01 55 55
[11:14:59.493] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.493] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.493] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.543] 测试写DID 0x1100, 数据: FF FF...
[11:14:59.544] 发送: can0  715   [8]  05 2E 11 00 FF FF 55 55
[11:14:59.544] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.544] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.544] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.594] 测试写DID 0x1100, 数据: 12 34...
[11:14:59.595] 发送: can0  715   [8]  05 2E 11 00 12 34 55 55
[11:14:59.595] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.595] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.595] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.645] 测试写DID 0x1100, 数据: 00 00 00...
[11:14:59.646] 发送: can0  715   [8]  06 2E 11 00 00 00 00 55
[11:14:59.646] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:14:59.646] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:14:59.646] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.646] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.646] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.696] 测试写DID 0x1100, 数据: 01 02 03...
[11:14:59.697] 发送: can0  715   [8]  06 2E 11 00 01 02 03 55
[11:14:59.698] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.698] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.698] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.748] 测试写DID 0x1100, 数据: 00 00 00 00...
[11:14:59.749] 发送: can0  715   [8]  07 2E 11 00 00 00 00 00
[11:14:59.750] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.750] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.750] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.801] 测试写DID 0x1100, 数据: 12 34 56 78...
[11:14:59.801] 发送: can0  715   [8]  07 2E 11 00 12 34 56 78
[11:14:59.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.802] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.853] 测试写DID 0x1100, 数据: FF FF FF FF...
[11:14:59.853] 发送: can0  715   [8]  07 2E 11 00 FF FF FF FF
[11:14:59.854] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.854] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.854] ❌ DID 0x1100: 负响应 NRC=0x13
[11:14:59.905] 测试写DID 0x1101, 数据: 00...
[11:14:59.905] 发送: can0  715   [8]  04 2E 11 01 00 55 55 55
[11:14:59.906] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.906] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.906] ❌ DID 0x1101: 负响应 NRC=0x13
[11:14:59.956] 测试写DID 0x1101, 数据: 01...
[11:14:59.957] 发送: can0  715   [8]  04 2E 11 01 01 55 55 55
[11:14:59.958] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.958] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:14:59.958] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.009] 测试写DID 0x1101, 数据: FF...
[11:15:00.009] 发送: can0  715   [8]  04 2E 11 01 FF 55 55 55
[11:15:00.009] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.009] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.009] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.060] 测试写DID 0x1101, 数据: 00 00...
[11:15:00.060] 发送: can0  715   [8]  05 2E 11 01 00 00 55 55
[11:15:00.060] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.060] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.060] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.111] 测试写DID 0x1101, 数据: 00 01...
[11:15:00.111] 发送: can0  715   [8]  05 2E 11 01 00 01 55 55
[11:15:00.111] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.111] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.111] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.161] 测试写DID 0x1101, 数据: FF FF...
[11:15:00.162] 发送: can0  715   [8]  05 2E 11 01 FF FF 55 55
[11:15:00.162] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:00.162] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:00.163] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.163] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.163] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.213] 测试写DID 0x1101, 数据: 12 34...
[11:15:00.214] 发送: can0  715   [8]  05 2E 11 01 12 34 55 55
[11:15:00.214] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.214] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.214] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.264] 测试写DID 0x1101, 数据: 00 00 00...
[11:15:00.265] 发送: can0  715   [8]  06 2E 11 01 00 00 00 55
[11:15:00.265] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.265] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.265] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.315] 测试写DID 0x1101, 数据: 01 02 03...
[11:15:00.316] 发送: can0  715   [8]  06 2E 11 01 01 02 03 55
[11:15:00.316] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.316] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.316] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.366] 测试写DID 0x1101, 数据: 00 00 00 00...
[11:15:00.367] 发送: can0  715   [8]  07 2E 11 01 00 00 00 00
[11:15:00.368] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.368] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.368] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.419] 测试写DID 0x1101, 数据: 12 34 56 78...
[11:15:00.419] 发送: can0  715   [8]  07 2E 11 01 12 34 56 78
[11:15:00.419] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.419] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.419] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.470] 测试写DID 0x1101, 数据: FF FF FF FF...
[11:15:00.470] 发送: can0  715   [8]  07 2E 11 01 FF FF FF FF
[11:15:00.471] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.471] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.471] ❌ DID 0x1101: 负响应 NRC=0x13
[11:15:00.522] 测试写DID 0x1102, 数据: 00...
[11:15:00.522] 发送: can0  715   [8]  04 2E 11 02 00 55 55 55
[11:15:00.522] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.522] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.522] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.573] 测试写DID 0x1102, 数据: 01...
[11:15:00.573] 发送: can0  715   [8]  04 2E 11 02 01 55 55 55
[11:15:00.573] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.573] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.573] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.623] 测试写DID 0x1102, 数据: FF...
[11:15:00.624] 发送: can0  715   [8]  04 2E 11 02 FF 55 55 55
[11:15:00.624] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:00.624] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:00.624] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.624] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.624] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.674] 测试写DID 0x1102, 数据: 00 00...
[11:15:00.675] 发送: can0  715   [8]  05 2E 11 02 00 00 55 55
[11:15:00.675] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.675] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.675] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.726] 测试写DID 0x1102, 数据: 00 01...
[11:15:00.726] 发送: can0  715   [8]  05 2E 11 02 00 01 55 55
[11:15:00.726] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.726] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.726] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.777] 测试写DID 0x1102, 数据: FF FF...
[11:15:00.777] 发送: can0  715   [8]  05 2E 11 02 FF FF 55 55
[11:15:00.778] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.778] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.778] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.829] 测试写DID 0x1102, 数据: 12 34...
[11:15:00.829] 发送: can0  715   [8]  05 2E 11 02 12 34 55 55
[11:15:00.829] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.829] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.829] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.880] 测试写DID 0x1102, 数据: 00 00 00...
[11:15:00.880] 发送: can0  715   [8]  06 2E 11 02 00 00 00 55
[11:15:00.880] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.880] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.880] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.931] 测试写DID 0x1102, 数据: 01 02 03...
[11:15:00.931] 发送: can0  715   [8]  06 2E 11 02 01 02 03 55
[11:15:00.931] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.931] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.931] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:00.982] 测试写DID 0x1102, 数据: 00 00 00 00...
[11:15:00.982] 发送: can0  715   [8]  07 2E 11 02 00 00 00 00
[11:15:00.982] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.982] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:00.982] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:01.033] 测试写DID 0x1102, 数据: 12 34 56 78...
[11:15:01.033] 发送: can0  715   [8]  07 2E 11 02 12 34 56 78
[11:15:01.033] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.033] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.033] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:01.084] 测试写DID 0x1102, 数据: FF FF FF FF...
[11:15:01.084] 发送: can0  715   [8]  07 2E 11 02 FF FF FF FF
[11:15:01.085] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.085] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.085] ❌ DID 0x1102: 负响应 NRC=0x13
[11:15:01.135] 测试写DID 0x1103, 数据: 00...
[11:15:01.135] 发送: can0  715   [8]  04 2E 11 03 00 55 55 55
[11:15:01.135] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:01.135] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:01.136] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.137] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.137] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.187] 测试写DID 0x1103, 数据: 01...
[11:15:01.187] 发送: can0  715   [8]  04 2E 11 03 01 55 55 55
[11:15:01.188] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.189] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.189] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.239] 测试写DID 0x1103, 数据: FF...
[11:15:01.239] 发送: can0  715   [8]  04 2E 11 03 FF 55 55 55
[11:15:01.239] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.239] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.239] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.290] 测试写DID 0x1103, 数据: 00 00...
[11:15:01.290] 发送: can0  715   [8]  05 2E 11 03 00 00 55 55
[11:15:01.290] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.290] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.291] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.341] 测试写DID 0x1103, 数据: 00 01...
[11:15:01.341] 发送: can0  715   [8]  05 2E 11 03 00 01 55 55
[11:15:01.342] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.342] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.342] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.392] 测试写DID 0x1103, 数据: FF FF...
[11:15:01.392] 发送: can0  715   [8]  05 2E 11 03 FF FF 55 55
[11:15:01.393] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.393] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.393] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.443] 测试写DID 0x1103, 数据: 12 34...
[11:15:01.443] 发送: can0  715   [8]  05 2E 11 03 12 34 55 55
[11:15:01.444] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.445] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.445] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.495] 测试写DID 0x1103, 数据: 00 00 00...
[11:15:01.495] 发送: can0  715   [8]  06 2E 11 03 00 00 00 55
[11:15:01.497] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.497] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.497] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.547] 测试写DID 0x1103, 数据: 01 02 03...
[11:15:01.547] 发送: can0  715   [8]  06 2E 11 03 01 02 03 55
[11:15:01.549] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.549] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.549] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.599] 测试写DID 0x1103, 数据: 00 00 00 00...
[11:15:01.599] 发送: can0  715   [8]  07 2E 11 03 00 00 00 00
[11:15:01.599] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.599] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.600] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.650] 测试写DID 0x1103, 数据: 12 34 56 78...
[11:15:01.650] 发送: can0  715   [8]  07 2E 11 03 12 34 56 78
[11:15:01.650] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:01.650] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:01.651] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.651] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.651] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.701] 测试写DID 0x1103, 数据: FF FF FF FF...
[11:15:01.701] 发送: can0  715   [8]  07 2E 11 03 FF FF FF FF
[11:15:01.701] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.702] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.702] ❌ DID 0x1103: 负响应 NRC=0x13
[11:15:01.752] 测试写DID 0x1104, 数据: 00...
[11:15:01.752] 发送: can0  715   [8]  04 2E 11 04 00 55 55 55
[11:15:01.752] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.753] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.753] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:01.803] 测试写DID 0x1104, 数据: 01...
[11:15:01.803] 发送: can0  715   [8]  04 2E 11 04 01 55 55 55
[11:15:01.804] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.804] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.804] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:01.854] 测试写DID 0x1104, 数据: FF...
[11:15:01.854] 发送: can0  715   [8]  04 2E 11 04 FF 55 55 55
[11:15:01.855] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.855] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.855] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:01.905] 测试写DID 0x1104, 数据: 00 00...
[11:15:01.905] 发送: can0  715   [8]  05 2E 11 04 00 00 55 55
[11:15:01.907] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.907] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.907] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:01.957] 测试写DID 0x1104, 数据: 00 01...
[11:15:01.957] 发送: can0  715   [8]  05 2E 11 04 00 01 55 55
[11:15:01.959] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.959] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:01.959] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.009] 测试写DID 0x1104, 数据: FF FF...
[11:15:02.009] 发送: can0  715   [8]  05 2E 11 04 FF FF 55 55
[11:15:02.010] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.010] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.010] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.060] 测试写DID 0x1104, 数据: 12 34...
[11:15:02.060] 发送: can0  715   [8]  05 2E 11 04 12 34 55 55
[11:15:02.061] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.061] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.061] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.111] 测试写DID 0x1104, 数据: 00 00 00...
[11:15:02.111] 发送: can0  715   [8]  06 2E 11 04 00 00 00 55
[11:15:02.113] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.113] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.113] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.163] 测试写DID 0x1104, 数据: 01 02 03...
[11:15:02.163] 发送: can0  715   [8]  06 2E 11 04 01 02 03 55
[11:15:02.163] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:02.163] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:02.164] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.164] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.164] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.214] 测试写DID 0x1104, 数据: 00 00 00 00...
[11:15:02.214] 发送: can0  715   [8]  07 2E 11 04 00 00 00 00
[11:15:02.215] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.215] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.215] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.265] 测试写DID 0x1104, 数据: 12 34 56 78...
[11:15:02.265] 发送: can0  715   [8]  07 2E 11 04 12 34 56 78
[11:15:02.266] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.266] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.266] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.316] 测试写DID 0x1104, 数据: FF FF FF FF...
[11:15:02.316] 发送: can0  715   [8]  07 2E 11 04 FF FF FF FF
[11:15:02.317] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.317] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.317] ❌ DID 0x1104: 负响应 NRC=0x13
[11:15:02.367] 测试写DID 0x2000, 数据: 00...
[11:15:02.367] 发送: can0  715   [8]  04 2E 20 00 00 55 55 55
[11:15:02.369] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.369] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.369] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.419] 测试写DID 0x2000, 数据: 01...
[11:15:02.419] 发送: can0  715   [8]  04 2E 20 00 01 55 55 55
[11:15:02.420] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.420] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.420] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.470] 测试写DID 0x2000, 数据: FF...
[11:15:02.470] 发送: can0  715   [8]  04 2E 20 00 FF 55 55 55
[11:15:02.471] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.471] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.471] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.521] 测试写DID 0x2000, 数据: 00 00...
[11:15:02.521] 发送: can0  715   [8]  05 2E 20 00 00 00 55 55
[11:15:02.522] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.522] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.522] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.572] 测试写DID 0x2000, 数据: 00 01...
[11:15:02.572] 发送: can0  715   [8]  05 2E 20 00 00 01 55 55
[11:15:02.573] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.573] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.573] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.623] 测试写DID 0x2000, 数据: FF FF...
[11:15:02.623] 发送: can0  715   [8]  05 2E 20 00 FF FF 55 55
[11:15:02.623] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:02.623] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:02.624] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.624] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.624] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.674] 测试写DID 0x2000, 数据: 12 34...
[11:15:02.674] 发送: can0  715   [8]  05 2E 20 00 12 34 55 55
[11:15:02.675] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.675] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.675] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.725] 测试写DID 0x2000, 数据: 00 00 00...
[11:15:02.725] 发送: can0  715   [8]  06 2E 20 00 00 00 00 55
[11:15:02.726] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.726] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.726] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.776] 测试写DID 0x2000, 数据: 01 02 03...
[11:15:02.776] 发送: can0  715   [8]  06 2E 20 00 01 02 03 55
[11:15:02.777] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.777] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.777] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.827] 测试写DID 0x2000, 数据: 00 00 00 00...
[11:15:02.827] 发送: can0  715   [8]  07 2E 20 00 00 00 00 00
[11:15:02.829] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.829] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.829] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.879] 测试写DID 0x2000, 数据: 12 34 56 78...
[11:15:02.879] 发送: can0  715   [8]  07 2E 20 00 12 34 56 78
[11:15:02.880] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.880] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.880] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.930] 测试写DID 0x2000, 数据: FF FF FF FF...
[11:15:02.930] 发送: can0  715   [8]  07 2E 20 00 FF FF FF FF
[11:15:02.931] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.931] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.931] ❌ DID 0x2000: 负响应 NRC=0x13
[11:15:02.981] 测试写DID 0x2001, 数据: 00...
[11:15:02.981] 发送: can0  715   [8]  04 2E 20 01 00 55 55 55
[11:15:02.982] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.982] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:02.982] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.032] 测试写DID 0x2001, 数据: 01...
[11:15:03.032] 发送: can0  715   [8]  04 2E 20 01 01 55 55 55
[11:15:03.033] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.033] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.033] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.083] 测试写DID 0x2001, 数据: FF...
[11:15:03.083] 发送: can0  715   [8]  04 2E 20 01 FF 55 55 55
[11:15:03.084] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.084] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.084] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.134] 测试写DID 0x2001, 数据: 00 00...
[11:15:03.134] 发送: can0  715   [8]  05 2E 20 01 00 00 55 55
[11:15:03.134] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:03.134] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:03.136] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.136] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.136] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.186] 测试写DID 0x2001, 数据: 00 01...
[11:15:03.186] 发送: can0  715   [8]  05 2E 20 01 00 01 55 55
[11:15:03.187] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.187] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.187] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.237] 测试写DID 0x2001, 数据: FF FF...
[11:15:03.237] 发送: can0  715   [8]  05 2E 20 01 FF FF 55 55
[11:15:03.239] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.239] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.239] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.289] 测试写DID 0x2001, 数据: 12 34...
[11:15:03.289] 发送: can0  715   [8]  05 2E 20 01 12 34 55 55
[11:15:03.290] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.290] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.290] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.340] 测试写DID 0x2001, 数据: 00 00 00...
[11:15:03.340] 发送: can0  715   [8]  06 2E 20 01 00 00 00 55
[11:15:03.341] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.341] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.341] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.391] 测试写DID 0x2001, 数据: 01 02 03...
[11:15:03.391] 发送: can0  715   [8]  06 2E 20 01 01 02 03 55
[11:15:03.392] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.392] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.392] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.442] 测试写DID 0x2001, 数据: 00 00 00 00...
[11:15:03.442] 发送: can0  715   [8]  07 2E 20 01 00 00 00 00
[11:15:03.443] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.443] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.443] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.493] 测试写DID 0x2001, 数据: 12 34 56 78...
[11:15:03.493] 发送: can0  715   [8]  07 2E 20 01 12 34 56 78
[11:15:03.494] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.494] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.494] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.544] 测试写DID 0x2001, 数据: FF FF FF FF...
[11:15:03.544] 发送: can0  715   [8]  07 2E 20 01 FF FF FF FF
[11:15:03.546] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.546] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.546] ❌ DID 0x2001: 负响应 NRC=0x13
[11:15:03.596] 测试写DID 0x2002, 数据: 00...
[11:15:03.596] 发送: can0  715   [8]  04 2E 20 02 00 55 55 55
[11:15:03.597] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.597] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.597] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.647] 测试写DID 0x2002, 数据: 01...
[11:15:03.647] 发送: can0  715   [8]  04 2E 20 02 01 55 55 55
[11:15:03.647] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:03.647] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:03.649] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.649] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.649] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.699] 测试写DID 0x2002, 数据: FF...
[11:15:03.699] 发送: can0  715   [8]  04 2E 20 02 FF 55 55 55
[11:15:03.700] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.700] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.700] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.750] 测试写DID 0x2002, 数据: 00 00...
[11:15:03.750] 发送: can0  715   [8]  05 2E 20 02 00 00 55 55
[11:15:03.751] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.751] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.751] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.801] 测试写DID 0x2002, 数据: 00 01...
[11:15:03.801] 发送: can0  715   [8]  05 2E 20 02 00 01 55 55
[11:15:03.802] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.802] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.802] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.852] 测试写DID 0x2002, 数据: FF FF...
[11:15:03.852] 发送: can0  715   [8]  05 2E 20 02 FF FF 55 55
[11:15:03.854] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.854] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.854] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.904] 测试写DID 0x2002, 数据: 12 34...
[11:15:03.904] 发送: can0  715   [8]  05 2E 20 02 12 34 55 55
[11:15:03.905] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.905] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.905] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:03.955] 测试写DID 0x2002, 数据: 00 00 00...
[11:15:03.955] 发送: can0  715   [8]  06 2E 20 02 00 00 00 55
[11:15:03.956] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.956] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:03.956] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:04.006] 测试写DID 0x2002, 数据: 01 02 03...
[11:15:04.006] 发送: can0  715   [8]  06 2E 20 02 01 02 03 55
[11:15:04.007] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.007] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.007] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:04.057] 测试写DID 0x2002, 数据: 00 00 00 00...
[11:15:04.057] 发送: can0  715   [8]  07 2E 20 02 00 00 00 00
[11:15:04.059] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.059] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.059] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:04.109] 测试写DID 0x2002, 数据: 12 34 56 78...
[11:15:04.109] 发送: can0  715   [8]  07 2E 20 02 12 34 56 78
[11:15:04.110] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.110] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.110] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:04.160] 测试写DID 0x2002, 数据: FF FF FF FF...
[11:15:04.160] 发送: can0  715   [8]  07 2E 20 02 FF FF FF FF
[11:15:04.160] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:04.160] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:04.161] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.161] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.161] ❌ DID 0x2002: 负响应 NRC=0x13
[11:15:04.211] 测试写DID 0x2003, 数据: 00...
[11:15:04.211] 发送: can0  715   [8]  04 2E 20 03 00 55 55 55
[11:15:04.212] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.212] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.212] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.262] 测试写DID 0x2003, 数据: 01...
[11:15:04.262] 发送: can0  715   [8]  04 2E 20 03 01 55 55 55
[11:15:04.263] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.263] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.263] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.313] 测试写DID 0x2003, 数据: FF...
[11:15:04.313] 发送: can0  715   [8]  04 2E 20 03 FF 55 55 55
[11:15:04.314] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.314] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.314] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.364] 测试写DID 0x2003, 数据: 00 00...
[11:15:04.364] 发送: can0  715   [8]  05 2E 20 03 00 00 55 55
[11:15:04.365] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.365] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.365] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.415] 测试写DID 0x2003, 数据: 00 01...
[11:15:04.415] 发送: can0  715   [8]  05 2E 20 03 00 01 55 55
[11:15:04.416] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.416] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.416] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.466] 测试写DID 0x2003, 数据: FF FF...
[11:15:04.466] 发送: can0  715   [8]  05 2E 20 03 FF FF 55 55
[11:15:04.467] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.467] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.467] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.517] 测试写DID 0x2003, 数据: 12 34...
[11:15:04.517] 发送: can0  715   [8]  05 2E 20 03 12 34 55 55
[11:15:04.519] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.519] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.519] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.569] 测试写DID 0x2003, 数据: 00 00 00...
[11:15:04.569] 发送: can0  715   [8]  06 2E 20 03 00 00 00 55
[11:15:04.570] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.570] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.570] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.620] 测试写DID 0x2003, 数据: 01 02 03...
[11:15:04.620] 发送: can0  715   [8]  06 2E 20 03 01 02 03 55
[11:15:04.620] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:04.620] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:04.621] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.621] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.621] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.671] 测试写DID 0x2003, 数据: 00 00 00 00...
[11:15:04.671] 发送: can0  715   [8]  07 2E 20 03 00 00 00 00
[11:15:04.672] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.672] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.672] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.722] 测试写DID 0x2003, 数据: 12 34 56 78...
[11:15:04.722] 发送: can0  715   [8]  07 2E 20 03 12 34 56 78
[11:15:04.723] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.723] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.723] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.773] 测试写DID 0x2003, 数据: FF FF FF FF...
[11:15:04.773] 发送: can0  715   [8]  07 2E 20 03 FF FF FF FF
[11:15:04.774] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.774] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.774] ❌ DID 0x2003: 负响应 NRC=0x13
[11:15:04.824] 测试写DID 0x2004, 数据: 00...
[11:15:04.824] 发送: can0  715   [8]  04 2E 20 04 00 55 55 55
[11:15:04.825] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.825] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.825] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:04.875] 测试写DID 0x2004, 数据: 01...
[11:15:04.875] 发送: can0  715   [8]  04 2E 20 04 01 55 55 55
[11:15:04.876] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.876] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.876] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:04.926] 测试写DID 0x2004, 数据: FF...
[11:15:04.926] 发送: can0  715   [8]  04 2E 20 04 FF 55 55 55
[11:15:04.927] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.927] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.927] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:04.977] 测试写DID 0x2004, 数据: 00 00...
[11:15:04.977] 发送: can0  715   [8]  05 2E 20 04 00 00 55 55
[11:15:04.979] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.979] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:04.979] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.029] 测试写DID 0x2004, 数据: 00 01...
[11:15:05.029] 发送: can0  715   [8]  05 2E 20 04 00 01 55 55
[11:15:05.030] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.030] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.030] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.080] 测试写DID 0x2004, 数据: FF FF...
[11:15:05.080] 发送: can0  715   [8]  05 2E 20 04 FF FF 55 55
[11:15:05.082] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.082] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.082] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.132] 测试写DID 0x2004, 数据: 12 34...
[11:15:05.132] 发送: can0  715   [8]  05 2E 20 04 12 34 55 55
[11:15:05.132] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:05.132] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:05.133] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.133] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.133] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.183] 测试写DID 0x2004, 数据: 00 00 00...
[11:15:05.183] 发送: can0  715   [8]  06 2E 20 04 00 00 00 55
[11:15:05.184] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.184] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.184] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.234] 测试写DID 0x2004, 数据: 01 02 03...
[11:15:05.234] 发送: can0  715   [8]  06 2E 20 04 01 02 03 55
[11:15:05.236] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.236] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.236] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.286] 测试写DID 0x2004, 数据: 00 00 00 00...
[11:15:05.286] 发送: can0  715   [8]  07 2E 20 04 00 00 00 00
[11:15:05.287] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.287] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.287] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.337] 测试写DID 0x2004, 数据: 12 34 56 78...
[11:15:05.337] 发送: can0  715   [8]  07 2E 20 04 12 34 56 78
[11:15:05.339] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.339] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.339] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.389] 测试写DID 0x2004, 数据: FF FF FF FF...
[11:15:05.389] 发送: can0  715   [8]  07 2E 20 04 FF FF FF FF
[11:15:05.390] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.390] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.390] ❌ DID 0x2004: 负响应 NRC=0x13
[11:15:05.440] 测试写DID 0x2010, 数据: 00...
[11:15:05.440] 发送: can0  715   [8]  04 2E 20 10 00 55 55 55
[11:15:05.441] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.441] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.441] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.491] 测试写DID 0x2010, 数据: 01...
[11:15:05.491] 发送: can0  715   [8]  04 2E 20 10 01 55 55 55
[11:15:05.492] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.492] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.492] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.542] 测试写DID 0x2010, 数据: FF...
[11:15:05.542] 发送: can0  715   [8]  04 2E 20 10 FF 55 55 55
[11:15:05.543] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.543] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.543] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.593] 测试写DID 0x2010, 数据: 00 00...
[11:15:05.593] 发送: can0  715   [8]  05 2E 20 10 00 00 55 55
[11:15:05.594] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.594] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.594] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.644] 测试写DID 0x2010, 数据: 00 01...
[11:15:05.644] 发送: can0  715   [8]  05 2E 20 10 00 01 55 55
[11:15:05.644] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:05.644] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:05.645] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.645] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.645] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.695] 测试写DID 0x2010, 数据: FF FF...
[11:15:05.695] 发送: can0  715   [8]  05 2E 20 10 FF FF 55 55
[11:15:05.696] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.696] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.696] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.746] 测试写DID 0x2010, 数据: 12 34...
[11:15:05.746] 发送: can0  715   [8]  05 2E 20 10 12 34 55 55
[11:15:05.747] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.747] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.747] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.797] 测试写DID 0x2010, 数据: 00 00 00...
[11:15:05.797] 发送: can0  715   [8]  06 2E 20 10 00 00 00 55
[11:15:05.799] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.799] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.799] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.849] 测试写DID 0x2010, 数据: 01 02 03...
[11:15:05.849] 发送: can0  715   [8]  06 2E 20 10 01 02 03 55
[11:15:05.850] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.850] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.850] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.900] 测试写DID 0x2010, 数据: 00 00 00 00...
[11:15:05.900] 发送: can0  715   [8]  07 2E 20 10 00 00 00 00
[11:15:05.901] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.901] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.901] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:05.951] 测试写DID 0x2010, 数据: 12 34 56 78...
[11:15:05.951] 发送: can0  715   [8]  07 2E 20 10 12 34 56 78
[11:15:05.953] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.953] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:05.953] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:06.003] 测试写DID 0x2010, 数据: FF FF FF FF...
[11:15:06.003] 发送: can0  715   [8]  07 2E 20 10 FF FF FF FF
[11:15:06.004] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.004] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.004] ❌ DID 0x2010: 负响应 NRC=0x13
[11:15:06.054] 测试写DID 0x2011, 数据: 00...
[11:15:06.054] 发送: can0  715   [8]  04 2E 20 11 00 55 55 55
[11:15:06.055] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.055] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.055] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.105] 测试写DID 0x2011, 数据: 01...
[11:15:06.105] 发送: can0  715   [8]  04 2E 20 11 01 55 55 55
[11:15:06.106] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.106] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.106] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.156] 测试写DID 0x2011, 数据: FF...
[11:15:06.156] 发送: can0  715   [8]  04 2E 20 11 FF 55 55 55
[11:15:06.156] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:06.156] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:06.157] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.157] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.157] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.207] 测试写DID 0x2011, 数据: 00 00...
[11:15:06.207] 发送: can0  715   [8]  05 2E 20 11 00 00 55 55
[11:15:06.209] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.209] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.209] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.259] 测试写DID 0x2011, 数据: 00 01...
[11:15:06.259] 发送: can0  715   [8]  05 2E 20 11 00 01 55 55
[11:15:06.260] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.260] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.260] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.310] 测试写DID 0x2011, 数据: FF FF...
[11:15:06.310] 发送: can0  715   [8]  05 2E 20 11 FF FF 55 55
[11:15:06.311] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.311] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.311] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.361] 测试写DID 0x2011, 数据: 12 34...
[11:15:06.361] 发送: can0  715   [8]  05 2E 20 11 12 34 55 55
[11:15:06.362] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.362] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.362] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.412] 测试写DID 0x2011, 数据: 00 00 00...
[11:15:06.412] 发送: can0  715   [8]  06 2E 20 11 00 00 00 55
[11:15:06.413] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.413] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.413] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.463] 测试写DID 0x2011, 数据: 01 02 03...
[11:15:06.463] 发送: can0  715   [8]  06 2E 20 11 01 02 03 55
[11:15:06.464] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.464] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.464] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.514] 测试写DID 0x2011, 数据: 00 00 00 00...
[11:15:06.514] 发送: can0  715   [8]  07 2E 20 11 00 00 00 00
[11:15:06.515] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.515] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.515] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.565] 测试写DID 0x2011, 数据: 12 34 56 78...
[11:15:06.565] 发送: can0  715   [8]  07 2E 20 11 12 34 56 78
[11:15:06.566] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.566] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.566] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.616] 测试写DID 0x2011, 数据: FF FF FF FF...
[11:15:06.617] 发送: can0  715   [8]  07 2E 20 11 FF FF FF FF
[11:15:06.617] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:06.617] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:06.618] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.618] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.618] ❌ DID 0x2011: 负响应 NRC=0x13
[11:15:06.668] 测试写DID 0x2012, 数据: 00...
[11:15:06.669] 发送: can0  715   [8]  04 2E 20 12 00 55 55 55
[11:15:06.670] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.670] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.670] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.720] 测试写DID 0x2012, 数据: 01...
[11:15:06.720] 发送: can0  715   [8]  04 2E 20 12 01 55 55 55
[11:15:06.721] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.721] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.721] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.771] 测试写DID 0x2012, 数据: FF...
[11:15:06.771] 发送: can0  715   [8]  04 2E 20 12 FF 55 55 55
[11:15:06.772] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.772] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.772] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.822] 测试写DID 0x2012, 数据: 00 00...
[11:15:06.822] 发送: can0  715   [8]  05 2E 20 12 00 00 55 55
[11:15:06.823] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.823] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.823] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.873] 测试写DID 0x2012, 数据: 00 01...
[11:15:06.873] 发送: can0  715   [8]  05 2E 20 12 00 01 55 55
[11:15:06.874] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.874] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.874] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.924] 测试写DID 0x2012, 数据: FF FF...
[11:15:06.924] 发送: can0  715   [8]  05 2E 20 12 FF FF 55 55
[11:15:06.925] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.925] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.925] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:06.975] 测试写DID 0x2012, 数据: 12 34...
[11:15:06.976] 发送: can0  715   [8]  05 2E 20 12 12 34 55 55
[11:15:06.976] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.976] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:06.976] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.026] 测试写DID 0x2012, 数据: 00 00 00...
[11:15:07.026] 发送: can0  715   [8]  06 2E 20 12 00 00 00 55
[11:15:07.027] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.027] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.027] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.077] 测试写DID 0x2012, 数据: 01 02 03...
[11:15:07.077] 发送: can0  715   [8]  06 2E 20 12 01 02 03 55
[11:15:07.079] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.079] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.079] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.129] 测试写DID 0x2012, 数据: 00 00 00 00...
[11:15:07.129] 发送: can0  715   [8]  07 2E 20 12 00 00 00 00
[11:15:07.130] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:07.130] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:07.130] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.130] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.130] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.180] 测试写DID 0x2012, 数据: 12 34 56 78...
[11:15:07.180] 发送: can0  715   [8]  07 2E 20 12 12 34 56 78
[11:15:07.181] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.181] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.181] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.231] 测试写DID 0x2012, 数据: FF FF FF FF...
[11:15:07.232] 发送: can0  715   [8]  07 2E 20 12 FF FF FF FF
[11:15:07.232] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.232] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.232] ❌ DID 0x2012: 负响应 NRC=0x13
[11:15:07.282] 测试写DID 0x2013, 数据: 00...
[11:15:07.283] 发送: can0  715   [8]  04 2E 20 13 00 55 55 55
[11:15:07.283] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.283] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.283] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.333] 测试写DID 0x2013, 数据: 01...
[11:15:07.334] 发送: can0  715   [8]  04 2E 20 13 01 55 55 55
[11:15:07.334] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.334] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.334] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.384] 测试写DID 0x2013, 数据: FF...
[11:15:07.385] 发送: can0  715   [8]  04 2E 20 13 FF 55 55 55
[11:15:07.385] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.385] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.385] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.435] 测试写DID 0x2013, 数据: 00 00...
[11:15:07.436] 发送: can0  715   [8]  05 2E 20 13 00 00 55 55
[11:15:07.436] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.436] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.436] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.486] 测试写DID 0x2013, 数据: 00 01...
[11:15:07.486] 发送: can0  715   [8]  05 2E 20 13 00 01 55 55
[11:15:07.487] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.487] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.487] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.537] 测试写DID 0x2013, 数据: FF FF...
[11:15:07.538] 发送: can0  715   [8]  05 2E 20 13 FF FF 55 55
[11:15:07.539] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.539] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.539] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.589] 测试写DID 0x2013, 数据: 12 34...
[11:15:07.590] 发送: can0  715   [8]  05 2E 20 13 12 34 55 55
[11:15:07.590] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.590] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.590] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.641] 测试写DID 0x2013, 数据: 00 00 00...
[11:15:07.641] 发送: can0  715   [8]  06 2E 20 13 00 00 00 55
[11:15:07.641] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:07.641] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:07.642] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.642] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.643] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.693] 测试写DID 0x2013, 数据: 01 02 03...
[11:15:07.693] 发送: can0  715   [8]  06 2E 20 13 01 02 03 55
[11:15:07.694] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.694] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.694] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.745] 测试写DID 0x2013, 数据: 00 00 00 00...
[11:15:07.745] 发送: can0  715   [8]  07 2E 20 13 00 00 00 00
[11:15:07.745] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.745] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.745] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.795] 测试写DID 0x2013, 数据: 12 34 56 78...
[11:15:07.796] 发送: can0  715   [8]  07 2E 20 13 12 34 56 78
[11:15:07.796] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.796] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.796] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.846] 测试写DID 0x2013, 数据: FF FF FF FF...
[11:15:07.847] 发送: can0  715   [8]  07 2E 20 13 FF FF FF FF
[11:15:07.847] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.847] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.847] ❌ DID 0x2013: 负响应 NRC=0x13
[11:15:07.897] 测试写DID 0x2014, 数据: 00...
[11:15:07.898] 发送: can0  715   [8]  04 2E 20 14 00 55 55 55
[11:15:07.899] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.899] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.899] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:07.950] 测试写DID 0x2014, 数据: 01...
[11:15:07.950] 发送: can0  715   [8]  04 2E 20 14 01 55 55 55
[11:15:07.951] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.951] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:07.951] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.002] 测试写DID 0x2014, 数据: FF...
[11:15:08.002] 发送: can0  715   [8]  04 2E 20 14 FF 55 55 55
[11:15:08.002] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.002] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.002] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.052] 测试写DID 0x2014, 数据: 00 00...
[11:15:08.053] 发送: can0  715   [8]  05 2E 20 14 00 00 55 55
[11:15:08.053] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.053] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.053] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.103] 测试写DID 0x2014, 数据: 00 01...
[11:15:08.104] 发送: can0  715   [8]  05 2E 20 14 00 01 55 55
[11:15:08.104] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.104] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.104] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.154] 测试写DID 0x2014, 数据: FF FF...
[11:15:08.155] 发送: can0  715   [8]  05 2E 20 14 FF FF 55 55
[11:15:08.155] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:08.155] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:08.155] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.155] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.155] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.205] 测试写DID 0x2014, 数据: 12 34...
[11:15:08.206] 发送: can0  715   [8]  05 2E 20 14 12 34 55 55
[11:15:08.206] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.206] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.206] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.257] 测试写DID 0x2014, 数据: 00 00 00...
[11:15:08.257] 发送: can0  715   [8]  06 2E 20 14 00 00 00 55
[11:15:08.257] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.257] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.257] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.308] 测试写DID 0x2014, 数据: 01 02 03...
[11:15:08.308] 发送: can0  715   [8]  06 2E 20 14 01 02 03 55
[11:15:08.309] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.309] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.309] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.360] 测试写DID 0x2014, 数据: 00 00 00 00...
[11:15:08.360] 发送: can0  715   [8]  07 2E 20 14 00 00 00 00
[11:15:08.361] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.361] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.361] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.412] 测试写DID 0x2014, 数据: 12 34 56 78...
[11:15:08.412] 发送: can0  715   [8]  07 2E 20 14 12 34 56 78
[11:15:08.412] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.412] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.412] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.463] 测试写DID 0x2014, 数据: FF FF FF FF...
[11:15:08.463] 发送: can0  715   [8]  07 2E 20 14 FF FF FF FF
[11:15:08.463] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.463] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.463] ❌ DID 0x2014: 负响应 NRC=0x13
[11:15:08.514] 测试写DID 0x2100, 数据: 00...
[11:15:08.514] 发送: can0  715   [8]  04 2E 21 00 00 55 55 55
[11:15:08.514] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.514] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.514] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.565] 测试写DID 0x2100, 数据: 01...
[11:15:08.565] 发送: can0  715   [8]  04 2E 21 00 01 55 55 55
[11:15:08.565] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.565] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.565] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.616] 测试写DID 0x2100, 数据: FF...
[11:15:08.616] 发送: can0  715   [8]  04 2E 21 00 FF 55 55 55
[11:15:08.616] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:08.616] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:08.616] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.616] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.617] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.667] 测试写DID 0x2100, 数据: 00 00...
[11:15:08.667] 发送: can0  715   [8]  05 2E 21 00 00 00 55 55
[11:15:08.667] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.667] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.667] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.718] 测试写DID 0x2100, 数据: 00 01...
[11:15:08.718] 发送: can0  715   [8]  05 2E 21 00 00 01 55 55
[11:15:08.719] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.719] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.719] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.770] 测试写DID 0x2100, 数据: FF FF...
[11:15:08.770] 发送: can0  715   [8]  05 2E 21 00 FF FF 55 55
[11:15:08.770] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.770] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.770] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.821] 测试写DID 0x2100, 数据: 12 34...
[11:15:08.821] 发送: can0  715   [8]  05 2E 21 00 12 34 55 55
[11:15:08.821] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.821] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.821] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.872] 测试写DID 0x2100, 数据: 00 00 00...
[11:15:08.872] 发送: can0  715   [8]  06 2E 21 00 00 00 00 55
[11:15:08.872] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.872] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.872] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.923] 测试写DID 0x2100, 数据: 01 02 03...
[11:15:08.923] 发送: can0  715   [8]  06 2E 21 00 01 02 03 55
[11:15:08.923] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.924] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.924] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:08.974] 测试写DID 0x2100, 数据: 00 00 00 00...
[11:15:08.974] 发送: can0  715   [8]  07 2E 21 00 00 00 00 00
[11:15:08.975] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.976] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:08.976] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:09.026] 测试写DID 0x2100, 数据: 12 34 56 78...
[11:15:09.026] 发送: can0  715   [8]  07 2E 21 00 12 34 56 78
[11:15:09.026] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.027] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.027] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:09.077] 测试写DID 0x2100, 数据: FF FF FF FF...
[11:15:09.077] 发送: can0  715   [8]  07 2E 21 00 FF FF FF FF
[11:15:09.077] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.078] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.078] ❌ DID 0x2100: 负响应 NRC=0x13
[11:15:09.128] 测试写DID 0x2101, 数据: 00...
[11:15:09.128] 发送: can0  715   [8]  04 2E 21 01 00 55 55 55
[11:15:09.128] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:09.128] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:09.129] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.130] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.130] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.180] 测试写DID 0x2101, 数据: 01...
[11:15:09.180] 发送: can0  715   [8]  04 2E 21 01 01 55 55 55
[11:15:09.181] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.181] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.181] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.231] 测试写DID 0x2101, 数据: FF...
[11:15:09.231] 发送: can0  715   [8]  04 2E 21 01 FF 55 55 55
[11:15:09.232] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.232] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.232] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.282] 测试写DID 0x2101, 数据: 00 00...
[11:15:09.282] 发送: can0  715   [8]  05 2E 21 01 00 00 55 55
[11:15:09.283] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.283] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.283] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.333] 测试写DID 0x2101, 数据: 00 01...
[11:15:09.333] 发送: can0  715   [8]  05 2E 21 01 00 01 55 55
[11:15:09.334] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.334] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.334] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.384] 测试写DID 0x2101, 数据: FF FF...
[11:15:09.384] 发送: can0  715   [8]  05 2E 21 01 FF FF 55 55
[11:15:09.384] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.385] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.385] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.435] 测试写DID 0x2101, 数据: 12 34...
[11:15:09.435] 发送: can0  715   [8]  05 2E 21 01 12 34 55 55
[11:15:09.436] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.436] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.436] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.486] 测试写DID 0x2101, 数据: 00 00 00...
[11:15:09.486] 发送: can0  715   [8]  06 2E 21 01 00 00 00 55
[11:15:09.487] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.487] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.487] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.537] 测试写DID 0x2101, 数据: 01 02 03...
[11:15:09.537] 发送: can0  715   [8]  06 2E 21 01 01 02 03 55
[11:15:09.538] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.538] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.538] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.588] 测试写DID 0x2101, 数据: 00 00 00 00...
[11:15:09.588] 发送: can0  715   [8]  07 2E 21 01 00 00 00 00
[11:15:09.590] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.590] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.590] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.640] 测试写DID 0x2101, 数据: 12 34 56 78...
[11:15:09.640] 发送: can0  715   [8]  07 2E 21 01 12 34 56 78
[11:15:09.640] 接收到帧: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:15:09.640] 忽略非目标ID帧: 0x3A1 (期望0x795)
[11:15:09.641] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.641] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.641] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.691] 测试写DID 0x2101, 数据: FF FF FF FF...
[11:15:09.691] 发送: can0  715   [8]  07 2E 21 01 FF FF FF FF
[11:15:09.692] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.692] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.692] ❌ DID 0x2101: 负响应 NRC=0x13
[11:15:09.742] 测试写DID 0x2102, 数据: 00...
[11:15:09.742] 发送: can0  715   [8]  04 2E 21 02 00 55 55 55
[11:15:09.743] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.743] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.743] ❌ DID 0x2102: 负响应 NRC=0x13
[11:15:09.793] 测试写DID 0x2102, 数据: 01...
[11:15:09.793] 发送: can0  715   [8]  04 2E 21 02 01 55 55 55
[11:15:09.794] 接收到帧: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.794] 接收目标响应: can0  795   [8]  03 7F 2E 13 55 55 55 55
[11:15:09.794] ❌ DID 0x2102: 负响应 NRC=0x13
