XCP连接扫描日志 - 2025-06-26 11:34:17.396593
扫描范围: 0x700 - 0x7DF
CAN接口: can0
============================================================

[11:34:17.396] 发送: can0  700   [8]  FF 00 00 00 00 00 00 00
[11:34:17.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:17.902] ✅ 发现XCP ECU: 请求ID=0x700, 响应数=1
[11:34:17.902]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:17.902] 
[11:34:17.922] 发送: can0  701   [8]  FF 00 00 00 00 00 00 00
[11:34:18.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:18.452] ✅ 发现XCP ECU: 请求ID=0x701, 响应数=1
[11:34:18.452]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:18.452] 
[11:34:18.472] 发送: can0  702   [8]  FF 00 00 00 00 00 00 00
[11:34:18.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:19.002] ✅ 发现XCP ECU: 请求ID=0x702, 响应数=1
[11:34:19.002]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:19.002] 
[11:34:19.022] 发送: can0  703   [8]  FF 00 00 00 00 00 00 00
[11:34:19.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:19.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:19.551] ✅ 发现XCP ECU: 请求ID=0x703, 响应数=2
[11:34:19.551]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:19.551]   响应2: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:19.551] 
[11:34:19.571] 发送: can0  704   [8]  FF 00 00 00 00 00 00 00
[11:34:20.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:20.101] ✅ 发现XCP ECU: 请求ID=0x704, 响应数=1
[11:34:20.101]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:20.101] 
[11:34:20.121] 发送: can0  705   [8]  FF 00 00 00 00 00 00 00
[11:34:20.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:20.651] ✅ 发现XCP ECU: 请求ID=0x705, 响应数=1
[11:34:20.651]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:20.651] 
[11:34:20.671] 发送: can0  706   [8]  FF 00 00 00 00 00 00 00
[11:34:21.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:21.201] ✅ 发现XCP ECU: 请求ID=0x706, 响应数=1
[11:34:21.201]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:21.201] 
[11:34:21.222] 发送: can0  707   [8]  FF 00 00 00 00 00 00 00
[11:34:21.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:21.751] ✅ 发现XCP ECU: 请求ID=0x707, 响应数=1
[11:34:21.751]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:21.751] 
[11:34:21.772] 发送: can0  708   [8]  FF 00 00 00 00 00 00 00
[11:34:22.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:22.301] ✅ 发现XCP ECU: 请求ID=0x708, 响应数=1
[11:34:22.301]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:22.301] 
[11:34:22.322] 发送: can0  709   [8]  FF 00 00 00 00 00 00 00
[11:34:22.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:22.851] ✅ 发现XCP ECU: 请求ID=0x709, 响应数=1
[11:34:22.851]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:22.851] 
[11:34:22.872] 发送: can0  70A   [8]  FF 00 00 00 00 00 00 00
[11:34:23.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:23.401] ✅ 发现XCP ECU: 请求ID=0x70A, 响应数=1
[11:34:23.401]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:23.401] 
[11:34:23.422] 发送: can0  70B   [8]  FF 00 00 00 00 00 00 00
[11:34:23.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:23.951] ✅ 发现XCP ECU: 请求ID=0x70B, 响应数=1
[11:34:23.951]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:23.951] 
[11:34:23.972] 发送: can0  70C   [8]  FF 00 00 00 00 00 00 00
[11:34:24.051] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:24.502] ✅ 发现XCP ECU: 请求ID=0x70C, 响应数=1
[11:34:24.502]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:24.502] 
[11:34:24.522] 发送: can0  70D   [8]  FF 00 00 00 00 00 00 00
[11:34:24.551] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:25.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:25.051] ✅ 发现XCP ECU: 请求ID=0x70D, 响应数=2
[11:34:25.051]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:25.051]   响应2: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:25.051] 
[11:34:25.071] 发送: can0  70E   [8]  FF 00 00 00 00 00 00 00
[11:34:25.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:25.601] ✅ 发现XCP ECU: 请求ID=0x70E, 响应数=1
[11:34:25.601]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:25.601] 
[11:34:25.621] 发送: can0  70F   [8]  FF 00 00 00 00 00 00 00
[11:34:26.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:26.151] ✅ 发现XCP ECU: 请求ID=0x70F, 响应数=1
[11:34:26.151]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:26.151] 
[11:34:26.171] 发送: can0  710   [8]  FF 00 00 00 00 00 00 00
[11:34:26.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:26.701] ✅ 发现XCP ECU: 请求ID=0x710, 响应数=1
[11:34:26.701]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:26.701] 
[11:34:26.721] 发送: can0  711   [8]  FF 00 00 00 00 00 00 00
[11:34:27.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:27.251] ✅ 发现XCP ECU: 请求ID=0x711, 响应数=1
[11:34:27.251]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:27.251] 
[11:34:27.271] 发送: can0  712   [8]  FF 00 00 00 00 00 00 00
[11:34:27.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:27.801] ✅ 发现XCP ECU: 请求ID=0x712, 响应数=1
[11:34:27.801]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:27.801] 
[11:34:27.821] 发送: can0  713   [8]  FF 00 00 00 00 00 00 00
[11:34:28.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:28.351] ✅ 发现XCP ECU: 请求ID=0x713, 响应数=1
[11:34:28.351]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:28.351] 
[11:34:28.371] 发送: can0  714   [8]  FF 00 00 00 00 00 00 00
[11:34:28.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:28.901] ✅ 发现XCP ECU: 请求ID=0x714, 响应数=1
[11:34:28.901]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:28.901] 
[11:34:28.922] 发送: can0  715   [8]  FF 00 00 00 00 00 00 00
[11:34:29.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:29.451] ✅ 发现XCP ECU: 请求ID=0x715, 响应数=1
[11:34:29.451]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:29.451] 
[11:34:29.471] 发送: can0  716   [8]  FF 00 00 00 00 00 00 00
[11:34:29.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:30.001] ✅ 发现XCP ECU: 请求ID=0x716, 响应数=1
[11:34:30.001]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:30.001] 
[11:34:30.022] 发送: can0  717   [8]  FF 00 00 00 00 00 00 00
[11:34:30.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:30.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:30.550] ✅ 发现XCP ECU: 请求ID=0x717, 响应数=2
[11:34:30.550]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:30.550]   响应2: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:30.550] 
[11:34:30.570] 发送: can0  718   [8]  FF 00 00 00 00 00 00 00
[11:34:31.050] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:31.100] ✅ 发现XCP ECU: 请求ID=0x718, 响应数=1
[11:34:31.101]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:31.101] 
[11:34:31.121] 发送: can0  719   [8]  FF 00 00 00 00 00 00 00
[11:34:31.550] 接收: can0  3A1   [8]  12 00 00 60 1C 00 00 02
[11:34:31.650] ✅ 发现XCP ECU: 请求ID=0x719, 响应数=1
[11:34:31.650]   响应1: ID=0x3A1, 数据=12 00 00 60 1C 00 00 02, 状态=可能的XCP响应
[11:34:31.650] 
[11:34:31.672] 
XCP扫描结果摘要:
[11:34:31.672] ====================
[11:34:31.672] 发现 26 个可能的XCP ECU
[11:34:31.672] XCP ECU: 0x700 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x701 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x702 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x703 -> 2 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x704 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x705 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x706 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x707 -> 1 个响应
[11:34:31.672]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.672] XCP ECU: 0x708 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x709 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70A -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70B -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70C -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70D -> 2 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70E -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x70F -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x710 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x711 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x712 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x713 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x714 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x715 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x716 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x717 -> 2 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x718 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
[11:34:31.673] XCP ECU: 0x719 -> 1 个响应
[11:34:31.673]   -> 0x3A1: 12 00 00 60 1C 00 00 02 (其他响应)
