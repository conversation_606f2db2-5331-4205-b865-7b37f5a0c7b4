UDS 27服务AES密钥破解 - 2025-06-22 20:21:18.116681
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:21:18.116] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:18.116] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:18.117] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:18.117] ✅ UDS 10 01 成功 (第1次尝试)
[20:21:18.617] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:18.617] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:18.618] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:21:18.618] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:18.618] === 第  1次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:21:18.618] 第  1次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:18.618] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:18.618] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:18.619] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:18.619] ✅ UDS 10 01 成功 (第1次尝试)
[20:21:19.620] 第  1次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:19.620] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:19.620] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:19.622] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:21:19.622] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:20.623] 第  1次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:21:20.623] 第  1次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:21:20.631] 第  1次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:21:20.631] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:21:20.631] === 第  1次测试结束 ===
[20:21:22.633] === 第  2次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:21:22.633] 第  2次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:22.633] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:22.633] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:22.633] 接收: can0  715   [3]  02 27 03
[20:21:22.633] 接收: can0  795   [8]  10 12 67 03 E8 6B 85 41
[20:21:22.633] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:22.633] 等待1秒后重试...
[20:21:23.635] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:23.635] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:23.635] 接收: can0  715   [3]  30 00 00
[20:21:23.635] 接收: can0  795   [8]  21 7D A3 A4 D6 A0 B9 4A
[20:21:23.635] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:23.635] 等待1秒后重试...
[20:21:24.636] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:24.636] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:24.636] 接收: can0  795   [8]  22 51 69 FE 28 4D 55 55
[20:21:24.636] ❌ UDS 10 01 失败 (第3次尝试)
[20:21:24.636] ❌ UDS 10 01 最终失败 (已重试3次)
[20:21:24.636] 第  2次: UDS 10 01 最终失败，测试终止
[20:21:24.636] === 第  3次测试开始: 密钥 00000000000000000000000000000000 ===
[20:21:24.636] 第  3次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:24.636] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:24.636] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:24.636] 接收: can0  715   [8]  10 12 27 04 E7 BA 8B 87
[20:21:24.636] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:21:24.636] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:24.636] 等待1秒后重试...
[20:21:25.637] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:25.637] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:25.637] 接收: can0  715   [8]  21 45 19 56 42 92 44 88
[20:21:25.637] 接收: can0  715   [6]  22 84 45 67 F7 BF
[20:21:25.637] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[20:21:25.637] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:25.637] 等待1秒后重试...
[20:21:26.639] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:26.639] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:26.639] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:26.639] ✅ UDS 10 01 成功 (第3次尝试)
[20:21:27.640] 第  3次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:27.640] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:27.640] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:27.640] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:27.640] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:28.641] 第  3次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[20:21:28.641] 第  3次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[20:21:28.649] 第  3次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[20:21:28.650] 第  3次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:21:28.650] === 第  3次测试结束 ===
[20:21:30.652] === 第  4次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:21:30.652] 第  4次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:30.652] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:30.652] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:30.652] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:30.652] ✅ UDS 10 01 成功 (第1次尝试)
[20:21:31.653] 第  4次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:31.653] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:31.653] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:31.653] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:31.653] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:32.654] 第  4次 - 步骤3: 执行UDS 27测试 (密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
[20:21:32.654] 第  4次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF -v
[20:21:32.663] 第  4次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFFFFFFFFFFFFFFF FFFFFFFFFFFFFFFF

CAN接口 can0 初始化...
[20:21:32.663] 第  4次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ❌ 失败 (未收到 02 67 04)
[20:21:32.663] === 第  4次测试结束 ===
[20:21:34.665] === 第  5次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:21:34.665] 第  5次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:34.665] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:34.665] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:34.665] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:34.665] ✅ UDS 10 01 成功 (第1次尝试)
[20:21:35.666] 第  5次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:35.667] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:35.667] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:35.667] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:21:35.667] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:36.667] 第  5次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:21:36.667] 第  5次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:21:36.676] 第  5次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:21:36.676] 第  5次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:21:36.676] === 第  5次测试结束 ===
[20:21:38.677] === 第  6次测试开始: 密钥 11111111111111111111111111111111 ===
[20:21:38.677] 第  6次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:38.677] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:38.677] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:38.677] 接收: can0  715   [3]  02 27 03
[20:21:38.677] 接收: can0  795   [8]  10 12 67 03 65 58 E6 94
[20:21:38.677] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:38.678] 等待1秒后重试...
[20:21:39.679] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:39.679] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:39.679] 接收: can0  715   [3]  30 00 00
[20:21:39.679] 接收: can0  795   [8]  21 00 B4 83 06 6E BE 5B
[20:21:39.679] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:39.679] 等待1秒后重试...
[20:21:40.679] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:40.679] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:40.679] 接收: can0  795   [8]  22 58 C3 6C F4 1C 55 55
[20:21:40.679] ❌ UDS 10 01 失败 (第3次尝试)
[20:21:40.679] ❌ UDS 10 01 最终失败 (已重试3次)
[20:21:40.679] 第  6次: UDS 10 01 最终失败，测试终止
[20:21:40.679] === 第  7次测试开始: 密钥 22222222222222222222222222222222 ===
[20:21:40.679] 第  7次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:40.679] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:40.679] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:40.679] 接收: can0  715   [8]  10 12 27 04 F4 00 42 69
[20:21:40.680] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:21:40.680] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:40.680] 等待1秒后重试...
[20:21:41.680] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:41.681] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:41.681] 接收: can0  715   [8]  21 0E FD 6E D2 40 D6 77
[20:21:41.681] 接收: can0  715   [6]  22 49 69 3D 4E F1
[20:21:41.681] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:21:41.681] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:41.681] 等待1秒后重试...
[20:21:42.681] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:42.681] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:42.681] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:42.681] ✅ UDS 10 01 成功 (第3次尝试)
[20:21:43.682] 第  7次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:43.682] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:43.682] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:43.682] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:21:43.682] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:44.683] 第  7次 - 步骤3: 执行UDS 27测试 (密钥: 22222222222222222222222222222222)
[20:21:44.683] 第  7次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 22222222222222222222222222222222 -v
[20:21:44.691] 第  7次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2222222222222222 2222222222222222

CAN接口 can0 初始化...
[20:21:44.691] 第  7次: 密钥 22222222222222222222222222222222 - ❌ 失败 (未收到 02 67 04)
[20:21:44.691] === 第  7次测试结束 ===
[20:21:46.693] === 第  8次测试开始: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ===
[20:21:46.693] 第  8次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:46.693] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:46.693] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:46.693] 接收: can0  715   [3]  02 27 03
[20:21:46.693] 接收: can0  795   [8]  10 12 67 03 B9 E4 5B 7B
[20:21:46.693] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:46.693] 等待1秒后重试...
[20:21:47.695] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:47.695] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:47.695] 接收: can0  715   [3]  30 00 00
[20:21:47.695] 接收: can0  795   [8]  21 43 31 5F 22 29 AD 1E
[20:21:47.695] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:47.695] 等待1秒后重试...
[20:21:48.696] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:48.696] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:48.696] 接收: can0  795   [8]  22 0F 86 86 33 42 55 55
[20:21:48.696] ❌ UDS 10 01 失败 (第3次尝试)
[20:21:48.696] ❌ UDS 10 01 最终失败 (已重试3次)
[20:21:48.696] 第  8次: UDS 10 01 最终失败，测试终止
[20:21:48.696] === 第  9次测试开始: 密钥 55555555555555555555555555555555 ===
[20:21:48.696] 第  9次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:48.696] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:48.696] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:48.696] 接收: can0  715   [8]  10 12 27 04 D8 E7 D4 0E
[20:21:48.696] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:21:48.696] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:48.696] 等待1秒后重试...
[20:21:49.697] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:49.697] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:49.697] 接收: can0  715   [8]  21 0B DB 74 F4 3D 88 11
[20:21:49.697] 接收: can0  715   [6]  22 01 CF 95 18 2A
[20:21:49.697] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:21:49.697] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:49.697] 等待1秒后重试...
[20:21:50.698] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:50.698] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:50.698] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:50.698] ✅ UDS 10 01 成功 (第3次尝试)
[20:21:51.699] 第  9次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:51.699] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:51.699] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:51.699] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:21:51.699] ✅ UDS 10 03 成功 (第1次尝试)
[20:21:52.700] 第  9次 - 步骤3: 执行UDS 27测试 (密钥: 55555555555555555555555555555555)
[20:21:52.700] 第  9次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 55555555555555555555555555555555 -v
[20:21:52.708] 第  9次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5555555555555555 5555555555555555

CAN接口 can0 初始化...
[20:21:52.708] 第  9次: 密钥 55555555555555555555555555555555 - ❌ 失败 (未收到 02 67 04)
[20:21:52.708] === 第  9次测试结束 ===
[20:21:54.709] === 第 10次测试开始: 密钥 1234567890ABCDEF1234567890ABCDEF ===
[20:21:54.709] 第 10次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:54.709] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:54.709] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:54.709] 接收: can0  715   [3]  02 27 03
[20:21:54.709] 接收: can0  795   [8]  10 12 67 03 2D D0 65 8E
[20:21:54.709] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:54.709] 等待1秒后重试...
[20:21:55.711] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:55.711] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:55.711] 接收: can0  715   [3]  30 00 00
[20:21:55.711] 接收: can0  795   [8]  21 7B E2 C8 9B D4 BA A3
[20:21:55.711] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:55.711] 等待1秒后重试...
[20:21:56.712] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:56.712] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:56.712] 接收: can0  795   [8]  22 21 00 E4 B4 5F 55 55
[20:21:56.712] ❌ UDS 10 01 失败 (第3次尝试)
[20:21:56.712] ❌ UDS 10 01 最终失败 (已重试3次)
[20:21:56.712] 第 10次: UDS 10 01 最终失败，测试终止
[20:21:56.712] === 第 11次测试开始: 密钥 ABCDEF1234567890ABCDEF1234567890 ===
[20:21:56.712] 第 11次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:21:56.712] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:21:56.712] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:56.712] 接收: can0  715   [8]  10 12 27 04 DE 47 D2 BB
[20:21:56.712] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:21:56.712] ❌ UDS 10 01 失败 (第1次尝试)
[20:21:56.712] 等待1秒后重试...
[20:21:57.713] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:21:57.713] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:57.713] 接收: can0  715   [8]  21 4B 8F 2F 0E 93 CD DA
[20:21:57.713] 接收: can0  715   [6]  22 DE B6 47 58 25
[20:21:57.713] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:21:57.713] ❌ UDS 10 01 失败 (第2次尝试)
[20:21:57.713] 等待1秒后重试...
[20:21:58.714] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:21:58.715] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:21:58.715] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:58.715] ✅ UDS 10 01 成功 (第3次尝试)
[20:21:59.715] 第 11次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:21:59.715] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:21:59.716] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:21:59.716] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:21:59.716] ✅ UDS 10 03 成功 (第1次尝试)
[20:22:00.717] 第 11次 - 步骤3: 执行UDS 27测试 (密钥: ABCDEF1234567890ABCDEF1234567890)
[20:22:00.717] 第 11次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABCDEF1234567890ABCDEF1234567890 -v
[20:22:00.725] 第 11次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABCDEF1234567890 ABCDEF1234567890

CAN接口 can0 初始化...
[20:22:00.725] 第 11次: 密钥 ABCDEF1234567890ABCDEF1234567890 - ❌ 失败 (未收到 02 67 04)
[20:22:00.725] === 第 11次测试结束 ===
[20:22:02.726] === 第 12次测试开始: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 ===
[20:22:02.726] 第 12次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:22:02.726] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:22:02.726] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:02.726] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:02.726] ✅ UDS 10 01 成功 (第1次尝试)
[20:22:03.727] 第 12次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:22:03.727] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:22:03.727] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:03.727] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:03.727] ✅ UDS 10 03 成功 (第1次尝试)
[20:22:04.728] 第 12次 - 步骤3: 执行UDS 27测试 (密钥: A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5)
[20:22:04.729] 第 12次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 -v
[20:22:04.737] 第 12次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: A5A5A5A5A5A5A5A5 A5A5A5A5A5A5A5A5

CAN接口 can0 初始化...
[20:22:04.737] 第 12次: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 - ❌ 失败 (未收到 02 67 04)
[20:22:04.737] === 第 12次测试结束 ===
[20:22:06.739] === 第 13次测试开始: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A ===
[20:22:06.739] 第 13次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:22:06.739] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:22:06.740] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:06.740] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:06.740] ✅ UDS 10 01 成功 (第1次尝试)
[20:22:07.741] 第 13次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:22:07.741] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:22:07.741] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:07.741] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:07.741] ✅ UDS 10 03 成功 (第1次尝试)
[20:22:08.741] 第 13次 - 步骤3: 执行UDS 27测试 (密钥: 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A)
[20:22:08.741] 第 13次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A -v
[20:22:08.750] 第 13次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5A5A5A5A5A5A5A5A 5A5A5A5A5A5A5A5A

CAN接口 can0 初始化...
[20:22:08.750] 第 13次: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A - ❌ 失败 (未收到 02 67 04)
[20:22:08.750] === 第 13次测试结束 ===
[20:22:10.752] === 第 14次测试开始: 密钥 20240101000000000000000000000000 ===
[20:22:10.752] 第 14次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:22:10.752] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:22:10.752] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:10.752] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:22:10.752] ✅ UDS 10 01 成功 (第1次尝试)
[20:22:11.753] 第 14次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:22:11.753] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:22:11.753] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:11.753] 接收: can0  715   [3]  02 27 03
[20:22:11.753] 接收: can0  795   [8]  10 12 67 03 01 AB 9C 74
[20:22:11.753] ❌ UDS 10 03 失败 (第1次尝试)
[20:22:11.753] 等待1秒后重试...
[20:22:12.754] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:22:12.755] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:12.755] 接收: can0  715   [3]  30 00 00
[20:22:12.755] 接收: can0  795   [8]  21 29 4D 48 39 6B B4 F2
[20:22:12.755] ❌ UDS 10 03 失败 (第2次尝试)
[20:22:12.755] 等待1秒后重试...
[20:22:13.756] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:22:13.756] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:13.756] 接收: can0  795   [8]  22 ED 89 ED 4D B8 55 55
[20:22:13.756] ❌ UDS 10 03 失败 (第3次尝试)
[20:22:13.756] ❌ UDS 10 03 最终失败 (已重试3次)
[20:22:13.756] 第 14次: UDS 10 03 最终失败，测试终止
[20:22:13.756] === 第 15次测试开始: 密钥 20230101000000000000000000000000 ===
[20:22:13.756] 第 15次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:22:13.756] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:22:13.756] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:13.756] 接收: can0  715   [8]  10 12 27 04 DB 6D 87 53
[20:22:13.756] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:22:13.756] ❌ UDS 10 01 失败 (第1次尝试)
[20:22:13.756] 等待1秒后重试...
[20:22:14.757] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:22:14.757] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:14.757] 接收: can0  715   [8]  21 1E 30 DE 62 01 3B 80
[20:22:14.757] 接收: can0  715   [6]  22 49 80 4E 5E 84
[20:22:14.757] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:22:14.757] ❌ UDS 10 01 失败 (第2次尝试)
[20:22:14.757] 等待1秒后重试...
[20:22:15.759] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:22:15.759] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:22:15.759] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:15.759] ✅ UDS 10 01 成功 (第3次尝试)
[20:22:16.759] 第 15次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:22:16.759] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:22:16.759] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:22:16.759] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:22:16.759] ✅ UDS 10 03 成功 (第1次尝试)
[20:22:17.760] 第 15次 - 步骤3: 执行UDS 27测试 (密钥: 20230101000000000000000000000000)
[20:22:17.760] 第 15次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20230101000000000000000000000000 -v
[20:22:17.769] 第 15次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2023010100000000 0000000000000000

CAN接口 can0 初始化...
[20:22:17.769] 第 15次: 密钥 20230101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:22:17.769] === 第 15次测试结束 ===
