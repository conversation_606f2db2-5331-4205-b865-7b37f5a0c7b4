#!/usr/bin/env python3
"""
UDS CAN ID扫描工具 (55填充版本)
通过向0x700-0x7FF范围的CAN ID发送UDS 10 01报文（用0x55填充到8字节），
发现响应的ECU并记录通信日志。
"""

import socket
import struct
import time
import sys
import signal
from datetime import datetime

# CAN帧格式常量
CAN_RAW = 1
CAN_EFF_FLAG = 0x80000000
CAN_RTR_FLAG = 0x40000000
CAN_ERR_FLAG = 0x20000000

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class UDSScanner:
    """UDS CAN ID扫描器 (55填充版本)"""
    
    def __init__(self, interface='can0'):
        self.interface = interface
        self.socket = None
        self.running = True
        self.log_file = None
        self.discovered_ecus = []
        
        # UDS 10 01 报文，用0x55填充到8字节 (诊断会话控制 - 默认会话)
        self.uds_request = b'\x02\x10\x01\x55\x55\x55\x55\x55'
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.running = False
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置接收超时
            self.socket.settimeout(0.1)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        import os

        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"uds_req_canid_55_fill_{timestamp}.log")

        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS CAN ID扫描日志 (55填充版本) - {datetime.now()}\n")
            self.log_file.write("=" * 50 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}")
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=0.5):
        """接收CAN帧"""
        try:
            # 临时设置超时
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(timeout)
            
            data = self.socket.recv(16)
            frame = CANFrame.unpack(data)
            
            # 恢复原超时设置
            self.socket.settimeout(original_timeout)
            
            self.log_message(f"接收: {frame}")
            return frame
            
        except socket.timeout:
            return None
        except Exception as e:
            self.log_message(f"接收失败: {e}")
            return None
    
    def is_positive_response(self, frame, request_id):
        """检查是否为UDS正响应"""
        if frame is None or len(frame.data) < 2:
            return False
        
        # UDS正响应格式: 长度 + 服务ID+0x40 + 子功能
        # 对于10 01请求，正响应应该是: 02 50 01 或类似格式
        if frame.data[0] >= 0x02 and frame.data[1] == 0x50:
            return True
        
        return False
    
    def is_negative_response(self, frame):
        """检查是否为UDS负响应"""
        if frame is None or len(frame.data) < 3:
            return False
        
        # UDS负响应格式: 长度 + 7F + 原服务ID + NRC
        if frame.data[0] == 0x03 and frame.data[1] == 0x7F and frame.data[2] == 0x10:
            return True
        
        return False
    
    def scan_can_id(self, request_id):
        """扫描单个CAN ID"""
        # 创建UDS请求帧 (8字节，用0x55填充)
        request_frame = CANFrame(request_id, self.uds_request)
        
        # 发送请求
        if not self.send_can_frame(request_frame):
            return None
        
        # 等待响应
        start_time = time.time()
        timeout = 0.5
        
        while time.time() - start_time < timeout:
            response_frame = self.receive_can_frame(0.1)
            
            if response_frame is None:
                continue
            
            # 检查是否为正响应
            if self.is_positive_response(response_frame, request_id):
                ecu_info = {
                    'request_id': request_id,
                    'response_id': response_frame.can_id,
                    'request_frame': request_frame,
                    'response_frame': response_frame
                }
                return ecu_info
            
            # 检查是否为负响应
            elif self.is_negative_response(response_frame):
                self.log_message(f"  -> 负响应: NRC=0x{response_frame.data[3]:02X}", False)
                return None
        
        return None
    
    def scan_range(self, start_id=0x700, end_id=0x7FF):
        """扫描CAN ID范围"""
        print(f"\n开始扫描CAN ID范围: 0x{start_id:03X} - 0x{end_id:03X}")
        print("=" * 50)
        
        total_ids = end_id - start_id + 1
        scanned = 0
        
        for can_id in range(start_id, end_id + 1):
            if not self.running:
                break
            
            scanned += 1
            progress = (scanned / total_ids) * 100
            
            print(f"\r扫描进度: {progress:5.1f}% (0x{can_id:03X})", end='', flush=True)
            
            ecu_info = self.scan_can_id(can_id)
            
            if ecu_info:
                print()  # 换行
                self.discovered_ecus.append(ecu_info)
                
                msg = f"✅ 发现ECU: 请求ID=0x{ecu_info['request_id']:03X}, " \
                      f"响应ID=0x{ecu_info['response_id']:03X}"
                self.log_message(msg)
                
                # 记录详细通信
                self.log_message(f"  请求: {ecu_info['request_frame']}", False)
                self.log_message(f"  响应: {ecu_info['response_frame']}", False)
                self.log_message("", False)  # 空行
            
            # 短暂延时避免总线拥塞
            time.sleep(0.01)
        
        print()  # 最终换行
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 50)
        print("扫描结果摘要 (55填充版本)")
        print("=" * 50)
        
        if not self.discovered_ecus:
            print("未发现任何响应UDS 10 01的ECU")
            return
        
        print(f"发现 {len(self.discovered_ecus)} 个ECU:")
        print()
        
        for i, ecu in enumerate(self.discovered_ecus, 1):
            print(f"{i}. ECU #{i}")
            print(f"   请求CAN ID: 0x{ecu['request_id']:03X}")
            print(f"   响应CAN ID: 0x{ecu['response_id']:03X}")
            print(f"   请求报文:   {ecu['request_frame']}")
            print(f"   响应报文:   {ecu['response_frame']}")
            print()
        
        # 记录到日志文件
        self.log_message("\n扫描结果摘要:", False)
        self.log_message("=" * 20, False)
        self.log_message(f"发现 {len(self.discovered_ecus)} 个ECU", False)
        
        for i, ecu in enumerate(self.discovered_ecus, 1):
            self.log_message(f"ECU #{i}: 0x{ecu['request_id']:03X} -> 0x{ecu['response_id']:03X}", False)
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行扫描"""
        print("UDS CAN ID扫描工具 (55填充版本)")
        print("=" * 35)
        print(f"接口: {self.interface}")
        print(f"扫描范围: 0x700 - 0x7FF")
        print(f"UDS服务: 10 01 (诊断会话控制)")
        print(f"数据格式: 02 10 01 55 55 55 55 55 (8字节，0x55填充)")
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行扫描
            self.scan_range()
            
            # 打印结果
            self.print_summary()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False
        
        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False
        
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        interface = sys.argv[1]
    else:
        interface = 'can0'
    
    scanner = UDSScanner(interface)
    
    try:
        success = scanner.run()
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
