UDS刷写日志 (ISOTP) - 2025-06-27 14:04:38.233413
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[14:04:38.233] 开始UDS刷写序列...
[14:04:38.233] === 步骤1: 诊断会话控制 (按报文序列) ===
[14:04:38.233] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[14:04:38.233] 发送UDS: 10 03
[14:04:38.335] 接收UDS: 50 03 00 32 01 F4
[14:04:38.335] ✅ UDS 10 03 成功
[14:04:38.435] 2. 发送UDS 10 02 (编程会话) - 0x715...
[14:04:38.435] 发送UDS: 10 02
[14:04:38.537] 接收UDS: 50 02 00 32 01 F4
[14:04:38.537] ✅ UDS 10 02 成功
[14:04:38.637] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:04:38.638] 发送广播UDS (0x7DF): 10 03
[14:04:38.639] 接收广播响应: 7F 10 22
[14:04:38.669] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[14:04:38.670] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[14:04:38.670] 发送广播UDS (0x7DF): 10 01
[14:04:38.671] 接收广播响应: 50 01 00 32 01 F4
[14:04:38.701] ✅ 广播UDS 10 01 成功
[14:04:38.802] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:04:38.802] 发送广播UDS (0x7DF): 10 03
[14:04:38.803] 接收广播响应: 50 03 00 32 01 F4
[14:04:38.837] ✅ 广播UDS 10 03 (重试) 成功
[14:04:39.838] 4a. 再次确认编程会话状态...
[14:04:39.839] 发送UDS: 10 02
[14:04:39.839] 清空缓冲区数据: 7F 10 22
[14:04:39.839] 清空缓冲区数据: 50 01 00 32 01 F4
[14:04:39.839] 清空缓冲区数据: 50 03 00 32 01 F4
[14:04:39.940] 接收UDS: 50 02 00 32 01 F4
[14:04:39.940] ✅ 编程会话状态确认成功
[14:04:40.441] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[14:04:40.441] 发送UDS: 31 01 02 03
[14:04:40.542] 接收UDS: 71 01 02 03 00
[14:04:40.542] UDS 31响应: 71 01 02 03 00 (长度: 5)
[14:04:40.542] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[14:04:40.542] ✅ UDS 31 01 02 03 成功
[14:04:40.643] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[14:04:40.643] 发送广播UDS (0x7DF): 85 02
[14:04:40.644] 接收广播响应: C5 02
[14:04:40.669] ✅ UDS 85 02 成功
[14:04:40.770] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[14:04:40.770] 发送广播UDS (0x7DF): 28 03 01
[14:04:40.771] 接收广播响应: 68 03
[14:04:40.809] ✅ UDS 28 03 01 成功
[14:04:40.910] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[14:04:40.910] 发送UDS: 10 02
[14:04:40.910] 清空缓冲区数据: C5 02
[14:04:40.910] 清空缓冲区数据: 68 03
[14:04:41.012] 接收UDS: 50 02 00 32 01 F4
[14:04:41.012] ✅ UDS 10 02 (再次) 成功
[14:04:41.513] === 步骤2: 安全访问 ===
[14:04:41.513] 发送UDS 27 03 (请求种子)...
[14:04:41.513] 发送UDS: 27 03
[14:04:41.616] 接收UDS: 67 03 EE F0 01 B2 CF B3 CB E3 85 4C 30 DF 84 B8 D9 3C
[14:04:41.616] 收到种子: EE F0 01 B2 CF B3 CB E3 85 4C 30 DF 84 B8 D9 3C
[14:04:41.616] 计算密钥，种子长度: 16字节, 种子: EEF001B2CFB3CBE3854C30DF84B8D93C
[14:04:41.618] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: EE F0 01 B2 CF B3 CB E3 85 4C 30 DF 84 B8 D9 3C 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 15 04 82 1A 0F 7F 65 96 E6 BA C6 F9 7A DD 90 88 

格式化输出:
===========
连续格式: 1504821A0F7F6596E6BAC6F97ADD9088
C数组格式: {0x15, 0x04, 0x82, 0x1A, 0x0F, 0x7F, 0x65, 0x96, 0xE6, 0xBA, 0xC6, 0xF9, 0x7A, 0xDD, 0x90, 0x88}
[14:04:41.618] 计算得到密钥: 15 04 82 1A 0F 7F 65 96 E6 BA C6 F9 7A DD 90 88
[14:04:41.618] 发送UDS 27 04 (发送密钥)...
[14:04:41.619] 发送UDS: 27 04 15 04 82 1A 0F 7F 65 96 E6 BA C6 F9 7A DD 90 88
[14:04:41.722] 接收UDS: 67 04
[14:04:41.722] ✅ 安全访问成功
[14:04:42.223] === 步骤3: 写入指纹 ===
[14:04:42.223] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[14:04:42.325] 接收UDS: 6E F1 84
[14:04:42.325] ✅ 写入指纹成功
[14:04:42.826] === 步骤4: 例程控制 ===
[14:04:42.826] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[14:04:42.928] 接收UDS: 71 01 FF 00 00
[14:04:42.928] ✅ 例程控制成功
[14:04:43.429] 
==================== 开始刷写 Block 1 ====================
[14:04:43.429] === 步骤5: 请求下载 Block 1 ===
[14:04:43.432] 发送UDS: 34 00 44 00 40 00 20 00 00 44 00
[14:04:43.535] 接收UDS: 7F 34 31
[14:04:43.535] ❌ 请求下载失败
[14:04:43.535] ❌ Block 1 刷写失败
