UDS刷写日志 (ISOTP) - 2025-06-27 16:24:05.111209
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[16:24:05.111] 开始UDS刷写序列...
[16:24:05.111] === 步骤1: 诊断会话控制 (按报文序列) ===
[16:24:05.111] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[16:24:05.111] 发送UDS: 10 03
[16:24:05.213] 接收UDS: 50 03 00 32 01 F4
[16:24:05.213] ✅ UDS 10 03 成功
[16:24:05.313] 2. 发送UDS 10 02 (编程会话) - 0x715...
[16:24:05.313] 发送UDS: 10 02
[16:24:05.415] 接收UDS: 50 02 00 32 01 F4
[16:24:05.415] ✅ UDS 10 02 成功
[16:24:05.515] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:24:05.515] 发送广播UDS (0x7DF): 10 03
[16:24:05.517] 接收广播响应: 7F 10 22
[16:24:05.561] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[16:24:05.562] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[16:24:05.562] 发送广播UDS (0x7DF): 10 01
[16:24:05.563] 接收广播响应: 50 01 00 32 01 F4
[16:24:05.597] ✅ 广播UDS 10 01 成功
[16:24:05.698] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:24:05.698] 发送广播UDS (0x7DF): 10 03
[16:24:05.700] 接收广播响应: 50 03 00 32 01 F4
[16:24:05.741] ✅ 广播UDS 10 03 (重试) 成功
[16:24:06.743] 4a. 再次确认编程会话状态...
[16:24:06.743] 发送UDS: 10 02
[16:24:06.743] 清空缓冲区数据: 7F 10 22
[16:24:06.743] 清空缓冲区数据: 50 01 00 32 01 F4
[16:24:06.743] 清空缓冲区数据: 50 03 00 32 01 F4
[16:24:06.844] 接收UDS: 50 02 00 32 01 F4
[16:24:06.844] ✅ 编程会话状态确认成功
[16:24:07.345] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[16:24:07.345] 发送UDS: 31 01 02 03
[16:24:07.446] 接收UDS: 71 01 02 03 00
[16:24:07.446] UDS 31响应: 71 01 02 03 00 (长度: 5)
[16:24:07.446] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[16:24:07.446] ✅ UDS 31 01 02 03 成功
[16:24:07.547] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[16:24:07.547] 发送广播UDS (0x7DF): 85 02
[16:24:07.548] 接收广播响应: C5 02
[16:24:07.581] ✅ UDS 85 02 成功
[16:24:07.682] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[16:24:07.682] 发送广播UDS (0x7DF): 28 03 01
[16:24:07.683] 接收广播响应: 68 03
[16:24:07.725] ✅ UDS 28 03 01 成功
[16:24:07.826] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[16:24:07.826] 发送UDS: 10 02
[16:24:07.826] 清空缓冲区数据: C5 02
[16:24:07.826] 清空缓冲区数据: 68 03
[16:24:07.928] 接收UDS: 50 02 00 32 01 F4
[16:24:07.928] ✅ UDS 10 02 (再次) 成功
[16:24:08.428] === 步骤2: 安全访问 ===
[16:24:08.428] 发送UDS 27 03 (请求种子)...
[16:24:08.428] 发送UDS: 27 03
[16:24:08.532] 接收UDS: 67 03 EB 9E D0 1F C7 F7 E3 17 45 67 C5 A1 7A 21 72 F0
[16:24:08.532] 收到种子: EB 9E D0 1F C7 F7 E3 17 45 67 C5 A1 7A 21 72 F0
[16:24:08.532] 计算密钥，种子长度: 16字节, 种子: EB9ED01FC7F7E3174567C5A17A2172F0
[16:24:08.534] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: EB 9E D0 1F C7 F7 E3 17 45 67 C5 A1 7A 21 72 F0 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: D1 73 64 D3 FC 66 53 7F 17 20 FA 98 1B CB 4A C9 

格式化输出:
===========
连续格式: D17364D3FC66537F1720FA981BCB4AC9
C数组格式: {0xD1, 0x73, 0x64, 0xD3, 0xFC, 0x66, 0x53, 0x7F, 0x17, 0x20, 0xFA, 0x98, 0x1B, 0xCB, 0x4A, 0xC9}
[16:24:08.534] 计算得到密钥: D1 73 64 D3 FC 66 53 7F 17 20 FA 98 1B CB 4A C9
[16:24:08.534] 发送UDS 27 04 (发送密钥)...
[16:24:08.534] 发送UDS: 27 04 D1 73 64 D3 FC 66 53 7F 17 20 FA 98 1B CB 4A C9
[16:24:08.638] 接收UDS: 67 04
[16:24:08.638] ✅ 安全访问成功
[16:24:09.138] === 步骤3: 写入指纹 ===
[16:24:09.139] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[16:24:09.241] 接收UDS: 6E F1 84
[16:24:09.241] ✅ 写入指纹成功
[16:24:09.741] === 步骤4: 例程控制 ===
[16:24:09.742] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:24:09.844] 接收UDS: 71 01 FF 00 00
[16:24:09.844] ✅ 例程控制成功
[16:24:10.345] 开始刷写 3 个blocks...
[16:24:10.345] 
==================== 开始刷写 Block 1 ====================
[16:24:10.345] 文件: 00004400_00005200.bin
[16:24:10.345] 地址: 0x00004400 - 0x000095FF
[16:24:10.345] 大小: 20992 字节
[16:24:10.345] === 步骤4a: 内存擦写 Block 1 ===
[16:24:10.345] 擦写地址: 0x00004400, 长度: 20992 字节
[16:24:10.345] 发送UDS: 31 01 FF 00 44 00 00 00 44 00 00 00 52 00
[16:24:10.448] 接收UDS: 7F 31 13
[16:24:10.448] ❌ 内存擦写失败, NRC=0x13
[16:24:10.448] ❌ Block 1 刷写失败
