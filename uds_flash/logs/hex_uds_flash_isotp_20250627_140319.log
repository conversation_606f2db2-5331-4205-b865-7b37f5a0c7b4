UDS刷写日志 (ISOTP) - 2025-06-27 14:03:19.027047
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[14:03:19.027] 开始UDS刷写序列...
[14:03:19.027] === 步骤1: 诊断会话控制 (按报文序列) ===
[14:03:19.027] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[14:03:19.027] 发送UDS: 10 03
[14:03:19.128] 接收UDS: 50 03 00 32 01 F4
[14:03:19.128] ✅ UDS 10 03 成功
[14:03:19.228] 2. 发送UDS 10 02 (编程会话) - 0x715...
[14:03:19.228] 发送UDS: 10 02
[14:03:19.330] 接收UDS: 50 02 00 32 01 F4
[14:03:19.330] ✅ UDS 10 02 成功
[14:03:19.430] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:03:19.430] 发送广播UDS (0x7DF): 10 03
[14:03:19.432] 接收广播响应: 7F 10 22
[14:03:19.477] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[14:03:19.477] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[14:03:19.478] 发送广播UDS (0x7DF): 10 01
[14:03:19.479] 接收广播响应: 50 01 00 32 01 F4
[14:03:19.517] ✅ 广播UDS 10 01 成功
[14:03:19.618] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:03:19.618] 发送广播UDS (0x7DF): 10 03
[14:03:19.619] 接收广播响应: 50 03 00 32 01 F4
[14:03:19.661] ✅ 广播UDS 10 03 (重试) 成功
[14:03:20.663] 4a. 再次确认编程会话状态...
[14:03:20.663] 发送UDS: 10 02
[14:03:20.663] 清空缓冲区数据: 7F 10 22
[14:03:20.663] 清空缓冲区数据: 50 01 00 32 01 F4
[14:03:20.663] 清空缓冲区数据: 50 03 00 32 01 F4
[14:03:20.764] 接收UDS: 50 02 00 32 01 F4
[14:03:20.764] ✅ 编程会话状态确认成功
[14:03:21.265] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[14:03:21.265] 发送UDS: 31 01 02 03
[14:03:21.366] 接收UDS: 71 01 02 03 00
[14:03:21.366] UDS 31响应: 71 01 02 03 00 (长度: 5)
[14:03:21.366] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[14:03:21.366] ✅ UDS 31 01 02 03 成功
[14:03:21.466] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[14:03:21.467] 发送广播UDS (0x7DF): 85 02
[14:03:21.468] 接收广播响应: C5 02
[14:03:21.517] ✅ UDS 85 02 成功
[14:03:21.618] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[14:03:21.618] 发送广播UDS (0x7DF): 28 03 01
[14:03:21.619] 接收广播响应: 68 03
[14:03:21.657] ✅ UDS 28 03 01 成功
[14:03:21.758] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[14:03:21.758] 发送UDS: 10 02
[14:03:21.758] 清空缓冲区数据: C5 02
[14:03:21.758] 清空缓冲区数据: 68 03
[14:03:21.859] 接收UDS: 50 02 00 32 01 F4
[14:03:21.859] ✅ UDS 10 02 (再次) 成功
[14:03:22.360] === 步骤2: 安全访问 ===
[14:03:22.360] 发送UDS 27 03 (请求种子)...
[14:03:22.360] 发送UDS: 27 03
[14:03:22.463] 接收UDS: 67 03 94 40 4C A5 1C AA 32 93 A9 3E ED 35 93 43 F4 1C
[14:03:22.463] 收到种子: 94 40 4C A5 1C AA 32 93 A9 3E ED 35 93 43 F4 1C
[14:03:22.464] 计算密钥，种子长度: 16字节, 种子: 94404CA51CAA3293A93EED359343F41C
[14:03:22.468] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 94 40 4C A5 1C AA 32 93 A9 3E ED 35 93 43 F4 1C 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 91 89 7D 5F E9 3C C7 BA 44 A3 CE B9 46 35 D3 6A 

格式化输出:
===========
连续格式: 91897D5FE93CC7BA44A3CEB94635D36A
C数组格式: {0x91, 0x89, 0x7D, 0x5F, 0xE9, 0x3C, 0xC7, 0xBA, 0x44, 0xA3, 0xCE, 0xB9, 0x46, 0x35, 0xD3, 0x6A}
[14:03:22.468] 计算得到密钥: 91 89 7D 5F E9 3C C7 BA 44 A3 CE B9 46 35 D3 6A
[14:03:22.468] 发送UDS 27 04 (发送密钥)...
[14:03:22.468] 发送UDS: 27 04 91 89 7D 5F E9 3C C7 BA 44 A3 CE B9 46 35 D3 6A
[14:03:22.571] 接收UDS: 67 04
[14:03:22.571] ✅ 安全访问成功
[14:03:23.072] === 步骤3: 写入指纹 ===
[14:03:23.072] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[14:03:23.175] 接收UDS: 6E F1 84
[14:03:23.175] ✅ 写入指纹成功
[14:03:23.675] === 步骤4: 例程控制 ===
[14:03:23.675] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[14:03:23.778] 接收UDS: 71 01 FF 00 00
[14:03:23.778] ✅ 例程控制成功
[14:03:24.278] 
==================== 开始刷写 Block 1 ====================
[14:03:24.278] === 步骤5: 请求下载 Block 1 ===
[14:03:24.278] 发送UDS: 34 00 44 00 40 00 20 00 00 44 00
[14:03:24.381] 接收UDS: 7F 34 31
[14:03:24.381] ❌ 请求下载失败
[14:03:24.381] ❌ Block 1 刷写失败
