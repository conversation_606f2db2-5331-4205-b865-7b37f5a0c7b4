UDS刷写日志 (ISOTP) - 2025-06-27 16:23:16.968809
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[16:23:16.968] 开始UDS刷写序列...
[16:23:16.968] === 步骤1: 诊断会话控制 (按报文序列) ===
[16:23:16.968] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[16:23:16.968] 发送UDS: 10 03
[16:23:17.070] 接收UDS: 50 03 00 32 01 F4
[16:23:17.070] ✅ UDS 10 03 成功
[16:23:17.170] 2. 发送UDS 10 02 (编程会话) - 0x715...
[16:23:17.170] 发送UDS: 10 02
[16:23:17.272] 接收UDS: 50 02 00 32 01 F4
[16:23:17.272] ✅ UDS 10 02 成功
[16:23:17.372] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:23:17.372] 发送广播UDS (0x7DF): 10 03
[16:23:17.374] 接收广播响应: 7F 10 22
[16:23:17.401] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[16:23:17.402] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[16:23:17.402] 发送广播UDS (0x7DF): 10 01
[16:23:17.403] 接收广播响应: 50 01 00 32 01 F4
[16:23:17.441] ✅ 广播UDS 10 01 成功
[16:23:17.542] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:23:17.542] 发送广播UDS (0x7DF): 10 03
[16:23:17.543] 接收广播响应: 50 03 00 32 01 F4
[16:23:17.577] ✅ 广播UDS 10 03 (重试) 成功
[16:23:18.579] 4a. 再次确认编程会话状态...
[16:23:18.579] 发送UDS: 10 02
[16:23:18.579] 清空缓冲区数据: 7F 10 22
[16:23:18.579] 清空缓冲区数据: 50 01 00 32 01 F4
[16:23:18.579] 清空缓冲区数据: 50 03 00 32 01 F4
[16:23:18.680] 接收UDS: 50 02 00 32 01 F4
[16:23:18.680] ✅ 编程会话状态确认成功
[16:23:19.181] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[16:23:19.181] 发送UDS: 31 01 02 03
[16:23:19.282] 接收UDS: 71 01 02 03 00
[16:23:19.282] UDS 31响应: 71 01 02 03 00 (长度: 5)
[16:23:19.282] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[16:23:19.282] ✅ UDS 31 01 02 03 成功
[16:23:19.383] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[16:23:19.383] 发送广播UDS (0x7DF): 85 02
[16:23:19.384] 接收广播响应: C5 02
[16:23:19.417] ✅ UDS 85 02 成功
[16:23:19.518] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[16:23:19.518] 发送广播UDS (0x7DF): 28 03 01
[16:23:19.519] 接收广播响应: 68 03
[16:23:19.545] ✅ UDS 28 03 01 成功
[16:23:19.646] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[16:23:19.646] 发送UDS: 10 02
[16:23:19.646] 清空缓冲区数据: C5 02
[16:23:19.646] 清空缓冲区数据: 68 03
[16:23:19.747] 接收UDS: 50 02 00 32 01 F4
[16:23:19.748] ✅ UDS 10 02 (再次) 成功
[16:23:20.248] === 步骤2: 安全访问 ===
[16:23:20.248] 发送UDS 27 03 (请求种子)...
[16:23:20.248] 发送UDS: 27 03
[16:23:20.352] 接收UDS: 67 03 B9 8D F4 05 9D 86 4D 02 75 AD 80 BE 80 E7 73 D2
[16:23:20.352] 收到种子: B9 8D F4 05 9D 86 4D 02 75 AD 80 BE 80 E7 73 D2
[16:23:20.352] 计算密钥，种子长度: 16字节, 种子: B98DF4059D864D0275AD80BE80E773D2
[16:23:20.354] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: B9 8D F4 05 9D 86 4D 02 75 AD 80 BE 80 E7 73 D2 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 35 33 07 F4 2B CF 8D DD 49 95 1D CF 0C B2 6C 4B 

格式化输出:
===========
连续格式: 353307F42BCF8DDD49951DCF0CB26C4B
C数组格式: {0x35, 0x33, 0x07, 0xF4, 0x2B, 0xCF, 0x8D, 0xDD, 0x49, 0x95, 0x1D, 0xCF, 0x0C, 0xB2, 0x6C, 0x4B}
[16:23:20.354] 计算得到密钥: 35 33 07 F4 2B CF 8D DD 49 95 1D CF 0C B2 6C 4B
[16:23:20.354] 发送UDS 27 04 (发送密钥)...
[16:23:20.354] 发送UDS: 27 04 35 33 07 F4 2B CF 8D DD 49 95 1D CF 0C B2 6C 4B
[16:23:20.458] 接收UDS: 67 04
[16:23:20.458] ✅ 安全访问成功
[16:23:20.958] === 步骤3: 写入指纹 ===
[16:23:20.958] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[16:23:21.061] 接收UDS: 6E F1 84
[16:23:21.061] ✅ 写入指纹成功
[16:23:21.561] === 步骤4: 例程控制 ===
[16:23:21.562] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:23:21.664] 接收UDS: 71 01 FF 00 00
[16:23:21.664] ✅ 例程控制成功
[16:23:22.164] 开始刷写 3 个blocks...
[16:23:22.165] 
==================== 开始刷写 Block 1 ====================
[16:23:22.165] 文件: 00004400_00005200.bin
[16:23:22.165] 地址: 0x00004400 - 0x000095FF
[16:23:22.165] 大小: 20992 字节
[16:23:22.165] === 步骤4a: 内存擦写 Block 1 ===
[16:23:22.165] 擦写地址: 0x00004400, 长度: 20992 字节
[16:23:22.165] 发送UDS: 31 01 FF 00 00 00 44 00 00 00 52 00
[16:23:22.267] 接收UDS: 7F 31 13
[16:23:22.267] ❌ 内存擦写失败, NRC=0x13
[16:23:22.267] ❌ Block 1 刷写失败
