UDS刷写日志 (ISOTP) - 2025-06-27 16:25:06.074198
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[16:25:06.074] 开始UDS刷写序列...
[16:25:06.074] === 步骤1: 诊断会话控制 (按报文序列) ===
[16:25:06.074] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[16:25:06.074] 发送UDS: 10 03
[16:25:06.175] 接收UDS: 50 03 00 32 01 F4
[16:25:06.175] ✅ UDS 10 03 成功
[16:25:06.275] 2. 发送UDS 10 02 (编程会话) - 0x715...
[16:25:06.275] 发送UDS: 10 02
[16:25:06.377] 接收UDS: 50 02 00 32 01 F4
[16:25:06.377] ✅ UDS 10 02 成功
[16:25:06.477] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:25:06.478] 发送广播UDS (0x7DF): 10 03
[16:25:06.479] 接收广播响应: 7F 10 22
[16:25:06.505] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[16:25:06.506] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[16:25:06.506] 发送广播UDS (0x7DF): 10 01
[16:25:06.507] 接收广播响应: 50 01 00 32 01 F4
[16:25:06.545] ✅ 广播UDS 10 01 成功
[16:25:06.646] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:25:06.646] 发送广播UDS (0x7DF): 10 03
[16:25:06.647] 接收广播响应: 50 03 00 32 01 F4
[16:25:06.693] ✅ 广播UDS 10 03 (重试) 成功
[16:25:07.695] 4a. 再次确认编程会话状态...
[16:25:07.695] 发送UDS: 10 02
[16:25:07.695] 清空缓冲区数据: 7F 10 22
[16:25:07.695] 清空缓冲区数据: 50 01 00 32 01 F4
[16:25:07.695] 清空缓冲区数据: 50 03 00 32 01 F4
[16:25:07.796] 接收UDS: 50 02 00 32 01 F4
[16:25:07.796] ✅ 编程会话状态确认成功
[16:25:08.296] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[16:25:08.296] 发送UDS: 31 01 02 03
[16:25:08.398] 接收UDS: 71 01 02 03 00
[16:25:08.398] UDS 31响应: 71 01 02 03 00 (长度: 5)
[16:25:08.398] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[16:25:08.398] ✅ UDS 31 01 02 03 成功
[16:25:08.498] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[16:25:08.498] 发送广播UDS (0x7DF): 85 02
[16:25:08.500] 接收广播响应: C5 02
[16:25:08.541] ✅ UDS 85 02 成功
[16:25:08.642] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[16:25:08.642] 发送广播UDS (0x7DF): 28 03 01
[16:25:08.643] 接收广播响应: 68 03
[16:25:08.677] ✅ UDS 28 03 01 成功
[16:25:08.778] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[16:25:08.778] 发送UDS: 10 02
[16:25:08.778] 清空缓冲区数据: C5 02
[16:25:08.778] 清空缓冲区数据: 68 03
[16:25:08.879] 接收UDS: 50 02 00 32 01 F4
[16:25:08.879] ✅ UDS 10 02 (再次) 成功
[16:25:09.380] === 步骤2: 安全访问 ===
[16:25:09.380] 发送UDS 27 03 (请求种子)...
[16:25:09.380] 发送UDS: 27 03
[16:25:09.483] 接收UDS: 67 03 F8 F7 C5 16 7F 4C D0 86 3B 1F FE 3A 09 3B A2 EA
[16:25:09.483] 收到种子: F8 F7 C5 16 7F 4C D0 86 3B 1F FE 3A 09 3B A2 EA
[16:25:09.483] 计算密钥，种子长度: 16字节, 种子: F8F7C5167F4CD0863B1FFE3A093BA2EA
[16:25:09.486] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: F8 F7 C5 16 7F 4C D0 86 3B 1F FE 3A 09 3B A2 EA 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 46 DE 76 09 80 A2 72 CE CB 3F 84 F1 8A AD EF B4 

格式化输出:
===========
连续格式: 46DE760980A272CECB3F84F18AADEFB4
C数组格式: {0x46, 0xDE, 0x76, 0x09, 0x80, 0xA2, 0x72, 0xCE, 0xCB, 0x3F, 0x84, 0xF1, 0x8A, 0xAD, 0xEF, 0xB4}
[16:25:09.486] 计算得到密钥: 46 DE 76 09 80 A2 72 CE CB 3F 84 F1 8A AD EF B4
[16:25:09.486] 发送UDS 27 04 (发送密钥)...
[16:25:09.486] 发送UDS: 27 04 46 DE 76 09 80 A2 72 CE CB 3F 84 F1 8A AD EF B4
[16:25:09.589] 接收UDS: 67 04
[16:25:09.589] ✅ 安全访问成功
[16:25:10.090] === 步骤3: 写入指纹 ===
[16:25:10.090] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[16:25:10.193] 接收UDS: 6E F1 84
[16:25:10.193] ✅ 写入指纹成功
[16:25:10.693] === 步骤4: 例程控制 ===
[16:25:10.693] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:25:10.795] 接收UDS: 71 01 FF 00 00
[16:25:10.795] ✅ 例程控制成功
[16:25:11.295] 开始刷写 3 个blocks...
[16:25:11.296] 
==================== 开始刷写 Block 1 ====================
[16:25:11.296] 文件: 00004400_00005200.bin
[16:25:11.296] 地址: 0x00004400 - 0x000095FF
[16:25:11.296] 大小: 20992 字节
[16:25:11.296] === 步骤4a: 内存擦写 Block 1 ===
[16:25:11.296] 擦写命令: 31 01 FF 00 44 00 00 00 44 00 00 00 52 00
[16:25:11.296] 擦写地址: 0x00004400, 长度: 20992 字节
[16:25:11.296] 发送UDS: 31 01 FF 00 44 00 00 00 44 00 00 00 52 00
[16:25:11.399] 接收UDS: 7F 31 13
[16:25:11.399] ❌ 内存擦写失败, NRC=0x13
[16:25:11.399] ❌ Block 1 刷写失败
