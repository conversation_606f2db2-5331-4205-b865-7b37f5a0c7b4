UDS刷写日志 (ISOTP) - 2025-06-27 13:51:38.277021
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[13:51:38.277] 开始UDS刷写序列...
[13:51:38.277] === 步骤1: 诊断会话控制 (按报文序列) ===
[13:51:38.277] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[13:51:38.277] 发送UDS: 10 03
[13:51:38.277] 接收UDS: 50 03 00 32 01 F4
[13:51:38.277] ✅ UDS 10 03 成功
[13:51:38.378] 2. 发送UDS 10 02 (编程会话) - 0x715...
[13:51:38.378] 发送UDS: 10 02
[13:51:38.378] 接收UDS: 50 02 00 32 01 F4
[13:51:38.379] ✅ UDS 10 02 成功
[13:51:38.479] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:51:38.479] 发送广播UDS (0x7DF): 10 03
[13:51:38.480] 接收广播响应: 7F 10 22
[13:51:38.523] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[13:51:38.523] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[13:51:38.523] 发送广播UDS (0x7DF): 10 01
[13:51:38.524] 接收广播响应: 50 01 00 32 01 F4
[13:51:38.555] ✅ 广播UDS 10 01 成功
[13:51:38.655] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:51:38.656] 发送广播UDS (0x7DF): 10 03
[13:51:38.657] 接收广播响应: 50 03 00 32 01 F4
[13:51:38.699] ✅ 广播UDS 10 03 (重试) 成功
[13:51:39.200] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[13:51:39.200] 发送UDS: 31 01 02 03
[13:51:39.200] 接收UDS: 7F 10 22
[13:51:39.200] UDS 31响应: 7F 10 22
[13:51:39.200] ❌ UDS 31 01 02 03 失败
