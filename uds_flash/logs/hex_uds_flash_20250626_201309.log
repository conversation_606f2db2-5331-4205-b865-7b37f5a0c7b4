UDS刷写日志 - 2025-06-26 20:13:09.632362
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[20:13:09.632] 开始UDS刷写序列...
[20:13:09.632] === 步骤1: 诊断会话控制 ===
[20:13:09.632] 发送UDS 10 01 (默认会话)...
[20:13:09.632] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:13:09.633] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:13:09.633] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:13:09.633] ✅ UDS 10 01 成功
[20:13:10.133] 发送UDS 10 03 (扩展会话)...
[20:13:10.134] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:13:10.135] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:13:10.135] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:13:10.135] ✅ UDS 10 03 成功
[20:13:10.636] === 步骤2: 安全访问 ===
[20:13:10.636] 发送UDS 27 03 (请求种子)...
[20:13:10.636] 发送: can0  715   [8]  02 27 03 55 55 55 55 55
[20:13:10.637] 接收到帧: can0  795   [8]  10 12 67 03 6A 5F 46 0A
[20:13:10.637] 接收目标响应: can0  795   [8]  10 12 67 03 6A 5F 46 0A
[20:13:10.637] 发送: can0  715   [8]  30 00 00 55 55 55 55 55
[20:13:10.638] 接收到帧: can0  795   [8]  21 D3 D3 10 E2 A6 F1 73
[20:13:10.638] 接收目标响应: can0  795   [8]  21 D3 D3 10 E2 A6 F1 73
[20:13:10.638] 收到种子: 03 6A 5F 46 0A D3 D3 10 E2 A6 F1 73
[20:13:10.638] 计算密钥，种子长度: 12字节, 种子: 036A5F460AD3D310E2A6F173
[20:13:10.638] 种子补零到16字节: 036A5F460AD3D310E2A6F17300000000
[20:13:10.640] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 03 6A 5F 46 0A D3 D3 10 E2 A6 F1 73 00 00 00 00 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 0C E5 50 0C 1D 72 A8 E3 FD FD DC 0D 1F F7 38 64 

格式化输出:
===========
连续格式: 0CE5500C1D72A8E3FDFDDC0D1FF73864
C数组格式: {0x0C, 0xE5, 0x50, 0x0C, 0x1D, 0x72, 0xA8, 0xE3, 0xFD, 0xFD, 0xDC, 0x0D, 0x1F, 0xF7, 0x38, 0x64}
[20:13:10.640] 计算得到密钥: 0C E5 50 0C 1D 72 A8 E3 FD FD DC 0D 1F F7 38 64
[20:13:10.640] 发送UDS 27 04 (发送密钥)...
[20:13:10.640] 发送: can0  715   [8]  10 12 27 04 0C E5 50 0C
[20:13:10.640] 接收到帧: can0  795   [8]  22 E8 86 AF 75 12 55 55
[20:13:10.640] 接收目标响应: can0  795   [8]  22 E8 86 AF 75 12 55 55
[20:13:10.641] 收到连续帧（遗漏首帧）: 22 E8 86 AF 75 12 55 55
[20:13:10.641] ❌ 安全访问失败
