UDS刷写日志 (ISOTP) - 2025-06-27 14:01:59.448503
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[14:01:59.448] 开始UDS刷写序列...
[14:01:59.448] === 步骤1: 诊断会话控制 (按报文序列) ===
[14:01:59.448] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[14:01:59.448] 发送UDS: 10 03
[14:01:59.449] 接收UDS: 50 03 00 32 01 F4
[14:01:59.449] ✅ UDS 10 03 成功
[14:01:57.984] 2. 发送UDS 10 02 (编程会话) - 0x715...
[14:01:57.984] 发送UDS: 10 02
[14:01:57.985] 接收UDS: 50 02 00 32 01 F4
[14:01:57.985] ✅ UDS 10 02 成功
[14:01:58.085] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:01:58.085] 发送广播UDS (0x7DF): 10 03
[14:01:58.087] 接收广播响应: 7F 10 22
[14:01:58.117] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[14:01:58.117] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[14:01:58.118] 发送广播UDS (0x7DF): 10 01
[14:01:58.119] 接收广播响应: 50 01 00 32 01 F4
[14:01:58.153] ✅ 广播UDS 10 01 成功
[14:01:58.254] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:01:58.254] 发送广播UDS (0x7DF): 10 03
[14:01:58.255] 接收广播响应: 50 03 00 32 01 F4
[14:01:58.285] ✅ 广播UDS 10 03 (重试) 成功
[14:01:58.386] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[14:01:58.386] 发送UDS: 31 01 02 03
[14:01:58.386] 接收UDS: 7F 10 22
[14:01:58.386] UDS 31响应: 7F 10 22 (长度: 3)
[14:01:58.386] 检查条件: response[0]=7F, 期望=0x71, 长度>=3
[14:01:58.386] ❌ UDS 31 01 02 03 失败 - 响应格式不匹配
