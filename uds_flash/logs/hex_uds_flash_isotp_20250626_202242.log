UDS刷写日志 (ISOTP) - 2025-06-26 20:22:42.008485
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[20:22:42.008] 开始UDS刷写序列...
[20:22:42.008] === 步骤1: 诊断会话控制 ===
[20:22:42.008] 发送UDS 10 01 (默认会话)...
[20:22:42.008] 发送UDS: 10 01
[20:22:42.009] 接收UDS: 50 01 00 32 01 F4
[20:22:42.009] ✅ UDS 10 01 成功
[20:22:42.509] 发送UDS 10 03 (扩展会话)...
[20:22:42.510] 发送UDS: 10 03
[20:22:42.511] 接收UDS: 50 03 00 32 01 F4
[20:22:42.511] ✅ UDS 10 03 成功
[20:22:43.012] === 步骤2: 安全访问 ===
[20:22:43.012] 发送UDS 27 03 (请求种子)...
[20:22:43.012] 发送UDS: 27 03
[20:22:43.015] 接收UDS: 67 03 D8 27 7A 11 4C 04 F8 4A B4 10 A9 6A A5 06 D4 DC
[20:22:43.015] 收到种子: D8 27 7A 11 4C 04 F8 4A B4 10 A9 6A A5 06 D4 DC
[20:22:43.015] 计算密钥，种子长度: 16字节, 种子: D8277A114C04F84AB410A96AA506D4DC
[20:22:43.018] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: D8 27 7A 11 4C 04 F8 4A B4 10 A9 6A A5 06 D4 DC 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 12 6C 0C 4D B5 9F 97 02 0E D8 E9 F9 2F 76 D9 42 

格式化输出:
===========
连续格式: 126C0C4DB59F97020ED8E9F92F76D942
C数组格式: {0x12, 0x6C, 0x0C, 0x4D, 0xB5, 0x9F, 0x97, 0x02, 0x0E, 0xD8, 0xE9, 0xF9, 0x2F, 0x76, 0xD9, 0x42}
[20:22:43.018] 计算得到密钥: 12 6C 0C 4D B5 9F 97 02 0E D8 E9 F9 2F 76 D9 42
[20:22:43.018] 发送UDS 27 04 (发送密钥)...
[20:22:43.018] 发送UDS: 27 04 12 6C 0C 4D B5 9F 97 02 0E D8 E9 F9 2F 76 D9 42
[20:22:43.021] 接收UDS: 67 04
[20:22:43.021] ✅ 安全访问成功
[20:22:43.522] === 步骤3: 写入指纹 ===
[20:22:43.522] 发送UDS: 0C 2E F1 84 00 01 02 03 04 05 06 07 08
[20:22:43.524] 接收UDS: 7F 0C 11
[20:22:43.524] ❌ 写入指纹失败
