UDS刷写日志 - 2025-06-26 20:14:08.589197
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[20:14:08.589] 开始UDS刷写序列...
[20:14:08.589] === 步骤1: 诊断会话控制 ===
[20:14:08.589] 发送UDS 10 01 (默认会话)...
[20:14:08.589] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:14:08.590] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:14:08.590] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:14:08.590] ✅ UDS 10 01 成功
[20:14:09.090] 发送UDS 10 03 (扩展会话)...
[20:14:09.090] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:14:09.092] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:14:09.092] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:14:09.092] ✅ UDS 10 03 成功
[20:14:09.592] === 步骤2: 安全访问 ===
[20:14:09.593] 发送UDS 27 03 (请求种子)...
[20:14:09.593] 发送: can0  715   [8]  02 27 03 55 55 55 55 55
[20:14:09.594] 接收到帧: can0  795   [8]  10 12 67 03 23 84 22 28
[20:14:09.594] 接收目标响应: can0  795   [8]  10 12 67 03 23 84 22 28
[20:14:09.594] 发送: can0  715   [8]  30 00 00 55 55 55 55 55
[20:14:09.595] 接收到帧: can0  795   [8]  21 41 4F A9 5B 13 7F 1E
[20:14:09.595] 接收目标响应: can0  795   [8]  21 41 4F A9 5B 13 7F 1E
[20:14:09.595] 收到种子: 03 23 84 22 28 41 4F A9 5B 13 7F 1E
[20:14:09.595] 计算密钥，种子长度: 12字节, 种子: 0323842228414FA95B137F1E
[20:14:09.595] 种子补零到16字节: 0323842228414FA95B137F1E00000000
[20:14:09.597] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 03 23 84 22 28 41 4F A9 5B 13 7F 1E 00 00 00 00 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 64 AF F5 42 21 A7 06 64 76 DA 51 60 9F 3A CF BC 

格式化输出:
===========
连续格式: 64AFF54221A7066476DA51609F3ACFBC
C数组格式: {0x64, 0xAF, 0xF5, 0x42, 0x21, 0xA7, 0x06, 0x64, 0x76, 0xDA, 0x51, 0x60, 0x9F, 0x3A, 0xCF, 0xBC}
[20:14:09.597] 计算得到密钥: 64 AF F5 42 21 A7 06 64 76 DA 51 60 9F 3A CF BC
[20:14:09.597] 发送UDS 27 04 (发送密钥)...
[20:14:09.597] 发送: can0  715   [8]  10 12 27 04 64 AF F5 42
[20:14:09.597] 接收到帧: can0  795   [8]  22 AD C3 43 8E 1B 55 55
[20:14:09.597] 接收目标响应: can0  795   [8]  22 AD C3 43 8E 1B 55 55
[20:14:09.598] 接收到帧: can0  795   [8]  30 08 00 55 55 55 55 55
[20:14:09.598] 接收目标响应: can0  795   [8]  30 08 00 55 55 55 55 55
[20:14:10.600] 等待响应ID 0x795 超时
[20:14:10.600] 响应1: 22 AD C3 43 8E 1B 55 55
[20:14:10.600] 响应2: 30 08 00 55 55 55 55 55
[20:14:10.600] 找到流控帧，使用流控帧逻辑
[20:14:10.600] 收到流控帧，发送连续帧...
[20:14:10.600] 发送: can0  715   [8]  21 21 A7 06 64 76 DA 51
[20:14:10.600] 发送: can0  715   [8]  22 60 9F 3A CF BC 55 55
[20:14:10.601] 接收到帧: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:14:10.602] 接收目标响应: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:14:10.602] 收到最终响应: 03 7F 27 35 55 55 55 55
[20:14:10.602] ❌ 安全访问失败
