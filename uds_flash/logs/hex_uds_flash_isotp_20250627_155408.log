UDS刷写日志 (ISOTP) - 2025-06-27 15:54:08.694679
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[15:54:08.694] 开始UDS刷写序列...
[15:54:08.694] === 步骤1: 诊断会话控制 (按报文序列) ===
[15:54:08.694] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[15:54:08.694] 发送UDS: 10 03
[15:54:08.796] 接收UDS: 50 03 00 32 01 F4
[15:54:08.796] ✅ UDS 10 03 成功
[15:54:08.896] 2. 发送UDS 10 02 (编程会话) - 0x715...
[15:54:08.896] 发送UDS: 10 02
[15:54:08.998] 接收UDS: 50 02 00 32 01 F4
[15:54:08.998] ✅ UDS 10 02 成功
[15:54:09.098] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[15:54:09.099] 发送广播UDS (0x7DF): 10 03
[15:54:09.100] 接收广播响应: 7F 10 22
[15:54:09.137] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[15:54:09.138] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[15:54:09.138] 发送广播UDS (0x7DF): 10 01
[15:54:09.139] 接收广播响应: 50 01 00 32 01 F4
[15:54:09.181] ✅ 广播UDS 10 01 成功
[15:54:09.282] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[15:54:09.282] 发送广播UDS (0x7DF): 10 03
[15:54:09.283] 接收广播响应: 50 03 00 32 01 F4
[15:54:09.317] ✅ 广播UDS 10 03 (重试) 成功
[15:54:10.319] 4a. 再次确认编程会话状态...
[15:54:10.319] 发送UDS: 10 02
[15:54:10.319] 清空缓冲区数据: 7F 10 22
[15:54:10.319] 清空缓冲区数据: 50 01 00 32 01 F4
[15:54:10.319] 清空缓冲区数据: 50 03 00 32 01 F4
[15:54:10.421] 接收UDS: 50 02 00 32 01 F4
[15:54:10.421] ✅ 编程会话状态确认成功
[15:54:10.921] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[15:54:10.921] 发送UDS: 31 01 02 03
[15:54:11.023] 接收UDS: 71 01 02 03 00
[15:54:11.023] UDS 31响应: 71 01 02 03 00 (长度: 5)
[15:54:11.023] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[15:54:11.023] ✅ UDS 31 01 02 03 成功
[15:54:11.123] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[15:54:11.123] 发送广播UDS (0x7DF): 85 02
[15:54:11.125] 接收广播响应: C5 02
[15:54:11.157] ✅ UDS 85 02 成功
[15:54:11.258] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[15:54:11.258] 发送广播UDS (0x7DF): 28 03 01
[15:54:11.259] 接收广播响应: 68 03
[15:54:11.325] ✅ UDS 28 03 01 成功
[15:54:11.426] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[15:54:11.426] 发送UDS: 10 02
[15:54:11.426] 清空缓冲区数据: C5 02
[15:54:11.426] 清空缓冲区数据: 68 03
[15:54:11.527] 接收UDS: 50 02 00 32 01 F4
[15:54:11.527] ✅ UDS 10 02 (再次) 成功
[15:54:12.028] === 步骤2: 安全访问 ===
[15:54:12.028] 发送UDS 27 03 (请求种子)...
[15:54:12.028] 发送UDS: 27 03
[15:54:12.131] 接收UDS: 67 03 3C 9F 00 E1 3F 43 8C 9F 9A 49 9A F9 DC 30 A5 47
[15:54:12.131] 收到种子: 3C 9F 00 E1 3F 43 8C 9F 9A 49 9A F9 DC 30 A5 47
[15:54:12.131] 计算密钥，种子长度: 16字节, 种子: 3C9F00E13F438C9F9A499AF9DC30A547
[15:54:12.134] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 3C 9F 00 E1 3F 43 8C 9F 9A 49 9A F9 DC 30 A5 47 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 0C 7C 92 C9 ED 52 69 F6 AA CB 3B 38 33 32 EA 91 

格式化输出:
===========
连续格式: 0C7C92C9ED5269F6AACB3B383332EA91
C数组格式: {0x0C, 0x7C, 0x92, 0xC9, 0xED, 0x52, 0x69, 0xF6, 0xAA, 0xCB, 0x3B, 0x38, 0x33, 0x32, 0xEA, 0x91}
[15:54:12.134] 计算得到密钥: 0C 7C 92 C9 ED 52 69 F6 AA CB 3B 38 33 32 EA 91
[15:54:12.134] 发送UDS 27 04 (发送密钥)...
[15:54:12.134] 发送UDS: 27 04 0C 7C 92 C9 ED 52 69 F6 AA CB 3B 38 33 32 EA 91
[15:54:12.237] 接收UDS: 67 04
[15:54:12.237] ✅ 安全访问成功
[15:54:12.737] === 步骤3: 写入指纹 ===
[15:54:12.738] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[15:54:12.840] 接收UDS: 6E F1 84
[15:54:12.840] ✅ 写入指纹成功
[15:54:13.341] === 步骤4: 例程控制 ===
[15:54:13.341] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[15:54:13.444] 接收UDS: 71 01 FF 00 00
[15:54:13.444] ✅ 例程控制成功
[15:54:13.944] 开始刷写 3 个blocks...
[15:54:13.944] 
==================== 开始刷写 Block 1 ====================
[15:54:13.944] 文件: 00004400_00005200.bin
[15:54:13.944] 地址: 0x00004400 - 0x000095FF
[15:54:13.944] 大小: 20992 字节
[15:54:13.945] === 步骤5: 请求下载 Block 1 ===
[15:54:13.945] 块信息: 00004400_00005200.bin
[15:54:13.945] 地址: 0x00004400 (0x00004400)
[15:54:13.945] 长度: 20992 字节 (0x00005200)
[15:54:13.945] 发送UDS: 34 00 44 00 00 44 00 00 00 52 00
[15:54:14.047] 接收UDS: 74 20 01 02
[15:54:14.047] ✅ 请求下载成功, 最大块长度: 8193
[15:54:14.147] === 步骤6: 传输数据 Block 1 ===
[15:54:14.147] 读取Block 1数据: 00004400_00005200.bin (20992 字节)
[15:54:14.147] 开始传输数据: 20992 字节, 分为 82 个数据包
[15:54:14.147] 发送UDS: 36 01 00 40 00 20 11 44 00 00 0D 6B 00 00 0D 6B 00 00 10 B5 03 4B 83 F3 08 88 02 F0 10 FF 02 F0 7E FB 00 40 00 20 10 B5 12 22 47 4B 1A 60 18 20 02 F0 59 FE 02 22 45 4B 1A 60 07 32 45 4B 1A 60 01 32 44 4B 1A 60 FA 20 C0 00 02 F0 C2 FE C0 B2 00 28 02 D0 01 20 02 F0 22 FB 3F 48 02 F0 AF FE 00 21 00 20 02 F0 29 FD 3D 4C 3D 4B 23 60 78 20 03 F0 FF FD 3C 4B 23 60 00 24 04 E0 F0 20 03 F0 F8 FD 01 34 E4 B2 18 2C 03 D8 37 4B 1B 68 00 2B F4 D0 35 4B 1B 68 00 2B 02 D1 03 20 02 F0 FF FA 01 22 32 4B 1A 60 29 4B 1B 68 00 2B 03 DA 30 4B 1B 68 DB 07 18 D5 2F 4A 13 68 80 21 09 06 0B 43 13 60 00 23 09 E0 2B 4A 11 68 02 20 01 43 11 60 11 68 81 43 11 60 01 33 DB B2 04 2B F3 D9 25 4A 13 68 5B 00 5B 08 13 60 00 24 23 4B 1C 60 23 4B 24 4A 1A 60 C0 21 89 00 23 4A 11 60 23 4A 1A 60 F0 21
[15:54:14.267] 接收UDS: 7F 36 78
[15:54:14.267] ❌ 传输数据块 1 失败, NRC=0x78
[15:54:14.267] ❌ Block 1 刷写失败
