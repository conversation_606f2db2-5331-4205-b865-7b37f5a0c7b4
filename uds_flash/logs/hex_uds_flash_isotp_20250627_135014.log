UDS刷写日志 (ISOTP) - 2025-06-27 13:50:14.217065
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[13:50:14.217] 开始UDS刷写序列...
[13:50:14.217] === 步骤1: 诊断会话控制 (按报文序列) ===
[13:50:14.217] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[13:50:14.217] 发送UDS: 10 03
[13:50:14.218] 接收UDS: 50 03 00 32 01 F4
[13:50:14.218] ✅ UDS 10 03 成功
[13:50:14.318] 2. 发送UDS 10 02 (编程会话) - 0x715...
[13:50:14.318] 发送UDS: 10 02
[13:50:14.319] 接收UDS: 50 02 00 32 01 F4
[13:50:14.319] ✅ UDS 10 02 成功
[13:50:14.419] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:50:14.419] 发送广播UDS (0x7DF): 10 03
[13:50:14.421] 接收广播响应: 7F 10 22
[13:50:14.451] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[13:50:14.451] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[13:50:14.451] 发送广播UDS (0x7DF): 10 01
[13:50:14.453] 接收广播响应: 50 01 00 32 01 F4
[13:50:14.479] ✅ 广播UDS 10 01 成功
[13:50:14.579] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:50:14.579] 发送广播UDS (0x7DF): 10 03
[13:50:14.581] 接收广播响应: 50 03 00 32 01 F4
[13:50:14.619] ✅ 广播UDS 10 03 (重试) 成功
[13:50:14.719] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[13:50:14.719] 发送UDS: 31 01 02 03
[13:50:14.719] 接收UDS: 7F 10 22
[13:50:14.719] ❌ UDS 31 01 02 03 失败
