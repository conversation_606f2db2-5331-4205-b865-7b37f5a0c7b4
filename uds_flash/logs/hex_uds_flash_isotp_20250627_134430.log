UDS刷写日志 (ISOTP) - 2025-06-27 13:44:30.305503
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[13:44:30.305] 开始UDS刷写序列...
[13:44:30.305] === 步骤1: 诊断会话控制 (按报文序列) ===
[13:44:30.305] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[13:44:30.305] 发送UDS: 10 03
[13:44:30.307] 接收UDS: 50 03 00 32 01 F4
[13:44:30.307] ✅ UDS 10 03 成功
[13:44:30.407] 2. 发送UDS 10 02 (编程会话) - 0x715...
[13:44:30.407] 发送UDS: 10 02
[13:44:30.408] 接收UDS: 50 02 00 32 01 F4
[13:44:30.408] ✅ UDS 10 02 成功
[13:44:30.508] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:44:30.508] 发送广播UDS (0x7DF): 10 03
[13:44:30.510] 接收广播响应: 7F 10 22
[13:44:30.535] ❌ 广播UDS 10 03 失败
