UDS刷写日志 (ISOTP) - 2025-06-27 13:54:28.532267
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[13:54:28.532] 开始UDS刷写序列...
[13:54:28.532] === 步骤1: 诊断会话控制 (按报文序列) ===
[13:54:28.532] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[13:54:28.532] 发送UDS: 10 03
[13:54:28.533] 接收UDS: 50 03 00 32 01 F4
[13:54:28.533] ✅ UDS 10 03 成功
[13:54:28.634] 2. 发送UDS 10 02 (编程会话) - 0x715...
[13:54:28.634] 发送UDS: 10 02
[13:54:28.634] 接收UDS: 50 02 00 32 01 F4
[13:54:28.634] ✅ UDS 10 02 成功
[13:54:28.735] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:54:28.735] 发送广播UDS (0x7DF): 10 03
[13:54:28.736] 接收广播响应: 7F 10 22
[13:54:28.795] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[13:54:28.795] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[13:54:28.795] 发送广播UDS (0x7DF): 10 01
[13:54:28.797] 接收广播响应: 50 01 00 32 01 F4
[13:54:28.839] ✅ 广播UDS 10 01 成功
[13:54:28.939] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[13:54:28.939] 发送广播UDS (0x7DF): 10 03
[13:54:28.941] 接收广播响应: 50 03 00 32 01 F4
[13:54:28.967] ✅ 广播UDS 10 03 (重试) 成功
[13:54:29.067] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[13:54:29.067] 发送UDS: 31 01 02 03
[13:54:29.067] 接收UDS: 7F 10 22
[13:54:29.067] ❌ UDS 31 01 02 03 失败
