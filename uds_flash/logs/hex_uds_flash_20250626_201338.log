UDS刷写日志 - 2025-06-26 20:13:38.744432
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[20:13:38.744] 开始UDS刷写序列...
[20:13:38.744] === 步骤1: 诊断会话控制 ===
[20:13:38.744] 发送UDS 10 01 (默认会话)...
[20:13:38.744] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:13:38.745] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:13:38.745] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:13:38.745] ✅ UDS 10 01 成功
[20:13:39.245] 发送UDS 10 03 (扩展会话)...
[20:13:39.245] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:13:39.247] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:13:39.247] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:13:39.247] ✅ UDS 10 03 成功
[20:13:39.748] === 步骤2: 安全访问 ===
[20:13:39.748] 发送UDS 27 03 (请求种子)...
[20:13:39.748] 发送: can0  715   [8]  02 27 03 55 55 55 55 55
[20:13:39.749] 接收到帧: can0  795   [8]  10 12 67 03 E5 23 B4 C3
[20:13:39.749] 接收目标响应: can0  795   [8]  10 12 67 03 E5 23 B4 C3
[20:13:39.749] 发送: can0  715   [8]  30 00 00 55 55 55 55 55
[20:13:39.750] 接收到帧: can0  795   [8]  21 3C 5C F7 BC 1A 88 A5
[20:13:39.750] 接收目标响应: can0  795   [8]  21 3C 5C F7 BC 1A 88 A5
[20:13:39.750] 收到种子: 03 E5 23 B4 C3 3C 5C F7 BC 1A 88 A5
[20:13:39.750] 计算密钥，种子长度: 12字节, 种子: 03E523B4C33C5CF7BC1A88A5
[20:13:39.750] 种子补零到16字节: 03E523B4C33C5CF7BC1A88A500000000
[20:13:39.752] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 03 E5 23 B4 C3 3C 5C F7 BC 1A 88 A5 00 00 00 00 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 87 B0 31 CD 9A 57 BC F5 9C 2E 1C FE CC 7F A8 1F 

格式化输出:
===========
连续格式: 87B031CD9A57BCF59C2E1CFECC7FA81F
C数组格式: {0x87, 0xB0, 0x31, 0xCD, 0x9A, 0x57, 0xBC, 0xF5, 0x9C, 0x2E, 0x1C, 0xFE, 0xCC, 0x7F, 0xA8, 0x1F}
[20:13:39.752] 计算得到密钥: 87 B0 31 CD 9A 57 BC F5 9C 2E 1C FE CC 7F A8 1F
[20:13:39.752] 发送UDS 27 04 (发送密钥)...
[20:13:39.752] 发送: can0  715   [8]  10 12 27 04 87 B0 31 CD
[20:13:39.752] 接收到帧: can0  795   [8]  22 73 A1 C3 36 0C 55 55
[20:13:39.752] 接收目标响应: can0  795   [8]  22 73 A1 C3 36 0C 55 55
[20:13:39.753] 接收到帧: can0  795   [8]  30 08 00 55 55 55 55 55
[20:13:39.753] 接收目标响应: can0  795   [8]  30 08 00 55 55 55 55 55
[20:13:40.755] 等待响应ID 0x795 超时
[20:13:40.755] 响应1: 22 73 A1 C3 36 0C 55 55
[20:13:40.755] 响应2: 30 08 00 55 55 55 55 55
[20:13:40.755] 收到连续帧（遗漏首帧）: 22 73 A1 C3 36 0C 55 55
[20:13:40.755] ❌ 安全访问失败
