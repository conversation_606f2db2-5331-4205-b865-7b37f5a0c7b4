UDS刷写日志 (ISOTP) - 2025-06-27 14:02:33.222633
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[14:02:33.222] 开始UDS刷写序列...
[14:02:33.222] === 步骤1: 诊断会话控制 (按报文序列) ===
[14:02:33.222] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[14:02:33.222] 发送UDS: 10 03
[14:02:33.223] 接收UDS: 50 03 00 32 01 F4
[14:02:33.223] ✅ UDS 10 03 成功
[14:02:33.323] 2. 发送UDS 10 02 (编程会话) - 0x715...
[14:02:33.323] 发送UDS: 10 02
[14:02:33.325] 接收UDS: 50 02 00 32 01 F4
[14:02:33.325] ✅ UDS 10 02 成功
[14:02:33.425] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:02:33.425] 发送广播UDS (0x7DF): 10 03
[14:02:33.427] 接收广播响应: 7F 10 22
[14:02:33.461] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[14:02:33.461] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[14:02:33.462] 发送广播UDS (0x7DF): 10 01
[14:02:33.463] 接收广播响应: 50 01 00 32 01 F4
[14:02:33.505] ✅ 广播UDS 10 01 成功
[14:02:33.606] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[14:02:33.606] 发送广播UDS (0x7DF): 10 03
[14:02:33.607] 接收广播响应: 50 03 00 32 01 F4
[14:02:33.633] ✅ 广播UDS 10 03 (重试) 成功
[14:02:34.634] 4a. 再次确认编程会话状态...
[14:02:34.634] 发送UDS: 10 02
[14:02:34.637] 接收UDS: 7F 10 22
[14:02:34.637] ⚠️ 编程会话状态确认失败
[14:02:35.138] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[14:02:35.138] 发送UDS: 31 01 02 03
[14:02:35.138] 接收UDS: 50 01 00 32 01 F4
[14:02:35.138] UDS 31响应: 50 01 00 32 01 F4 (长度: 6)
[14:02:35.138] 检查条件: response[0]=50, 期望=0x71, 长度>=6
[14:02:35.138] ❌ UDS 31 01 02 03 失败 - 响应格式不匹配
