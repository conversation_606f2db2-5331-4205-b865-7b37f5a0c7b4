UDS刷写日志 - 2025-06-26 20:11:51.162299
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[20:11:51.162] 开始UDS刷写序列...
[20:11:51.162] === 步骤1: 诊断会话控制 ===
[20:11:51.162] 发送UDS 10 01 (默认会话)...
[20:11:51.162] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:11:51.163] 接收到帧: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:11:51.163] 接收目标响应: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:11:51.163] ✅ UDS 10 01 成功
[20:11:51.663] 发送UDS 10 03 (扩展会话)...
[20:11:51.664] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:11:51.665] 接收到帧: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:11:51.665] 接收目标响应: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:11:51.665] ✅ UDS 10 03 成功
[20:11:52.166] === 步骤2: 安全访问 ===
[20:11:52.166] 发送UDS 27 03 (请求种子)...
[20:11:52.166] 发送: can0  715   [8]  02 27 03 55 55 55 55 55
[20:11:52.167] 接收到帧: can0  795   [8]  10 12 67 03 2A 27 5D 4D
[20:11:52.167] 接收目标响应: can0  795   [8]  10 12 67 03 2A 27 5D 4D
[20:11:52.167] 发送: can0  715   [8]  30 00 00 55 55 55 55 55
[20:11:52.168] 接收到帧: can0  795   [8]  21 1C 83 F0 1F 2B F5 9A
[20:11:52.168] 接收目标响应: can0  795   [8]  21 1C 83 F0 1F 2B F5 9A
[20:11:52.168] 收到种子: 03 2A 27 5D 4D 1C 83 F0 1F 2B F5 9A
[20:11:52.168] 计算密钥，种子长度: 12字节, 种子: 032A275D4D1C83F01F2BF59A
[20:11:52.168] 种子补零到16字节: 032A275D4D1C83F01F2BF59A00000000
[20:11:52.170] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 03 2A 27 5D 4D 1C 83 F0 1F 2B F5 9A 00 00 00 00 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: D6 2B E4 1A 8A 7D BE DB B7 9C 05 C7 95 88 2F FC 

格式化输出:
===========
连续格式: D62BE41A8A7DBEDBB79C05C795882FFC
C数组格式: {0xD6, 0x2B, 0xE4, 0x1A, 0x8A, 0x7D, 0xBE, 0xDB, 0xB7, 0x9C, 0x05, 0xC7, 0x95, 0x88, 0x2F, 0xFC}
[20:11:52.170] 计算得到密钥: D6 2B E4 1A 8A 7D BE DB B7 9C 05 C7 95 88 2F FC
[20:11:52.171] 发送UDS 27 04 (发送密钥)...
[20:11:52.171] 发送: can0  715   [8]  10 12 27 04 D6 2B E4 1A
[20:11:52.171] 接收到帧: can0  795   [8]  22 60 4E 71 A8 0E 55 55
[20:11:52.171] 接收目标响应: can0  795   [8]  22 60 4E 71 A8 0E 55 55
[20:11:52.171] 收到连续帧: 22 60 4E 71 A8 0E 55 55
[20:11:52.171] ❌ 安全访问失败
