UDS刷写日志 (ISOTP) - 2025-06-27 16:35:09.153353
请求CAN ID: 0x715
响应CAN ID: 0x795
============================================================

[16:35:09.153] 开始UDS刷写序列...
[16:35:09.153] === 步骤1: 诊断会话控制 (按报文序列) ===
[16:35:09.153] 1. 发送UDS 10 03 (扩展会话) - 0x715...
[16:35:09.153] 发送UDS: 10 03
[16:35:09.254] 接收UDS: 50 03 00 32 01 F4
[16:35:09.254] ✅ UDS 10 03 成功
[16:35:09.354] 2. 发送UDS 10 02 (编程会话) - 0x715...
[16:35:09.354] 发送UDS: 10 02
[16:35:09.456] 接收UDS: 50 02 00 32 01 F4
[16:35:09.456] ✅ UDS 10 02 成功
[16:35:09.556] 3. 发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:35:09.557] 发送广播UDS (0x7DF): 10 03
[16:35:09.558] 接收广播响应: 7F 10 22
[16:35:09.601] ❌ 广播UDS 10 03 失败，尝试恢复序列...
[16:35:09.602] 3a. 发送UDS 10 01 (默认会话) - 广播0x7DF...
[16:35:09.602] 发送广播UDS (0x7DF): 10 01
[16:35:09.603] 接收广播响应: 50 01 00 32 01 F4
[16:35:09.641] ✅ 广播UDS 10 01 成功
[16:35:09.742] 3b. 重新发送UDS 10 03 (扩展会话) - 广播0x7DF...
[16:35:09.742] 发送广播UDS (0x7DF): 10 03
[16:35:09.743] 接收广播响应: 50 03 00 32 01 F4
[16:35:09.773] ✅ 广播UDS 10 03 (重试) 成功
[16:35:10.774] 4a. 再次确认编程会话状态...
[16:35:10.774] 发送UDS: 10 02
[16:35:10.774] 清空缓冲区数据: 7F 10 22
[16:35:10.774] 清空缓冲区数据: 50 01 00 32 01 F4
[16:35:10.774] 清空缓冲区数据: 50 03 00 32 01 F4
[16:35:10.876] 接收UDS: 50 02 00 32 01 F4
[16:35:10.876] ✅ 编程会话状态确认成功
[16:35:11.376] 4. 发送UDS 31 01 02 03 (启动例程) - 0x715...
[16:35:11.376] 发送UDS: 31 01 02 03
[16:35:11.478] 接收UDS: 71 01 02 03 00
[16:35:11.478] UDS 31响应: 71 01 02 03 00 (长度: 5)
[16:35:11.478] 检查条件: response[0]=71, 期望=0x71, 长度>=5
[16:35:11.478] ✅ UDS 31 01 02 03 成功
[16:35:11.578] 5. 发送UDS 85 02 (DTC控制) - 广播0x7DF...
[16:35:11.579] 发送广播UDS (0x7DF): 85 02
[16:35:11.580] 接收广播响应: C5 02
[16:35:11.613] ✅ UDS 85 02 成功
[16:35:11.714] 6. 发送UDS 28 03 01 (通信控制) - 广播0x7DF...
[16:35:11.714] 发送广播UDS (0x7DF): 28 03 01
[16:35:11.715] 接收广播响应: 68 03
[16:35:11.741] ✅ UDS 28 03 01 成功
[16:35:11.842] 7. 发送UDS 10 02 (再次编程会话) - 0x715...
[16:35:11.842] 发送UDS: 10 02
[16:35:11.842] 清空缓冲区数据: C5 02
[16:35:11.842] 清空缓冲区数据: 68 03
[16:35:11.943] 接收UDS: 50 02 00 32 01 F4
[16:35:11.943] ✅ UDS 10 02 (再次) 成功
[16:35:12.444] === 步骤2: 安全访问 ===
[16:35:12.444] 发送UDS 27 03 (请求种子)...
[16:35:12.444] 发送UDS: 27 03
[16:35:12.547] 接收UDS: 67 03 2A D9 E0 50 3F DE DE 9D 3C 84 25 4D AC 4D 11 E1
[16:35:12.547] 收到种子: 2A D9 E0 50 3F DE DE 9D 3C 84 25 4D AC 4D 11 E1
[16:35:12.548] 计算密钥，种子长度: 16字节, 种子: 2AD9E0503FDEDE9D3C84254DAC4D11E1
[16:35:12.550] calc_key输出: UDS 27服务 Key计算
==================

输入Seed: 2A D9 E0 50 3F DE DE 9D 3C 84 25 4D AC 4D 11 E1 
AES密钥:  BE 11 A1 C1 12 03 44 05 26 87 18 32 34 B9 A1 A2 

UDS 27 04要发送的Key:
====================
Key: 64 E3 B3 C4 82 46 8F DF 8B BC BE 5C CD 5A 76 A3 

格式化输出:
===========
连续格式: 64E3B3C482468FDF8BBCBE5CCD5A76A3
C数组格式: {0x64, 0xE3, 0xB3, 0xC4, 0x82, 0x46, 0x8F, 0xDF, 0x8B, 0xBC, 0xBE, 0x5C, 0xCD, 0x5A, 0x76, 0xA3}
[16:35:12.550] 计算得到密钥: 64 E3 B3 C4 82 46 8F DF 8B BC BE 5C CD 5A 76 A3
[16:35:12.550] 发送UDS 27 04 (发送密钥)...
[16:35:12.550] 发送UDS: 27 04 64 E3 B3 C4 82 46 8F DF 8B BC BE 5C CD 5A 76 A3
[16:35:12.653] 接收UDS: 67 04
[16:35:12.654] ✅ 安全访问成功
[16:35:13.154] === 步骤3: 写入指纹 ===
[16:35:13.154] 发送UDS: 2E F1 84 00 01 02 03 04 05 06 07 08
[16:35:13.257] 接收UDS: 6E F1 84
[16:35:13.257] ✅ 写入指纹成功
[16:35:13.757] === 步骤4: 例程控制 ===
[16:35:13.758] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:35:13.860] 接收UDS: 71 01 FF 00 00
[16:35:13.860] ✅ 例程控制成功
[16:35:14.361] 开始刷写 3 个blocks...
[16:35:14.361] 
==================== 开始刷写 Block 1 ====================
[16:35:14.361] 文件: 00004400_00005200.bin
[16:35:14.361] 地址: 0x00004400 - 0x000095FF
[16:35:14.361] 大小: 20992 字节
[16:35:14.361] === 步骤4a: 内存擦写 Block 1 ===
[16:35:14.361] 擦写地址: 0x00004400 -> 00 44 00
[16:35:14.361] 擦写长度: 20992 -> 00 00 52 00
[16:35:14.361] 完整擦写命令: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:35:14.361] 擦写地址: 0x00004400, 长度: 20992 字节
[16:35:14.361] 发送UDS: 31 01 FF 00 44 00 00 44 00 00 00 52 00
[16:35:14.463] 接收UDS: 71 01 FF 00 00
[16:35:14.463] ✅ 内存擦写成功
[16:35:14.664] === 步骤5: 请求下载 Block 1 ===
[16:35:14.664] 块信息: 00004400_00005200.bin
[16:35:14.664] 地址: 0x00004400 (0x00004400)
[16:35:14.664] 长度: 20992 字节 (0x00005200)
[16:35:14.664] 发送UDS: 34 00 44 00 00 44 00 00 00 52 00
[16:35:14.766] 接收UDS: 74 20 01 02
[16:35:14.766] ✅ 请求下载成功
[16:35:14.766] 长度格式: 0x20 (0字节)
[16:35:14.766] 最大块长度: 256 (0x0100)
[16:35:14.867] === 步骤6: 传输数据 Block 1 ===
[16:35:14.867] 读取Block 1数据: 00004400_00005200.bin (20992 字节)
[16:35:14.867] 使用数据块大小: 254 字节 (ECU最大块长度: 256)
[16:35:14.867] 开始传输数据: 20992 字节, 分为 83 个数据包
[16:35:14.867] 发送UDS: 36 01 00 40 00 20 11 44 00 00 0D 6B 00 00 0D 6B 00 00 10 B5 03 4B 83 F3 08 88 02 F0 10 FF 02 F0 7E FB 00 40 00 20 10 B5 12 22 47 4B 1A 60 18 20 02 F0 59 FE 02 22 45 4B 1A 60 07 32 45 4B 1A 60 01 32 44 4B 1A 60 FA 20 C0 00 02 F0 C2 FE C0 B2 00 28 02 D0 01 20 02 F0 22 FB 3F 48 02 F0 AF FE 00 21 00 20 02 F0 29 FD 3D 4C 3D 4B 23 60 78 20 03 F0 FF FD 3C 4B 23 60 00 24 04 E0 F0 20 03 F0 F8 FD 01 34 E4 B2 18 2C 03 D8 37 4B 1B 68 00 2B F4 D0 35 4B 1B 68 00 2B 02 D1 03 20 02 F0 FF FA 01 22 32 4B 1A 60 29 4B 1B 68 00 2B 03 DA 30 4B 1B 68 DB 07 18 D5 2F 4A 13 68 80 21 09 06 0B 43 13 60 00 23 09 E0 2B 4A 11 68 02 20 01 43 11 60 11 68 81 43 11 60 01 33 DB B2 04 2B F3 D9 25 4A 13 68 5B 00 5B 08 13 60 00 24 23 4B 1C 60 23 4B 24 4A 1A 60 C0 21 89 00 23 4A 11 60 23 4A 1A 60
[16:35:14.986] 接收UDS: 7F 36 13
[16:35:14.987] ❌ 传输数据块 1 失败, NRC=0x13
[16:35:14.987] ❌ Block 1 刷写失败
