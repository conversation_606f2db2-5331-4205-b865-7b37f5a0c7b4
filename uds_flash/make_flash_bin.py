#!/usr/bin/env python3
"""
合并多个HEX文件生成刷写用的BIN文件
基于hex2bin.py，支持合并多个Intel HEX文件并按顺序生成刷写用的bin文件和JSON描述
"""

import os
import json
import sys
from datetime import datetime
from hex2bin import IntelHexParser

class FlashBinMaker:
    """刷写BIN文件制作器"""
    
    def __init__(self, output_dir="bins"):
        self.output_dir = output_dir
        self.all_blocks = []  # 所有块的信息
        self.total_size = 0
        
    def process_hex_file(self, hex_file_path, file_priority=0):
        """处理单个HEX文件"""
        print(f"\n处理HEX文件: {hex_file_path}")
        
        if not os.path.exists(hex_file_path):
            print(f"❌ 文件不存在: {hex_file_path}")
            return False
        
        # 创建解析器
        parser = IntelHexParser(hex_file_path)
        
        # 解析HEX文件
        if not parser.parse_hex_file():
            print(f"❌ 解析失败: {hex_file_path}")
            return False
        
        # 查找连续块
        parser.find_continuous_blocks(min_block_size=1)  # 最小1字节，不过滤小块
        
        if not parser.continuous_blocks:
            print(f"⚠️ 未找到数据块: {hex_file_path}")
            return True
        
        # 处理每个连续块
        for i, block in enumerate(parser.continuous_blocks):
            start_addr = block['start_address']
            size = block['size']
            
            # 生成文件名，处理重复地址的情况
            base_filename = f"{start_addr:08X}_{size:08X}"
            bin_filename = f"{base_filename}.bin"
            bin_filepath = os.path.join(self.output_dir, bin_filename)

            # 如果文件已存在，添加来源标识
            if os.path.exists(bin_filepath):
                source_name = os.path.splitext(os.path.basename(hex_file_path))[0]
                bin_filename = f"{base_filename}_{source_name}.bin"
                bin_filepath = os.path.join(self.output_dir, bin_filename)
            
            # 提取数据
            block_data = parser.extract_block_data(start_addr, size)
            
            # 保存二进制文件
            try:
                # 确保输出目录存在
                if not os.path.exists(self.output_dir):
                    os.makedirs(self.output_dir)
                
                with open(bin_filepath, 'wb') as f:
                    f.write(block_data)
                print(f"保存块: {bin_filename} ({size} 字节)")
                
                # 记录块信息
                block_info = {
                    'block_index': len(self.all_blocks) + 1,
                    'filename': bin_filename,
                    'start_address': f"0x{start_addr:08X}",
                    'end_address': f"0x{block['end_address']:08X}",
                    'size': size,
                    'size_hex': f"0x{size:08X}",
                    'md5': self.calculate_md5(block_data),
                    'source_file': os.path.basename(hex_file_path),
                    'file_priority': file_priority,
                    'created_time': datetime.now().isoformat()
                }
                
                self.all_blocks.append(block_info)
                self.total_size += size
                
            except Exception as e:
                print(f"❌ 保存文件失败 {bin_filename}: {e}")
                return False
        
        return True
    
    def calculate_md5(self, data):
        """计算数据的MD5哈希值"""
        import hashlib
        return hashlib.md5(data).hexdigest()
    
    def sort_blocks_by_priority_and_address(self):
        """按优先级和地址排序块"""
        # 首先按file_priority排序，然后按start_address排序
        self.all_blocks.sort(key=lambda x: (x['file_priority'], int(x['start_address'], 16)))
        
        # 重新分配block_index
        for i, block in enumerate(self.all_blocks):
            block['block_index'] = i + 1
    
    def save_blocks_info_json(self):
        """保存JSON描述文件"""
        json_info = {
            'created_time': datetime.now().isoformat(),
            'total_blocks': len(self.all_blocks),
            'total_size': self.total_size,
            'description': 'Combined flash binary files from multiple HEX sources',
            'source_files': list(set(block['source_file'] for block in self.all_blocks)),
            'blocks': self.all_blocks
        }
        
        json_filepath = os.path.join(self.output_dir, 'blocks_info.json')
        try:
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(json_info, f, indent=2, ensure_ascii=False)
            print(f"✅ 保存JSON描述文件: {json_filepath}")
            return True
        except Exception as e:
            print(f"❌ 保存JSON文件失败: {e}")
            return False
    
    def print_summary(self):
        """打印处理摘要"""
        print("\n" + "=" * 60)
        print("刷写BIN文件制作摘要")
        print("=" * 60)
        
        if not self.all_blocks:
            print("未生成任何块")
            return
        
        print(f"总块数:       {len(self.all_blocks)}")
        print(f"总大小:       {self.total_size:,} 字节")
        print(f"输出目录:     {self.output_dir}")
        
        print(f"\n刷写顺序:")
        for block in self.all_blocks:
            print(f"  块 {block['block_index']}: {block['filename']}")
            print(f"    地址: {block['start_address']} - {block['end_address']}")
            print(f"    大小: {block['size']:,} 字节")
            print(f"    来源: {block['source_file']}")
            print()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("用法: python3 make_flash_bin.py [输出目录]")
        print("默认: python3 make_flash_bin.py bins")
        print("说明:")
        print("  将kaiwo.hex和extracted_flash_*.hex合并生成刷写用的bin文件")
        print("  输出目录:    二进制文件输出目录 (默认: bins)")
        print("  自动按地址顺序排列，kaiwo.hex优先级高于extracted文件")
        sys.exit(0)
    
    # 解析参数
    output_dir = sys.argv[1] if len(sys.argv) > 1 else 'bins'
    
    print("刷写BIN文件制作器")
    print("=" * 20)
    print(f"输出目录: {output_dir}")
    print()
    
    # 创建制作器
    maker = FlashBinMaker(output_dir)
    
    try:
        # 处理主要的HEX文件 (优先级0，最高)
        main_hex = 'kaiwo.hex'
        if os.path.exists(main_hex):
            if not maker.process_hex_file(main_hex, file_priority=0):
                print(f"❌ 处理主HEX文件失败: {main_hex}")
                sys.exit(1)
        else:
            print(f"⚠️ 主HEX文件不存在: {main_hex}")
        
        # 查找并处理extracted HEX文件 (优先级1)
        extracted_files = []
        for filename in os.listdir('.'):
            if filename.startswith('extracted_flash_') and filename.endswith('.hex'):
                extracted_files.append(filename)
        
        # 按文件名排序extracted文件
        extracted_files.sort()
        
        for extracted_hex in extracted_files:
            print(f"发现extracted文件: {extracted_hex}")
            if not maker.process_hex_file(extracted_hex, file_priority=1):
                print(f"❌ 处理extracted文件失败: {extracted_hex}")
                sys.exit(1)
        
        if not extracted_files:
            print("⚠️ 未找到extracted_flash_*.hex文件")
        
        # 检查是否有任何块
        if not maker.all_blocks:
            print("❌ 未生成任何数据块")
            sys.exit(1)
        
        # 按优先级和地址排序
        maker.sort_blocks_by_priority_and_address()
        
        # 保存JSON描述文件
        if not maker.save_blocks_info_json():
            print("❌ 保存JSON文件失败")
            sys.exit(1)
        
        # 打印摘要
        maker.print_summary()
        
        print(f"🎉 刷写BIN文件制作完成!")
        print(f"生成了 {len(maker.all_blocks)} 个二进制文件")
        print(f"总大小: {maker.total_size:,} 字节")
        print(f"输出目录: {output_dir}")
        print(f"JSON描述: {output_dir}/blocks_info.json")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
