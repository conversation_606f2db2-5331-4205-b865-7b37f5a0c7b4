#!/usr/bin/env python3
"""
Intel HEX to Binary Converter
从kaiwo.hex文件中提取所有二进制块，并生成相应的bin文件和json描述文件
"""

import os
import json
import sys
from datetime import datetime

class IntelHexParser:
    """Intel HEX文件解析器"""
    
    def __init__(self, hex_file_path):
        self.hex_file_path = hex_file_path
        self.memory_blocks = {}  # {address: data}
        self.continuous_blocks = []  # 连续的内存块信息
        
    def parse_hex_line(self, line):
        """解析单行Intel HEX数据"""
        line = line.strip()
        if not line.startswith(':'):
            return None
        
        try:
            # 移除冒号并转换为字节
            hex_data = line[1:]
            
            # 解析各个字段
            byte_count = int(hex_data[0:2], 16)
            address = int(hex_data[2:6], 16)
            record_type = int(hex_data[6:8], 16)
            data = hex_data[8:8+byte_count*2]
            checksum = int(hex_data[8+byte_count*2:8+byte_count*2+2], 16)
            
            # 验证校验和
            calculated_checksum = 0
            for i in range(0, len(hex_data)-2, 2):
                calculated_checksum += int(hex_data[i:i+2], 16)
            calculated_checksum = (256 - (calculated_checksum & 0xFF)) & 0xFF
            
            if calculated_checksum != checksum:
                print(f"警告: 校验和错误在行: {line}")
                return None
            
            return {
                'byte_count': byte_count,
                'address': address,
                'record_type': record_type,
                'data': data,
                'checksum': checksum
            }
            
        except Exception as e:
            print(f"解析错误在行 '{line}': {e}")
            return None
    
    def parse_hex_file(self):
        """解析整个HEX文件"""
        print(f"解析HEX文件: {self.hex_file_path}")
        
        try:
            with open(self.hex_file_path, 'r') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return False
        
        extended_address = 0  # 扩展地址
        total_data_records = 0
        
        for line_num, line in enumerate(lines, 1):
            record = self.parse_hex_line(line)
            if not record:
                continue
            
            record_type = record['record_type']
            
            if record_type == 0x00:  # 数据记录
                # 计算实际地址
                actual_address = extended_address + record['address']
                
                # 将十六进制数据转换为字节
                data_bytes = bytes.fromhex(record['data'])
                
                # 存储到内存块中
                for i, byte_val in enumerate(data_bytes):
                    self.memory_blocks[actual_address + i] = byte_val
                
                total_data_records += 1
                
            elif record_type == 0x01:  # 文件结束记录
                print(f"文件结束记录，共处理 {total_data_records} 条数据记录")
                break
                
            elif record_type == 0x02:  # 扩展段地址记录
                segment = int(record['data'], 16)
                extended_address = segment << 4
                print(f"扩展段地址: 0x{extended_address:08X}")
                
            elif record_type == 0x04:  # 扩展线性地址记录
                extended_linear = int(record['data'], 16)
                extended_address = extended_linear << 16
                print(f"扩展线性地址: 0x{extended_address:08X}")
                
            elif record_type == 0x03:  # 开始段地址记录
                print(f"开始段地址记录: {record['data']}")
                
            elif record_type == 0x05:  # 开始线性地址记录
                print(f"开始线性地址记录: {record['data']}")
                
            else:
                print(f"未知记录类型: 0x{record_type:02X} 在行 {line_num}")
        
        print(f"解析完成，总共 {len(self.memory_blocks)} 字节数据")
        return True
    
    def find_continuous_blocks(self, min_block_size=256):
        """查找连续的内存块"""
        if not self.memory_blocks:
            return
        
        # 获取所有地址并排序
        addresses = sorted(self.memory_blocks.keys())
        
        current_block_start = addresses[0]
        current_block_end = addresses[0]
        
        for addr in addresses[1:]:
            if addr == current_block_end + 1:
                # 连续地址，扩展当前块
                current_block_end = addr
            else:
                # 地址不连续，保存当前块并开始新块
                block_size = current_block_end - current_block_start + 1
                if block_size >= min_block_size:
                    self.continuous_blocks.append({
                        'start_address': current_block_start,
                        'end_address': current_block_end,
                        'size': block_size
                    })
                
                current_block_start = addr
                current_block_end = addr
        
        # 处理最后一个块
        block_size = current_block_end - current_block_start + 1
        if block_size >= min_block_size:
            self.continuous_blocks.append({
                'start_address': current_block_start,
                'end_address': current_block_end,
                'size': block_size
            })
        
        print(f"找到 {len(self.continuous_blocks)} 个连续块 (最小大小: {min_block_size} 字节)")
        for i, block in enumerate(self.continuous_blocks):
            print(f"  块 {i+1}: 0x{block['start_address']:08X} - 0x{block['end_address']:08X} ({block['size']} 字节)")
    
    def extract_block_data(self, start_address, size):
        """提取指定地址范围的数据"""
        data = bytearray()
        
        for addr in range(start_address, start_address + size):
            if addr in self.memory_blocks:
                data.append(self.memory_blocks[addr])
            else:
                # 如果地址没有数据，填充0xFF
                data.append(0xFF)
        
        return bytes(data)
    
    def save_binary_blocks(self, output_dir="bins"):
        """保存二进制块到文件"""
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        
        block_info_list = []
        
        for i, block in enumerate(self.continuous_blocks):
            start_addr = block['start_address']
            size = block['size']
            
            # 生成文件名
            bin_filename = f"{start_addr:08X}_{size:08X}.bin"
            bin_filepath = os.path.join(output_dir, bin_filename)
            
            # 提取数据
            block_data = self.extract_block_data(start_addr, size)
            
            # 保存二进制文件
            try:
                with open(bin_filepath, 'wb') as f:
                    f.write(block_data)
                print(f"保存块 {i+1}: {bin_filename} ({size} 字节)")
                
                # 记录块信息
                block_info = {
                    'block_index': i + 1,
                    'filename': bin_filename,
                    'start_address': f"0x{start_addr:08X}",
                    'end_address': f"0x{block['end_address']:08X}",
                    'size': size,
                    'size_hex': f"0x{size:08X}",
                    'md5': self.calculate_md5(block_data),
                    'created_time': datetime.now().isoformat()
                }
                block_info_list.append(block_info)
                
            except Exception as e:
                print(f"保存文件失败 {bin_filename}: {e}")
        
        # 保存JSON描述文件
        json_info = {
            'source_file': os.path.basename(self.hex_file_path),
            'extraction_time': datetime.now().isoformat(),
            'total_blocks': len(block_info_list),
            'total_size': sum(block['size'] for block in block_info_list),
            'blocks': block_info_list
        }
        
        json_filepath = os.path.join(output_dir, 'blocks_info.json')
        try:
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(json_info, f, indent=2, ensure_ascii=False)
            print(f"保存JSON描述文件: {json_filepath}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
        
        return block_info_list
    
    def calculate_md5(self, data):
        """计算数据的MD5哈希值"""
        import hashlib
        return hashlib.md5(data).hexdigest()
    
    def print_summary(self):
        """打印解析摘要"""
        print("\n" + "=" * 60)
        print("Intel HEX解析摘要")
        print("=" * 60)
        
        if not self.memory_blocks:
            print("未找到任何数据")
            return
        
        addresses = sorted(self.memory_blocks.keys())
        min_addr = addresses[0]
        max_addr = addresses[-1]
        total_bytes = len(self.memory_blocks)
        
        print(f"地址范围:     0x{min_addr:08X} - 0x{max_addr:08X}")
        print(f"总数据字节:   {total_bytes}")
        print(f"地址跨度:     {max_addr - min_addr + 1} 字节")
        print(f"数据密度:     {total_bytes / (max_addr - min_addr + 1) * 100:.1f}%")
        print(f"连续块数:     {len(self.continuous_blocks)}")
        
        print(f"\n连续块详情:")
        for i, block in enumerate(self.continuous_blocks):
            print(f"  块 {i+1}: 0x{block['start_address']:08X} - 0x{block['end_address']:08X} ({block['size']:,} 字节)")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("用法: python3 hex2bin.py [HEX文件] [输出目录] [最小块大小]")
        print("默认: python3 hex2bin.py kaiwo.hex bins 1")
        print("说明:")
        print("  HEX文件:     Intel HEX格式的输入文件 (默认: kaiwo.hex)")
        print("  输出目录:    二进制文件输出目录 (默认: bins)")
        print("  最小块大小:  最小连续块大小，字节 (默认: 1，不过滤)")
        sys.exit(0)
    
    # 解析参数
    hex_file = sys.argv[1] if len(sys.argv) > 1 else 'kaiwo.hex'
    output_dir = sys.argv[2] if len(sys.argv) > 2 else 'bins'
    min_block_size = int(sys.argv[3]) if len(sys.argv) > 3 else 1  # 改为1字节，不过滤任何块
    
    # 检查输入文件
    if not os.path.exists(hex_file):
        print(f"❌ 输入文件不存在: {hex_file}")
        sys.exit(1)
    
    print("Intel HEX to Binary Converter")
    print("=" * 30)
    print(f"输入文件: {hex_file}")
    print(f"输出目录: {output_dir}")
    print(f"最小块大小: {min_block_size} 字节")
    print()
    
    # 创建解析器并处理
    parser = IntelHexParser(hex_file)
    
    try:
        # 解析HEX文件
        if not parser.parse_hex_file():
            print("❌ HEX文件解析失败")
            sys.exit(1)
        
        # 查找连续块
        parser.find_continuous_blocks(min_block_size)
        
        # 保存二进制块
        block_info = parser.save_binary_blocks(output_dir)
        
        # 打印摘要
        parser.print_summary()
        
        print(f"\n🎉 转换完成!")
        print(f"生成了 {len(block_info)} 个二进制文件")
        print(f"输出目录: {output_dir}")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
