#!/usr/bin/env python3
"""
CAN日志到Intel HEX转换器
从UDS 36服务的CAN日志中提取数据并生成Intel HEX文件
"""

import sys
from datetime import datetime

def calculate_checksum(data_bytes):
    """计算Intel HEX校验和"""
    checksum = sum(data_bytes) & 0xFF
    return (256 - checksum) & 0xFF

def create_intel_hex_record(address, data):
    """创建Intel HEX记录"""
    byte_count = len(data)
    addr_high = (address >> 8) & 0xFF
    addr_low = address & 0xFF
    record_type = 0x00  # 数据记录
    
    # 构建记录字节
    record_bytes = [byte_count, addr_high, addr_low, record_type] + list(data)
    checksum = calculate_checksum(record_bytes)
    
    # 格式化为HEX字符串
    hex_string = ":" + "".join(f"{b:02X}" for b in record_bytes) + f"{checksum:02X}"
    return hex_string

def create_extended_linear_address_record(high_address):
    """创建扩展线性地址记录"""
    byte_count = 0x02
    address = 0x0000
    record_type = 0x04
    data = [(high_address >> 8) & 0xFF, high_address & 0xFF]
    
    record_bytes = [byte_count, 0x00, 0x00, record_type] + data
    checksum = calculate_checksum(record_bytes)
    
    hex_string = ":" + "".join(f"{b:02X}" for b in record_bytes) + f"{checksum:02X}"
    return hex_string

def create_end_of_file_record():
    """创建文件结束记录"""
    return ":00000001FF"

def main():
    """主函数"""
    print("CAN日志到Intel HEX转换器")
    print("=" * 30)
    
    # 从CAN日志分析得到的数据
    base_address = 0x0100FF80  # UDS 34服务中的地址
    data_length = 0x80         # UDS 34服务中的长度 (128字节)
    
    # 从UDS 36服务提取的实际数据 (128字节)
    extracted_data = [
        # 前面大部分是0x00
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x97, 0x91, 0x1D, 0x00,
        0x00, 0x3A, 0x00, 0x00, 0x00, 0x80, 0x50, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    ]
    
    print(f"基地址: 0x{base_address:08X}")
    print(f"数据长度: {data_length} 字节")
    print(f"提取数据长度: {len(extracted_data)} 字节")
    
    if len(extracted_data) != data_length:
        print(f"警告: 提取的数据长度({len(extracted_data)})与期望长度({data_length})不匹配")
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"extracted_flash_{timestamp}.hex"
    
    print(f"生成Intel HEX文件: {output_filename}")
    
    try:
        with open(output_filename, 'w') as f:
            # 计算扩展线性地址
            extended_address = (base_address >> 16) & 0xFFFF
            if extended_address != 0:
                # 写入扩展线性地址记录
                ela_record = create_extended_linear_address_record(extended_address)
                f.write(ela_record + "\n")
                print(f"扩展线性地址: 0x{extended_address:04X}")
            
            # 计算16位地址
            addr_16bit = base_address & 0xFFFF
            
            # 按16字节分块写入数据
            bytes_per_line = 16
            for i in range(0, len(extracted_data), bytes_per_line):
                chunk = extracted_data[i:i+bytes_per_line]
                current_address = addr_16bit + i
                
                # 检查是否需要新的扩展线性地址记录
                if current_address > 0xFFFF:
                    # 地址溢出，需要新的扩展线性地址
                    extended_address += 1
                    ela_record = create_extended_linear_address_record(extended_address)
                    f.write(ela_record + "\n")
                    current_address = current_address & 0xFFFF
                
                # 创建数据记录
                hex_record = create_intel_hex_record(current_address, chunk)
                f.write(hex_record + "\n")
            
            # 写入文件结束记录
            eof_record = create_end_of_file_record()
            f.write(eof_record + "\n")
        
        print(f"✅ Intel HEX文件生成成功: {output_filename}")
        
        # 显示文件内容摘要
        print("\n文件内容摘要:")
        with open(output_filename, 'r') as f:
            lines = f.readlines()
            print(f"总行数: {len(lines)}")
            print(f"前3行:")
            for i, line in enumerate(lines[:3]):
                print(f"  {i+1}: {line.strip()}")
            if len(lines) > 3:
                print(f"  ...")
                print(f"  {len(lines)}: {lines[-1].strip()}")
        
        # 验证数据
        print(f"\n数据验证:")
        non_zero_bytes = [i for i, b in enumerate(extracted_data) if b != 0]
        if non_zero_bytes:
            print(f"非零字节位置: {non_zero_bytes}")
            print(f"非零字节值: {[hex(extracted_data[i]) for i in non_zero_bytes]}")
        else:
            print("所有字节都为0x00")
            
    except Exception as e:
        print(f"❌ 生成文件失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
