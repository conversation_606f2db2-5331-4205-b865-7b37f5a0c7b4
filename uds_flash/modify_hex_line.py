#!/usr/bin/env python3
"""
修改Intel HEX文件中的特定行，将数据内容改为FE填充
"""

import sys
import os

def calculate_intel_hex_checksum(hex_data_without_checksum):
    """计算Intel HEX校验和"""
    checksum = 0
    # 将十六进制字符串按2字符分组，转换为字节并累加
    for i in range(0, len(hex_data_without_checksum), 2):
        byte_val = int(hex_data_without_checksum[i:i+2], 16)
        checksum += byte_val
    
    # Intel HEX校验和算法：256 - (sum & 0xFF) & 0xFF
    checksum = (256 - (checksum & 0xFF)) & 0xFF
    return checksum

def parse_intel_hex_line(line):
    """解析Intel HEX行"""
    line = line.strip()
    if not line.startswith(':'):
        return None
    
    hex_data = line[1:]  # 移除冒号
    
    # 解析各字段
    byte_count = int(hex_data[0:2], 16)
    address = int(hex_data[2:6], 16)
    record_type = int(hex_data[6:8], 16)
    data = hex_data[8:8+byte_count*2]
    checksum = int(hex_data[8+byte_count*2:8+byte_count*2+2], 16)
    
    return {
        'byte_count': byte_count,
        'address': address,
        'record_type': record_type,
        'data': data,
        'checksum': checksum,
        'original_line': line
    }

def create_modified_hex_line(byte_count, address, record_type, fill_byte=0xFE):
    """创建修改后的Intel HEX行"""
    # 生成填充数据
    data_bytes = [fill_byte] * byte_count
    data_hex = ''.join(f'{b:02X}' for b in data_bytes)
    
    # 构建不含校验和的记录
    record_without_checksum = f'{byte_count:02X}{address:04X}{record_type:02X}{data_hex}'
    
    # 计算校验和
    checksum = calculate_intel_hex_checksum(record_without_checksum)
    
    # 构建完整记录
    complete_record = f':{record_without_checksum}{checksum:02X}'
    
    return complete_record

def modify_hex_file(input_file, output_file, target_address=0x44C0):
    """修改HEX文件中指定地址的行"""
    
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    print(f"修改Intel HEX文件")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"目标地址: 0x{target_address:04X}")
    print()
    
    try:
        # 读取原文件
        with open(input_file, 'r') as f:
            lines = f.readlines()
        
        modified_lines = []
        found_target = False
        extended_address = 0  # 跟踪扩展地址
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            if not line:
                modified_lines.append(line)
                continue
            
            # 解析当前行
            parsed = parse_intel_hex_line(line)
            if not parsed:
                modified_lines.append(line)
                continue
            
            # 处理扩展线性地址记录
            if parsed['record_type'] == 0x04:
                extended_address = int(parsed['data'], 16) << 16
                print(f"扩展线性地址: 0x{extended_address:08X}")
                modified_lines.append(line)
                continue
            
            # 处理数据记录
            if parsed['record_type'] == 0x00:
                actual_address = extended_address + parsed['address']
                
                # 检查是否是目标地址
                if actual_address == target_address:
                    print(f"找到目标地址 0x{actual_address:08X} 在行 {line_num}")
                    print(f"原始数据: {parsed['data']}")
                    print(f"原始校验和: 0x{parsed['checksum']:02X}")
                    
                    # 验证原始校验和
                    original_without_checksum = f"{parsed['byte_count']:02X}{parsed['address']:04X}{parsed['record_type']:02X}{parsed['data']}"
                    calculated_original_checksum = calculate_intel_hex_checksum(original_without_checksum)
                    print(f"验证原始校验和: 0x{calculated_original_checksum:02X} {'✅' if calculated_original_checksum == parsed['checksum'] else '❌'}")
                    
                    # 创建修改后的行
                    modified_line = create_modified_hex_line(
                        parsed['byte_count'], 
                        parsed['address'], 
                        parsed['record_type'], 
                        0xFE
                    )
                    
                    print(f"修改后数据: {'FE' * parsed['byte_count']}")
                    print(f"新校验和: {modified_line[-2:]}")
                    print(f"完整新行: {modified_line}")
                    print()
                    
                    modified_lines.append(modified_line)
                    found_target = True
                else:
                    modified_lines.append(line)
            else:
                modified_lines.append(line)
        
        if not found_target:
            print(f"⚠️ 未找到目标地址 0x{target_address:04X}")
            return False
        
        # 写入修改后的文件
        with open(output_file, 'w') as f:
            for line in modified_lines:
                f.write(line + '\n')
        
        print(f"✅ 修改完成，保存到: {output_file}")
        
        # 验证文件完整性
        print(f"\n验证文件完整性:")
        with open(output_file, 'r') as f:
            new_lines = f.readlines()
        
        print(f"原文件行数: {len(lines)}")
        print(f"新文件行数: {len(new_lines)}")
        print(f"行数匹配: {'✅' if len(lines) == len(new_lines) else '❌'}")
        
        # 检查文件结束记录
        last_line = new_lines[-1].strip() if new_lines else ""
        has_eof = last_line == ":00000001FF"
        print(f"文件结束记录: {'✅' if has_eof else '❌'} ({last_line})")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("用法: python3 modify_hex_line.py [输入文件] [输出文件] [目标地址]")
        print("默认: python3 modify_hex_line.py kaiwo.hex kaiwo_fe.hex 0x44C0")
        print("说明:")
        print("  将指定地址的数据内容改为FE填充")
        print("  自动重新计算校验和")
        print("  保持文件完整性")
        sys.exit(0)
    
    # 解析参数
    input_file = sys.argv[1] if len(sys.argv) > 1 else 'kaiwo.hex'
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'kaiwo_fe.hex'
    target_address = int(sys.argv[3], 16) if len(sys.argv) > 3 else 0x44C0
    
    # 执行修改
    success = modify_hex_file(input_file, output_file, target_address)
    
    if success:
        print(f"\n🎉 修改成功完成!")
        print(f"新文件: {output_file}")
    else:
        print(f"\n❌ 修改失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
