#!/usr/bin/env python3
"""
CAN ID扫描工具
从CAN ID 0x000到0x7FF发送8字节全FF数据包，
发现活跃的ECU并记录通信内容。
"""

import socket
import struct
import time
import sys
import signal
from datetime import datetime
from collections import defaultdict

# CAN帧格式常量
CAN_RAW = 1
CAN_EFF_FLAG = 0x80000000
CAN_RTR_FLAG = 0x40000000
CAN_ERR_FLAG = 0x20000000

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class CANIDScanner:
    """CAN ID扫描器"""

    def __init__(self, start_id=0x000, end_id=0x7FF, interface='can0'):
        self.start_id = start_id
        self.end_id = end_id
        self.interface = interface
        self.socket = None
        self.running = True
        self.log_file = None

        # 存储发现的ECU通信记录
        self.discovered_ecus = {}  # {request_id: [response_records]}
        self.all_responses = []    # 所有响应记录

        # 测试数据包：8字节全FF
        self.test_data = b'\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF'

        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.running = False
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置接收超时
            self.socket.settimeout(0.1)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        import os
        
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"can_id_scan_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"CAN ID扫描日志 - {datetime.now()}\n")
            self.log_file.write(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}\n")
            self.log_file.write(f"测试数据: FF FF FF FF FF FF FF FF\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frames(self, timeout=0.5):
        """接收CAN帧（可能有多个响应）"""
        responses = []
        start_time = time.time()
        
        try:
            # 临时设置超时
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.05)  # 短超时，快速收集响应
            
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)
                    responses.append(frame)
                    self.log_message(f"接收: {frame}", False)
                except socket.timeout:
                    # 短暂超时是正常的，继续等待
                    continue
                except Exception as e:
                    self.log_message(f"接收失败: {e}", False)
                    break
            
            # 恢复原超时设置
            self.socket.settimeout(original_timeout)
            
        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
        
        return responses
    
    def scan_can_id(self, request_id):
        """扫描单个CAN ID"""
        # 创建测试帧
        test_frame = CANFrame(request_id, self.test_data)
        
        # 发送测试帧
        if not self.send_can_frame(test_frame):
            return []
        
        # 等待响应
        responses = self.receive_can_frames(0.5)
        
        # 过滤掉自己发送的帧（回环）
        filtered_responses = []
        for response in responses:
            if response.can_id != request_id:  # 不是自己发送的帧
                filtered_responses.append(response)
        
        return filtered_responses
    
    def scan_range(self):
        """扫描CAN ID范围"""
        print(f"\n开始CAN ID扫描: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print(f"测试数据: {' '.join(f'{b:02X}' for b in self.test_data)}")
        print("=" * 60)

        total_ids = self.end_id - self.start_id + 1
        scanned = 0
        found_ecus = 0

        for can_id in range(self.start_id, self.end_id + 1):
            if not self.running:
                break
            
            scanned += 1
            progress = (scanned / total_ids) * 100
            
            # 每100个ID显示一次进度
            if scanned % 100 == 0 or scanned == 1:
                print(f"\r扫描进度: {progress:5.1f}% (0x{can_id:03X})", end='', flush=True)
            
            responses = self.scan_can_id(can_id)
            
            if responses:
                found_ecus += 1
                print()  # 换行
                
                # 记录发现的ECU
                self.discovered_ecus[can_id] = []
                
                for response in responses:
                    response_record = {
                        'request_id': can_id,
                        'response_id': response.can_id,
                        'request_data': self.test_data,
                        'response_data': response.data,
                        'timestamp': datetime.now()
                    }
                    
                    self.discovered_ecus[can_id].append(response_record)
                    self.all_responses.append(response_record)
                
                # 显示发现的ECU
                msg = f"✅ 发现ECU: 请求ID=0x{can_id:03X}, 响应数={len(responses)}"
                self.log_message(msg)
                
                for i, response in enumerate(responses, 1):
                    detail_msg = f"  响应{i}: ID=0x{response.can_id:03X}, 数据={' '.join(f'{b:02X}' for b in response.data)}"
                    self.log_message(detail_msg, False)
                
                self.log_message("", False)  # 空行
            
            # 短暂延时避免总线拥塞
            time.sleep(0.01)
        
        print()  # 最终换行
        print(f"\n扫描完成: 发现 {found_ecus} 个活跃ECU")
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 60)
        print("CAN ID扫描结果摘要")
        print("=" * 60)
        
        if not self.discovered_ecus:
            print("未发现任何活跃的ECU")
            return
        
        print(f"发现 {len(self.discovered_ecus)} 个活跃ECU:")
        print()
        
        ecu_num = 1
        for request_id, responses in self.discovered_ecus.items():
            print(f"{ecu_num}. ECU #{ecu_num}")
            print(f"   请求CAN ID: 0x{request_id:03X}")
            print(f"   发送数据:   {' '.join(f'{b:02X}' for b in self.test_data)}")
            print(f"   响应数量:   {len(responses)}")
            
            for i, record in enumerate(responses, 1):
                print(f"   响应{i}:")
                print(f"     响应ID:   0x{record['response_id']:03X}")
                print(f"     响应数据: {' '.join(f'{b:02X}' for b in record['response_data'])}")
                print(f"     时间戳:   {record['timestamp'].strftime('%H:%M:%S.%f')[:-3]}")
            
            print()
            ecu_num += 1
        
        # 统计分析
        print("统计分析:")
        print("=" * 20)
        
        # 按响应ID分组
        response_id_count = defaultdict(int)
        for record in self.all_responses:
            response_id_count[record['response_id']] += 1
        
        print("响应ID统计:")
        for response_id, count in sorted(response_id_count.items()):
            print(f"  0x{response_id:03X}: {count} 次响应")
        
        # 记录到日志文件
        self.log_message("\n扫描结果摘要:", False)
        self.log_message("=" * 20, False)
        self.log_message(f"发现 {len(self.discovered_ecus)} 个活跃ECU", False)
        
        for request_id, responses in self.discovered_ecus.items():
            self.log_message(f"ECU: 0x{request_id:03X} -> {len(responses)} 个响应", False)
            for record in responses:
                self.log_message(f"  -> 0x{record['response_id']:03X}: {' '.join(f'{b:02X}' for b in record['response_data'])}", False)
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行扫描"""
        print("CAN ID扫描工具")
        print("=" * 20)
        print(f"接口: {self.interface}")
        print(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print(f"测试数据: {' '.join(f'{b:02X}' for b in self.test_data)}")

        # 初始化
        if not self.init_can_socket():
            return False

        if not self.open_log_file():
            return False

        try:
            # 执行扫描
            self.scan_range()

            # 打印结果
            self.print_summary()

            return True

        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False

        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False

        finally:
            self.cleanup()

def parse_can_id(can_id_str):
    """解析CAN ID字符串"""
    try:
        if can_id_str.startswith('0x') or can_id_str.startswith('0X'):
            return int(can_id_str, 16)
        else:
            return int(can_id_str, 16)
    except ValueError:
        raise ValueError(f"无效的CAN ID: {can_id_str}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 can_id_scan.py <开始CAN_ID> [结束CAN_ID] [接口]")
        print("示例: python3 can_id_scan.py 0x700")
        print("      python3 can_id_scan.py 0x700 0x7FF")
        print("      python3 can_id_scan.py 700 7FF can0")
        print("      python3 can_id_scan.py 0x000 0x7FF can0")
        print("说明:")
        print("  开始CAN_ID: 必需，扫描的起始CAN ID")
        print("  结束CAN_ID: 可选，扫描的结束CAN ID (默认为开始ID)")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)

    # 解析开始CAN ID
    try:
        start_id = parse_can_id(sys.argv[1])
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)

    # 解析结束CAN ID
    if len(sys.argv) > 2 and not sys.argv[2].startswith('can'):
        try:
            end_id = parse_can_id(sys.argv[2])
        except ValueError as e:
            print(f"❌ {e}")
            sys.exit(1)
    else:
        end_id = start_id  # 默认只扫描一个ID

    # 验证ID范围
    if start_id > end_id:
        print(f"❌ 开始ID (0x{start_id:03X}) 不能大于结束ID (0x{end_id:03X})")
        sys.exit(1)

    if start_id < 0 or end_id > 0x7FF:
        print(f"❌ CAN ID范围必须在 0x000 - 0x7FF 之间")
        sys.exit(1)

    # 解析接口名称
    interface = 'can0'
    if len(sys.argv) > 3:
        interface = sys.argv[3]
    elif len(sys.argv) > 2 and sys.argv[2].startswith('can'):
        interface = sys.argv[2]

    scanner = CANIDScanner(start_id, end_id, interface)

    try:
        success = scanner.run()
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
