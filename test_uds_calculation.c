#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"
#include "uds_config.h"

// 从uds_27_advanced.c复制的计算函数
void calculate_key_with_aes_from_uds(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key, int verbose) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);
    
    if (verbose) {
        printf("UDS 27服务Key计算过程:\n");
        printf("=====================\n");
        printf("输入Seed: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", seed[i]);
        }
        printf("\n");
        
        printf("AES密钥:  ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", aes_key[i]);
        }
        printf("\n");
        
        printf("发送Key:  ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", key[i]);
        }
        printf(" (标准AES结果)\n\n");
    }
}

int main() {
    printf("测试uds_27_advanced的key计算\n");
    printf("===========================\n\n");
    
    // 测试seed
    uint8_t test_seed[16] = {
        0x86, 0xBA, 0x46, 0x4A, 0x7C, 0xE0, 0x28, 0x14,
        0xD4, 0x02, 0x82, 0x90, 0xA3, 0xCA, 0xF5, 0x22
    };
    
    printf("测试Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", test_seed[i]);
    }
    printf("\n\n");
    
    // 使用默认配置
    uds_config_t config = DEFAULT_CONFIG;
    const uint8_t *aes_key = get_aes_key(config.key_type, config.custom_key);
    
    printf("默认配置:\n");
    printf("========\n");
    printf("密钥类型: %d (%s)\n", config.key_type, get_aes_key_description(config.key_type));
    printf("AES密钥: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", aes_key[i]);
    }
    printf("\n\n");
    
    // 计算key
    uint8_t calculated_key[16];
    calculate_key_with_aes_from_uds(test_seed, calculated_key, aes_key, 1);
    
    // 与calc_key的结果比较
    printf("与calc_key比较:\n");
    printf("==============\n");
    printf("uds_advanced: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", calculated_key[i]);
    }
    printf("\n");
    
    printf("calc_key:     1C57E5BD6E1A39BFDDFA9025CBA59CEC\n");
    
    // 检查是否匹配
    uint8_t expected[16] = {
        0x1C, 0x57, 0xE5, 0xBD, 0x6E, 0x1A, 0x39, 0xBF,
        0xDD, 0xFA, 0x90, 0x25, 0xCB, 0xA5, 0x9C, 0xEC
    };
    
    int match = memcmp(calculated_key, expected, 16) == 0;
    printf("结果: %s\n", match ? "✅ 匹配" : "❌ 不匹配");
    
    if (!match) {
        printf("\n差异分析:\n");
        for (int i = 0; i < 16; i++) {
            if (calculated_key[i] != expected[i]) {
                printf("字节%2d: 计算=%02X, 期望=%02X\n", i, calculated_key[i], expected[i]);
            }
        }
    }
    
    return match ? 0 : 1;
}
