#!/usr/bin/env python3
"""
UDS FF数据CAN ID扫描工具
1. 执行UDS 10 01 (默认会话)
2. 执行UDS 10 03 (扩展会话)
3. 向指定CAN ID范围发送8字节FF数据
4. 记录非周期性CAN ID的响应
"""

import socket
import struct
import time
import sys
import os
import signal
from datetime import datetime
from collections import defaultdict

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class FFCANIDScanner:
    """FF数据CAN ID扫描器"""

    def __init__(self, start_id, end_id, periodic_id, uds_request_id=None, uds_response_id=None, interface='can0'):
        self.start_id = int(start_id, 16) if isinstance(start_id, str) else start_id
        self.end_id = int(end_id, 16) if isinstance(end_id, str) else end_id
        self.periodic_id = int(periodic_id, 16) if isinstance(periodic_id, str) else periodic_id
        self.interface = interface

        # UDS会话使用的CAN ID (默认使用715/795，根据之前的测试)
        if uds_request_id is not None:
            self.uds_request_id = int(uds_request_id, 16) if isinstance(uds_request_id, str) else uds_request_id
        else:
            self.uds_request_id = 0x715

        if uds_response_id is not None:
            self.uds_response_id = int(uds_response_id, 16) if isinstance(uds_response_id, str) else uds_response_id
        else:
            self.uds_response_id = 0x795
        
        self.socket = None
        self.log_file = None
        self.running = True
        
        # 存储发现的响应
        self.discovered_responses = {}  # {request_id: [response_records]}
        self.all_responses = []
        
        # UDS服务常量
        self.UDS_SERVICE_10 = 0x10  # 诊断会话控制
        self.UDS_POSITIVE_RESPONSE = 0x40  # 正响应偏移
        
        # 测试数据：8字节全FF
        self.test_data = b'\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF'
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.running = False
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置接收超时
            self.socket.settimeout(0.1)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_FF_can_id_scan_{self.start_id:03X}_{self.end_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"FF数据CAN ID扫描 - {datetime.now()}\n")
            self.log_file.write(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}\n")
            self.log_file.write(f"周期CAN ID: 0x{self.periodic_id:03X} (将被忽略)\n")
            self.log_file.write(f"UDS请求ID: 0x{self.uds_request_id:03X}\n")
            self.log_file.write(f"UDS响应ID: 0x{self.uds_response_id:03X}\n")
            self.log_file.write(f"测试数据: FF FF FF FF FF FF FF FF\n")
            self.log_file.write(f"CAN接口: {self.interface}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=1.0):
        """接收CAN帧（只接收来自UDS响应ID的帧）"""
        try:
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.1)  # 短超时用于循环检查

            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)

                    # 记录所有接收到的帧（用于调试）
                    self.log_message(f"接收到帧: {frame}", False)

                    # 只处理来自UDS响应ID的帧
                    if frame.can_id == self.uds_response_id:
                        self.socket.settimeout(original_timeout)
                        self.log_message(f"接收UDS响应: {frame}", False)
                        return frame
                    else:
                        # 记录但忽略其他ID的帧
                        self.log_message(f"忽略非UDS响应帧: 0x{frame.can_id:03X} (期望0x{self.uds_response_id:03X})", False)

                except socket.timeout:
                    # 短超时是正常的，继续等待
                    continue
                except Exception as e:
                    self.socket.settimeout(original_timeout)
                    self.log_message(f"接收失败: {e}", False)
                    return None

            # 超时
            self.socket.settimeout(original_timeout)
            self.log_message(f"等待UDS响应ID 0x{self.uds_response_id:03X} 超时", False)
            return None

        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
            return None
    
    def receive_can_frames_scan(self, timeout=0.5):
        """接收CAN帧（扫描模式，接收所有非周期性帧）"""
        responses = []
        start_time = time.time()
        
        try:
            # 临时设置超时
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.05)  # 短超时，快速收集响应
            
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)
                    
                    # 记录所有接收到的帧
                    self.log_message(f"接收到帧: {frame}", False)
                    
                    # 过滤掉周期性CAN ID
                    if frame.can_id != self.periodic_id:
                        responses.append(frame)
                        self.log_message(f"检测到非周期响应: {frame}", False)
                    else:
                        self.log_message(f"忽略周期性帧: {frame}", False)
                        
                except socket.timeout:
                    # 短暂超时是正常的，继续等待
                    continue
                except Exception as e:
                    self.log_message(f"接收失败: {e}", False)
                    break
            
            # 恢复原超时设置
            self.socket.settimeout(original_timeout)
            
        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
        
        return responses
    
    def send_uds_request(self, service_data):
        """发送UDS请求并等待响应"""
        # 填充到8字节
        padded_data = service_data + b'\x55' * (8 - len(service_data))
        request_frame = CANFrame(self.uds_request_id, padded_data)

        if not self.send_can_frame(request_frame):
            return None

        # 等待响应
        return self.receive_can_frame(2.0)

    def execute_uds_10_01(self):
        """执行UDS 10 01 (默认会话)"""
        self.log_message("执行UDS 10 01 (默认会话)...")
        response = self.send_uds_request(b'\x02\x10\x01')

        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 01 成功")
            return True
        else:
            self.log_message("❌ UDS 10 01 失败")
            return False

    def execute_uds_10_03(self):
        """执行UDS 10 03 (扩展会话)"""
        self.log_message("执行UDS 10 03 (扩展会话)...")
        response = self.send_uds_request(b'\x02\x10\x03')

        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 03 成功")
            return True
        else:
            self.log_message("❌ UDS 10 03 失败")
            return False
    
    def scan_can_id(self, request_id):
        """扫描单个CAN ID"""
        # 创建FF测试帧
        test_frame = CANFrame(request_id, self.test_data)
        
        # 发送测试帧
        if not self.send_can_frame(test_frame):
            return []
        
        # 等待非周期性响应
        responses = self.receive_can_frames_scan(0.5)
        
        # 过滤掉自己发送的帧（回环）
        filtered_responses = []
        for response in responses:
            if response.can_id != request_id:  # 不是自己发送的帧
                filtered_responses.append(response)
        
        return filtered_responses
    
    def scan_range(self):
        """扫描CAN ID范围"""
        print(f"\n开始FF数据CAN ID扫描: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print(f"周期CAN ID: 0x{self.periodic_id:03X} (将被忽略)")
        print(f"测试数据: {' '.join(f'{b:02X}' for b in self.test_data)}")
        print("=" * 60)
        
        total_ids = self.end_id - self.start_id + 1
        scanned = 0
        found_responses = 0
        
        for can_id in range(self.start_id, self.end_id + 1):
            if not self.running:
                break
            
            scanned += 1
            progress = (scanned / total_ids) * 100
            
            # 每50个ID显示一次进度
            if scanned % 50 == 0 or scanned == 1:
                print(f"\r扫描进度: {progress:5.1f}% (0x{can_id:03X})", end='', flush=True)
            
            responses = self.scan_can_id(can_id)
            
            if responses:
                found_responses += 1
                print()  # 换行
                
                # 记录发现的响应
                self.discovered_responses[can_id] = []
                
                for response in responses:
                    response_record = {
                        'request_id': can_id,
                        'response_id': response.can_id,
                        'request_data': self.test_data,
                        'response_data': response.data,
                        'timestamp': datetime.now()
                    }
                    
                    self.discovered_responses[can_id].append(response_record)
                    self.all_responses.append(response_record)
                
                # 显示发现的响应
                msg = f"✅ 发现响应: 请求ID=0x{can_id:03X}, 响应数={len(responses)}"
                self.log_message(msg)
                
                for i, response in enumerate(responses, 1):
                    detail_msg = f"  响应{i}: ID=0x{response.can_id:03X}, 数据={' '.join(f'{b:02X}' for b in response.data)}"
                    self.log_message(detail_msg, False)
                
                self.log_message("", False)  # 空行
            
            # 短暂延时避免总线拥塞
            time.sleep(0.02)
        
        print()  # 最终换行
        print(f"\n扫描完成: 发现 {found_responses} 个有响应的CAN ID")
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 60)
        print("FF数据CAN ID扫描结果摘要")
        print("=" * 60)
        
        if not self.discovered_responses:
            print("未发现任何非周期性响应")
            return
        
        print(f"发现 {len(self.discovered_responses)} 个有响应的CAN ID:")
        print()
        
        response_num = 1
        for request_id, responses in self.discovered_responses.items():
            print(f"{response_num}. 请求ID: 0x{request_id:03X}")
            print(f"   发送数据: {' '.join(f'{b:02X}' for b in self.test_data)}")
            print(f"   响应数量: {len(responses)}")
            
            for i, record in enumerate(responses, 1):
                print(f"   响应{i}:")
                print(f"     响应ID:   0x{record['response_id']:03X}")
                print(f"     响应数据: {' '.join(f'{b:02X}' for b in record['response_data'])}")
                print(f"     时间戳:   {record['timestamp'].strftime('%H:%M:%S.%f')[:-3]}")
            
            print()
            response_num += 1
        
        # 统计分析
        print("统计分析:")
        print("=" * 20)
        
        # 按响应ID分组
        response_id_count = defaultdict(int)
        for record in self.all_responses:
            response_id_count[record['response_id']] += 1
        
        print("响应ID统计:")
        for response_id, count in sorted(response_id_count.items()):
            print(f"  0x{response_id:03X}: {count} 次响应")
        
        # 记录到日志文件
        self.log_message("\nFF数据扫描结果摘要:", False)
        self.log_message("=" * 20, False)
        self.log_message(f"发现 {len(self.discovered_responses)} 个有响应的CAN ID", False)
        
        for request_id, responses in self.discovered_responses.items():
            self.log_message(f"请求ID: 0x{request_id:03X} -> {len(responses)} 个响应", False)
            for record in responses:
                self.log_message(f"  -> 0x{record['response_id']:03X}: {' '.join(f'{b:02X}' for b in record['response_data'])}", False)
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行扫描"""
        print("FF数据CAN ID扫描工具")
        print("=" * 20)
        print(f"接口: {self.interface}")
        print(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print(f"周期CAN ID: 0x{self.periodic_id:03X} (将被忽略)")
        print(f"UDS请求ID: 0x{self.uds_request_id:03X}")
        print(f"UDS响应ID: 0x{self.uds_response_id:03X}")
        print(f"测试数据: {' '.join(f'{b:02X}' for b in self.test_data)}")
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行UDS 10 01
            if not self.execute_uds_10_01():
                print("⚠️  UDS 10 01失败，但继续进行FF数据扫描...")
            
            time.sleep(0.5)
            
            # 执行UDS 10 03
            if not self.execute_uds_10_03():
                print("⚠️  UDS 10 03失败，但继续进行FF数据扫描...")
            
            time.sleep(0.5)
            
            # 执行扫描
            self.scan_range()
            
            # 打印结果
            self.print_summary()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False
        
        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False
        
        finally:
            self.cleanup()

def parse_hex_value(value_str, name):
    """解析十六进制值"""
    try:
        if value_str.startswith('0x') or value_str.startswith('0X'):
            return int(value_str, 16)
        else:
            return int(value_str, 16)
    except ValueError:
        raise ValueError(f"无效的{name}: {value_str}")

def main():
    """主函数"""
    if len(sys.argv) < 4:
        print("用法: python3 10_03_FF_can_id_scan.py <开始CAN_ID> <结束CAN_ID> <周期CAN_ID> [UDS请求ID] [UDS响应ID] [接口]")
        print("示例: python3 10_03_FF_can_id_scan.py 0x700 0x7FF 0x3A1")
        print("      python3 10_03_FF_can_id_scan.py 700 7FF 3A1 715 795 can0")
        print("说明:")
        print("  开始CAN_ID: 必需，扫描的起始CAN ID")
        print("  结束CAN_ID: 必需，扫描的结束CAN ID")
        print("  周期CAN_ID: 必需，周期性出现的CAN ID (将被忽略)")
        print("  UDS请求ID:  可选，UDS请求CAN ID (默认为0x715)")
        print("  UDS响应ID:  可选，UDS响应CAN ID (默认为0x795)")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)
    
    try:
        # 解析参数
        start_id = parse_hex_value(sys.argv[1], "开始CAN ID")
        end_id = parse_hex_value(sys.argv[2], "结束CAN ID")
        periodic_id = parse_hex_value(sys.argv[3], "周期CAN ID")
        
        # 验证参数范围
        if start_id < 0 or start_id > 0x7FF:
            print(f"❌ 开始CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if end_id < 0 or end_id > 0x7FF:
            print(f"❌ 结束CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if periodic_id < 0 or periodic_id > 0x7FF:
            print(f"❌ 周期CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if start_id > end_id:
            print(f"❌ 开始CAN ID不能大于结束CAN ID")
            sys.exit(1)
        
        # 解析可选参数
        uds_request_id = None
        uds_response_id = None
        interface = 'can0'

        if len(sys.argv) > 4:
            # 检查第4个参数是否为接口名称
            if sys.argv[4].startswith('can'):
                interface = sys.argv[4]
            else:
                # 第4个参数是UDS请求ID
                uds_request_id = parse_hex_value(sys.argv[4], "UDS请求ID")

                if len(sys.argv) > 5:
                    # 第5个参数是UDS响应ID
                    if sys.argv[5].startswith('can'):
                        interface = sys.argv[5]
                    else:
                        uds_response_id = parse_hex_value(sys.argv[5], "UDS响应ID")

                        if len(sys.argv) > 6:
                            # 第6个参数是接口名称
                            interface = sys.argv[6]

    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)

    scanner = FFCANIDScanner(start_id, end_id, periodic_id, uds_request_id, uds_response_id, interface)
    
    try:
        success = scanner.run()
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
