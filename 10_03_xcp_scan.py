#!/usr/bin/env python3
"""
XCP连接扫描工具
向指定CAN ID范围发送XCP连接请求，
发现支持XCP协议的ECU并记录通信内容。
"""

import socket
import struct
import time
import sys
import os
import signal
from datetime import datetime
from collections import defaultdict

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class XCPScanner:
    """XCP连接扫描器"""
    
    def __init__(self, start_id, end_id, interface='can0'):
        self.start_id = int(start_id, 16) if isinstance(start_id, str) else start_id
        self.end_id = int(end_id, 16) if isinstance(end_id, str) else end_id
        self.interface = interface
        
        self.socket = None
        self.log_file = None
        self.running = True
        
        # 存储发现的XCP ECU
        self.discovered_ecus = {}  # {request_id: [response_records]}
        self.all_responses = []    # 所有响应记录
        
        # XCP协议常量
        self.XCP_CMD_CONNECT = 0xFF  # XCP连接命令
        self.XCP_RES_CONNECT = 0xFF  # XCP连接响应
        self.XCP_ERR_CODE = 0xFE     # XCP错误响应
        
        # XCP连接模式
        self.XCP_MODE_NORMAL = 0x00  # 正常模式
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.running = False
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            # 创建CAN socket
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            
            # 绑定到CAN接口
            self.socket.bind((self.interface,))
            
            # 设置接收超时
            self.socket.settimeout(0.1)
            
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_xcp_scan_{self.start_id:03X}_{self.end_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"XCP连接扫描日志 - {datetime.now()}\n")
            self.log_file.write(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}\n")
            self.log_file.write(f"CAN接口: {self.interface}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}", False)
            return False
    
    def receive_can_frames(self, timeout=0.5):
        """接收CAN帧（可能有多个响应）"""
        responses = []
        start_time = time.time()
        
        try:
            # 临时设置超时
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.05)  # 短超时，快速收集响应
            
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)
                    responses.append(frame)
                    self.log_message(f"接收: {frame}", False)
                except socket.timeout:
                    # 短暂超时是正常的，继续等待
                    continue
                except Exception as e:
                    self.log_message(f"接收失败: {e}", False)
                    break
            
            # 恢复原超时设置
            self.socket.settimeout(original_timeout)
            
        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
        
        return responses
    
    def is_xcp_connect_response(self, frame):
        """检查是否为XCP连接响应"""
        if frame is None or len(frame.data) < 1:
            return False

        # XCP正响应格式: FF (连接成功)
        if frame.data[0] == self.XCP_RES_CONNECT:
            # 进一步验证：XCP连接正响应通常包含资源信息
            if len(frame.data) >= 8:
                # 检查是否有合理的XCP连接响应格式
                # 第二字节通常是资源保护状态
                # 后续字节包含最大CTO/DTO长度等信息
                return True
            elif len(frame.data) >= 2:
                # 最小的XCP连接响应
                return True

        # XCP错误响应格式: FE + 错误代码
        if frame.data[0] == self.XCP_ERR_CODE and len(frame.data) >= 2:
            # 这是XCP错误响应，也算作XCP协议响应
            return True

        return False

    def is_valid_xcp_response(self, frame, request_id):
        """验证是否为有效的XCP响应"""
        if frame is None:
            return False

        # 检查响应ID是否合理
        # XCP响应ID通常是请求ID + 偏移量，或者是预定义的响应ID
        id_diff = frame.can_id - request_id

        # 常见的XCP ID映射模式
        valid_offsets = [0x8, 0x10, 0x20, 0x80, 0x100]  # 常见偏移量

        # 检查是否符合常见的ID映射模式
        if id_diff in valid_offsets:
            return self.is_xcp_connect_response(frame)

        # 检查是否为固定的XCP响应ID范围
        if 0x580 <= frame.can_id <= 0x5FF:  # CANopen XCP范围
            return self.is_xcp_connect_response(frame)

        if 0x700 <= frame.can_id <= 0x7FF:  # 高优先级范围
            return self.is_xcp_connect_response(frame)

        # 如果响应内容明确是XCP格式，也接受
        if self.is_xcp_connect_response(frame):
            return True

        return False
    
    def scan_xcp_connect(self, request_id):
        """扫描单个CAN ID的XCP连接"""
        # 创建XCP连接请求帧
        # XCP CONNECT命令格式: FF 00 (命令码 + 模式)
        xcp_connect_data = bytes([self.XCP_CMD_CONNECT, self.XCP_MODE_NORMAL])
        
        # 填充到8字节 (XCP通常使用8字节帧)
        padded_data = xcp_connect_data + b'\x00' * (8 - len(xcp_connect_data))
        
        request_frame = CANFrame(request_id, padded_data)
        
        # 发送XCP连接请求
        if not self.send_can_frame(request_frame):
            return []
        
        # 等待响应
        responses = self.receive_can_frames(0.5)
        
        # 过滤真正的XCP响应
        xcp_responses = []
        for response in responses:
            if response.can_id != request_id:  # 不是自己发送的帧
                if self.is_valid_xcp_response(response, request_id):
                    xcp_responses.append(response)
                else:
                    # 记录但不认为是XCP响应
                    self.log_message(f"忽略非XCP响应: ID=0x{response.can_id:03X}, 数据={' '.join(f'{b:02X}' for b in response.data)}", False)

        return xcp_responses
    
    def scan_range(self):
        """扫描CAN ID范围"""
        print(f"\n开始XCP连接扫描: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print("XCP连接命令: FF 00 00 00 00 00 00 00")
        print("=" * 60)
        
        total_ids = self.end_id - self.start_id + 1
        scanned = 0
        found_ecus = 0
        
        for can_id in range(self.start_id, self.end_id + 1):
            if not self.running:
                break
            
            scanned += 1
            progress = (scanned / total_ids) * 100
            
            # 每50个ID显示一次进度
            if scanned % 50 == 0 or scanned == 1:
                print(f"\r扫描进度: {progress:5.1f}% (0x{can_id:03X})", end='', flush=True)
            
            responses = self.scan_xcp_connect(can_id)
            
            if responses:
                found_ecus += 1
                print()  # 换行
                
                # 记录发现的XCP ECU
                self.discovered_ecus[can_id] = []
                
                for response in responses:
                    response_record = {
                        'request_id': can_id,
                        'response_id': response.can_id,
                        'request_data': bytes([self.XCP_CMD_CONNECT, self.XCP_MODE_NORMAL]),
                        'response_data': response.data,
                        'timestamp': datetime.now(),
                        'is_xcp_response': self.is_xcp_connect_response(response),
                        'id_offset': response.can_id - can_id
                    }
                    
                    self.discovered_ecus[can_id].append(response_record)
                    self.all_responses.append(response_record)
                
                # 显示发现的XCP ECU
                msg = f"✅ 发现XCP ECU: 请求ID=0x{can_id:03X}, 响应数={len(responses)}"
                self.log_message(msg)
                
                for i, response in enumerate(responses, 1):
                    is_xcp = self.is_xcp_connect_response(response)
                    xcp_status = "XCP连接成功" if is_xcp else "可能的XCP响应"
                    detail_msg = f"  响应{i}: ID=0x{response.can_id:03X}, 数据={' '.join(f'{b:02X}' for b in response.data)}, 状态={xcp_status}"
                    self.log_message(detail_msg, False)
                
                self.log_message("", False)  # 空行
            
            # 短暂延时避免总线拥塞
            time.sleep(0.02)
        
        print()  # 最终换行
        print(f"\n扫描完成: 发现 {found_ecus} 个可能的XCP ECU")
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "=" * 60)
        print("XCP连接扫描结果摘要")
        print("=" * 60)
        
        if not self.discovered_ecus:
            print("未发现任何XCP ECU")
            return
        
        print(f"发现 {len(self.discovered_ecus)} 个可能的XCP ECU:")
        print()
        
        ecu_num = 1
        for request_id, responses in self.discovered_ecus.items():
            print(f"{ecu_num}. XCP ECU #{ecu_num}")
            print(f"   请求CAN ID: 0x{request_id:03X}")
            print(f"   XCP连接命令: FF 00 00 00 00 00 00 00")
            print(f"   响应数量:   {len(responses)}")
            
            for i, record in enumerate(responses, 1):
                xcp_status = "✅ XCP连接成功" if record['is_xcp_response'] else "❓ 可能的XCP响应"
                print(f"   响应{i}:")
                print(f"     响应ID:   0x{record['response_id']:03X}")
                print(f"     响应数据: {' '.join(f'{b:02X}' for b in record['response_data'])}")
                print(f"     状态:     {xcp_status}")
                print(f"     时间戳:   {record['timestamp'].strftime('%H:%M:%S.%f')[:-3]}")
            
            print()
            ecu_num += 1
        
        # 统计分析
        print("统计分析:")
        print("=" * 20)
        
        # 按响应ID分组
        response_id_count = defaultdict(int)
        xcp_success_count = 0
        
        for record in self.all_responses:
            response_id_count[record['response_id']] += 1
            if record['is_xcp_response']:
                xcp_success_count += 1
        
        print("响应ID统计:")
        for response_id, count in sorted(response_id_count.items()):
            print(f"  0x{response_id:03X}: {count} 次响应")
        
        print(f"\nXCP连接成功: {xcp_success_count} 次")
        print(f"其他响应: {len(self.all_responses) - xcp_success_count} 次")
        
        # 记录到日志文件
        self.log_message("\nXCP扫描结果摘要:", False)
        self.log_message("=" * 20, False)
        self.log_message(f"发现 {len(self.discovered_ecus)} 个可能的XCP ECU", False)
        
        for request_id, responses in self.discovered_ecus.items():
            self.log_message(f"XCP ECU: 0x{request_id:03X} -> {len(responses)} 个响应", False)
            for record in responses:
                status = "XCP成功" if record['is_xcp_response'] else "其他响应"
                self.log_message(f"  -> 0x{record['response_id']:03X}: {' '.join(f'{b:02X}' for b in record['response_data'])} ({status})", False)
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行扫描"""
        print("XCP连接扫描工具")
        print("=" * 16)
        print(f"接口: {self.interface}")
        print(f"扫描范围: 0x{self.start_id:03X} - 0x{self.end_id:03X}")
        print("XCP连接命令: FF 00 (CONNECT + NORMAL模式)")
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行扫描
            self.scan_range()
            
            # 打印结果
            self.print_summary()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断扫描")
            return False
        
        except Exception as e:
            print(f"\n扫描过程中发生错误: {e}")
            return False
        
        finally:
            self.cleanup()

def parse_hex_value(value_str, name):
    """解析十六进制值"""
    try:
        if value_str.startswith('0x') or value_str.startswith('0X'):
            return int(value_str, 16)
        else:
            return int(value_str, 16)
    except ValueError:
        raise ValueError(f"无效的{name}: {value_str}")

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python3 10_03_xcp_scan.py <开始CAN_ID> <结束CAN_ID> [接口]")
        print("示例: python3 10_03_xcp_scan.py 0x700 0x7FF")
        print("      python3 10_03_xcp_scan.py 700 7FF can0")
        print("说明:")
        print("  开始CAN_ID: 必需，扫描的起始CAN ID")
        print("  结束CAN_ID: 必需，扫描的结束CAN ID")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)
    
    try:
        # 解析参数
        start_id = parse_hex_value(sys.argv[1], "开始CAN ID")
        end_id = parse_hex_value(sys.argv[2], "结束CAN ID")
        
        # 验证参数范围
        if start_id < 0 or start_id > 0x7FF:
            print(f"❌ 开始CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if end_id < 0 or end_id > 0x7FF:
            print(f"❌ 结束CAN ID必须在0x000-0x7FF范围内")
            sys.exit(1)
        
        if start_id > end_id:
            print(f"❌ 开始CAN ID不能大于结束CAN ID")
            sys.exit(1)
        
        # 获取接口名称
        interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'
        
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    scanner = XCPScanner(start_id, end_id, interface)
    
    try:
        success = scanner.run()
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
